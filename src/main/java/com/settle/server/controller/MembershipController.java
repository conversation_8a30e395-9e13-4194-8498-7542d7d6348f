package com.settle.server.controller;

import com.settle.server.service.MembershipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/Member")
@Api(tags = "成员权益领取文件")
public class MembershipController {

    @Autowired
    private MembershipService membershipService;

    @GetMapping(value = "/save")
    @ApiOperation(value = "成员权益领取文件", notes = "成员权益领取文件")
    public String save(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            membershipService.save(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("IotSettController --->save error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }

}
