<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.mnp.StlBaseinfoLineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.mnp.StlBaseinfoLine">
        <result column="POSPECNUMBER" property="pospecnumber" />
        <result column="POSPECNAME" property="pospecname" />
        <result column="SOSPECNUMBER" property="sospecnumber" />
        <result column="SOSPECNAME" property="sospecname" />
        <result column="SOID" property="soid" />
        <result column="CUSTOMERNUMBER" property="customernumber" />
        <result column="START_PROV_NM" property="startProvNm" />
        <result column="START_PROV" property="startProv" />
        <result column="START_CITY" property="startCity" />
        <result column="MANAGER_NM" property="managerNm" />
        <result column="MANAGER_CON" property="managerCon" />
        <result column="A_PROV_NM" property="aProvNm" />
        <result column="A_PROV" property="aProv" />
        <result column="A_CITY" property="aCity" />
        <result column="A_DISTR" property="aDistr" />
        <result column="A_CP_NM" property="aCpNm" />
        <result column="A_CP_CON" property="aCpCon" />
        <result column="Z_PROV_NM" property="zProvNm" />
        <result column="Z_PROV" property="zProv" />
        <result column="Z_CITY" property="zCity" />
        <result column="Z_DISTR" property="zDistr" />
        <result column="Z_CP_NM" property="zCpNm" />
        <result column="Z_CP_CON" property="zCpCon" />
        <result column="BANDWIDTH" property="bandwidth" />
        <result column="A_FUNC_RATE" property="aFuncRate" />
        <result column="A_SETUP_RATE" property="aSetupRate" />
        <result column="Z_FUNC_RATE" property="zFuncRate" />
        <result column="Z_SETUP_RATE" property="zSetupRate" />
        <result column="A_ADDRESS" property="aAddress" />
        <result column="CUSTOMERNAME" property="customername" />
        <result column="START_FUNC_RATE" property="startFuncRate" />
        <result column="START_SETUP_RATE" property="startSetupRate" />
        <result column="Z_ADDRESS" property="zAddress" />
        <result column="SETTLEMONTH" property="settlemonth" />
    </resultMap>
    <insert id="insertBatch">
        insert into stl_baseinfo_line
        (POSPECNUMBER, POSPECNAME, SOSPECNUMBER, SOSPECNAME, SOID, CUSTOMERNUMBER, START_PROV_NM,START_PROV,
        START_CITY,MANAGER_NM,MANAGER_CON, A_PROV_NM, A_PROV, A_CITY, A_DISTR, A_CP_NM, A_CP_CON, Z_PROV_NM, Z_PROV,
        Z_CITY, Z_DISTR, Z_CP_NM, Z_CP_CON, BANDWIDTH, A_FUNC_RATE, A_SETUP_RATE, Z_FUNC_RATE, Z_SETUP_RATE,
        A_ADDRESS, CUSTOMERNAME, START_FUNC_RATE, START_SETUP_RATE, Z_ADDRESS, SETTLEMONTH)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.pospecnumber},
            #{item.pospecname},
            #{item.sospecnumber},
            #{item.sospecname},
            #{item.soid},
            #{item.customernumber},
            #{item.startProvNm},
            #{item.startProv},
            #{item.startCity},
            #{item.managerNm},
            #{item.managerCon},
            #{item.aProvNm},
            #{item.aProv},
            #{item.aCity},
            #{item.aDistr},
            #{item.aCpNm},
            #{item.aCpCon},
            #{item.zProvNm},
            #{item.zProv},
            #{item.zCity},
            #{item.zDistr},
            #{item.zCpNm},
            #{item.zCpCon},
            #{item.bandwidth},
            #{item.aFuncRate},
            #{item.aSetupRate},
            #{item.zFuncRate},
            #{item.zSetupRate},
            #{item.aAddress},
            #{item.customername},
            #{item.startFuncRate},
            #{item.startSetupRate},
            #{item.zAddress},
            #{item.settlemonth}
            )
        </foreach>
    </insert>
    <delete id="deleteHis" parameterType="string">
        delete from stl_baseinfo_line_his where settlemonth = #{acctMonth}
    </delete>

</mapper>
