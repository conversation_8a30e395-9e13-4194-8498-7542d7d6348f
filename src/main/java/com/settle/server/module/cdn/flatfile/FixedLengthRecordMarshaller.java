package com.settle.server.module.cdn.flatfile;


import com.settle.server.module.cdn.field.BeanFieldExtractor;
import com.settle.server.module.cdn.field.FieldExtractor;
import com.settle.server.module.cdn.marshaller.RecordMarshaller;
import com.settle.server.module.cdn.record.Header;
import com.settle.server.module.cdn.record.Record;
import com.settle.server.module.cdn.record.StringRecord;
import com.settle.server.utils.Utils;

import java.util.Locale;
import java.util.stream.StreamSupport;
/**
 * 将 POJO 编组为固定长度格式
 * 此编组器可用于将带有空格的字段左右填充到固定长度。
 * 但是，如果字段值长于指定的字段长度，它不会截断数据。
 *
 * @param <P> 记录有效负载的类型
 * <AUTHOR>
 */
public class FixedLengthRecordMarshaller<P> implements RecordMarshaller<P, String> {

    private FieldExtractor<P> fieldExtractor;
    private String format;
    private Locale locale = Locale.getDefault();

    /**
     * 创建一个新的 {@link FixedLengthRecordMarshaller}
     * 此构造函数将创建一个类型的字段提取器 {@link BeanFieldExtractor}
     *
     * @param type   要编组的对象类型
     * @param format 使用（在 {@link java.util.Formatter} 语法中）
     * @param fields 以正确的顺序编组
     */
    public FixedLengthRecordMarshaller(final Class<P> type, String format, final String... fields) {
        this(new BeanFieldExtractor<>(type, fields), format);
    }

    /**
     * 创建一个新的 {@link FixedLengthRecordMarshaller}
     *
     * @param fieldExtractor 用于提取字段
     * @param format         使用（在 {@link java.util.Formatter} 语法中）
     */
    public FixedLengthRecordMarshaller(FieldExtractor<P> fieldExtractor, String format) {
        Utils.checkNotNull(fieldExtractor, "field extractor");
        Utils.checkNotNull(format, "format");
        this.fieldExtractor = fieldExtractor;
        this.format = format;
    }

    @Override
    public StringRecord processRecord(final Record<P> record) throws Exception {
        Header header = record.getHeader();
        Object[] fields = toArray(fieldExtractor.extractFields(record.getPayload()));
        String payload = String.format(locale, format, fields);
        return new StringRecord(header, payload);
    }

    /**
     * 设置用于格式化记录的语言环境
     *
     * @param locale 用于格式化记录
     */
    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    private Object[] toArray(Iterable<Object> iterable) {
        return StreamSupport.stream(iterable.spliterator(), false).toArray(Object[]::new);
    }
}
