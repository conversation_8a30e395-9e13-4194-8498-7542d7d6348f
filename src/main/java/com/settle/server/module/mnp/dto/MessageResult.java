package com.settle.server.module.mnp.dto;

import lombok.Data;

@Data
public class MessageResult {
    public static final String FAIL_001 = "F001";
    public static final String FAIL_002 = "F002";
    public static final String FAIL_999 = "F999";

    private String msgCode;
    private String msg;



    private MessageResult(String msgCode, String msg) {
        this.msgCode = msgCode;
        this.msg = msg;
    }

    public static MessageResult success() {
        return new MessageResult("0000", "OK");
    }

    public boolean isSuccess() {
        return  "0000".equals(this.msgCode);
    }
    public static MessageResult error(String code,String msg) {
        return new MessageResult(code, msg);
    }
}

