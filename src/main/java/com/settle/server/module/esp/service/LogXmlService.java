package com.settle.server.module.esp.service;

import cn.hutool.db.DbUtil;
import com.settle.server.module.esp.dto.LogXml;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/16
 * @since 1.0.0
 */
@Service
@Slf4j
public class LogXmlService {

    @Autowired
    @Qualifier(value = "stludrDataSource")
    private DataSource stludrDataSource;

    private final String INSERT_LOG_XML = "insert into LOG_X2T_T (IOID_ID0 , CREATED_TIME, MOD_TIME, BIZ_TYPE, FILE_NAME, DIRECTION, RAW_COUNT, ERR_CODE, ERR_MESSAGE, FILE_BYTE) values(SEQ_IOID_ID.nextval, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    public void saveLogXml(LogXml logXml) {
        Connection connection = null;
        PreparedStatement pstmt = null;
        try {
            connection = stludrDataSource.getConnection();
            pstmt = connection.prepareStatement(INSERT_LOG_XML);
            Date date = new Date();
            pstmt.setTimestamp(1, new java.sql.Timestamp(date.getTime()));
            pstmt.setTimestamp(2, new java.sql.Timestamp(date.getTime()));
            pstmt.setString(3, logXml.getBizType());
            pstmt.setString(4, logXml.getFileName());
            pstmt.setInt(5, logXml.getDirection());
            pstmt.setLong(6, logXml.getRowCount());
            pstmt.setString(7, logXml.getErrCode());
            pstmt.setString(8, logXml.getErrMessage());
            pstmt.setLong(9, logXml.getFileSize());
            pstmt.executeUpdate();
            log.info("save LOG_X2T_T success,{}", logXml.getFileName());
        }catch (Exception e) {
            log.error("save LOG_X2T_T error",e);
        }finally {
            DbUtil.close(connection,pstmt);
        }

    }
}