<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="RecIncome">
		<xs:annotation>
			<xs:documentation>Comment describing your root element</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="BillMonth">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="6"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="RecIncomeList">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="RecIncomeInfo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="ProvCode">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:length value="3"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="DirectRecAmount">
											<xs:simpleType>
												<xs:restriction base="xs:integer">
													<xs:totalDigits value="16"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="JointRecAmount">
											<xs:simpleType>
												<xs:restriction base="xs:integer">
													<xs:totalDigits value="16"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
