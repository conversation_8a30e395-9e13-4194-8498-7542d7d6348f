<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.settle.server.dao.stludr.UdmDataFor45053Dao">

    <update id="deleteFor45053">
        truncate table stludr.udm_data_for_45053_item
    </update>

    <update id="deleteCustomerInfo">
        truncate table stludr.customer_45053
    </update>
    <insert id="insertIntoCustomerInfo">
        insert into stludr.customer_45053(EC_CODE) select distinct  EC_CODE from  stludr.ur_maapmma_${acctMonth}_t a
           where a.offer_code = '50034' and product_code = '5003401' and a.cerca_mark = '1'
    </insert>

    <update id="updateCustomerInfoLocationName">
        UPDATE stludr.customer_45053 ut
            JOIN stlusers.stl_customer c ON ut.EC_CODE = c.customer_code
            JOIN stludr.abs_location t ON c.location = t.location_number
            SET ut.LOCATION_NAME = t.location_name;
    </update>

    <update id="deleteTempMaapmmaInfo">
        truncate table stludr.temp_maapmma_45053
    </update>

    <insert id="insertTempMaapmmaInfo">
        insert into stludr.temp_maapmma_45053 (ec_code_prov,manufacturer_code,location,charge_out,total,RATEPLAN_ID)
        SELECT a.ec_code_prov,
               a.manufacturer_code,
               cust.LOCATION_NAME,
               sum(decode(a.rate_back_id, '1', 54, a.bill_charge)) as charge_out,
               count(a.cerca_mark) as total
        FROM stludr.UR_MAAPMMA_${acctMonth}_T a
                 JOIN stludr.customer_45053 cust ON a.EC_CODE = cust.EC_CODE
        WHERE a.offer_code = '50034'
          AND a.product_code = '5003401'
          AND a.cerca_mark = '1'
        GROUP BY a.ec_code_prov, a.manufacturer_code, cust.LOCATION_NAME,a.RATEPLAN_ID
    </insert>

    <insert id="insertDataAll" parameterType="java.lang.String">
        <![CDATA[ INSERT INTO stludr.udm_data_for_45053_item (EC_CODE_PROV,TERMINAL_NAME,CHARGE_OUT,TOTAL,EVALUATIONSCORE,INCENTIVECOEFFICIENT,LOCATION_NAME)
                  SELECT *
                  FROM (SELECT t.EC_PROV ec_code,
                               f.terminal_name,
                               sum(t.bg_fee) ,
                               sum( t.total_count)      total_count,
                               f.evaluationscore             evaluation_score,
                               f.incentivecoefficient        incentive_coefficient,
                               c.LOCATION_NAME                    location_name
                        FROM stludr.maapnma_terminal_prod_total t
                                 JOIN stludr.stl_5g_terminal_source f ON t.manufacturer_code = f.terminal_brand
                                 JOIN stludr.customer_45053 c ON c.EC_CODE = t.ec_code
                        where t.acct_Month= ${acctMonth} and f.acct_Month= ${acctMonth}
                        group by EC_PROV,terminal_name,evaluationscore,incentivecoefficient,LOCATION_NAME
                        )
        ]]>
    </insert>


</mapper>
           
