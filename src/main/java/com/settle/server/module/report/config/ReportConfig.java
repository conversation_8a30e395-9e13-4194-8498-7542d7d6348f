package com.settle.server.module.report.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/9
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "report.config")
@NacosConfigurationProperties(dataId = "settle-service-tools", autoRefreshed = true)
public class ReportConfig {

    /**
     * 报表服务远程地址
     */
    private String remoteUrl;

    /**
     * 超时时间,默认60s
     */
    private Integer timeout=60000;

    //erp报表服务地址
    private String erpUri;

    //暂估/销暂估报表服务地址
    private String predictionUri;

    //清退报表服务地址
    private String evictUri;

    private String erpFilePath="/home/<USER>/SettleFile/ERP_Files";

}