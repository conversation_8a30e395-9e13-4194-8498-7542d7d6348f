<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.mnp.StlBaseinfoVpnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.mnp.StlBaseinfoVpn">
        <result column="POSPECNUMBER" property="pospecnumber" />
        <result column="POSPECNAME" property="pospecname" />
        <result column="SOSPECNUMBER" property="sospecnumber" />
        <result column="SOSPECNAME" property="sospecname" />
        <result column="SOID" property="soid" />
        <result column="CUSTOMERNUMBER" property="customernumber" />
        <result column="START_PROV_NM" property="startProvNm" />
        <result column="START_PROV" property="startProv" />
        <result column="START_CITY" property="startCity" />
        <result column="MANAGER_NM" property="managerNm" />
        <result column="MANAGER_CON" property="managerCon" />
        <result column="END_PROV_NM" property="endProvNm" />
        <result column="END_PROV" property="endProv" />
        <result column="END_CITY" property="endCity" />
        <result column="END_DISTR" property="endDistr" />
        <result column="CP_NM" property="cpNm" />
        <result column="CP_CON" property="cpCon" />
        <result column="BANDWIDTH" property="bandwidth" />
        <result column="END_ELE_RENT_RATE" property="endEleRentRate" />
        <result column="END_PE_RENT_RATE" property="endPeRentRate" />
        <result column="END_SERV_RENT_RATE" property="endServRentRate" />
        <result column="END_SERV_ONCE_RATE" property="endServOnceRate" />
        <result column="ADDRESS" property="address" />
        <result column="CUSTOMERNAME" property="customername" />
        <result column="START_ELE_RENT_RATE" property="startEleRentRate" />
        <result column="START_PE_RENT_RATE" property="startPeRentRate" />
        <result column="START_SERV_RENT_RATE" property="startServRentRate" />
        <result column="START_SERV_ONCE_RATE" property="startServOnceRate" />
        <result column="END_SETUP_RATE" property="endSetupRate" />
        <result column="START_SETUP_RATE" property="startSetupRate" />
        <result column="END_RENT_RATE" property="endRentRate" />
        <result column="START_RENT_RATE" property="startRentRate" />
        <result column="SETTLEMONTH" property="settlemonth" />
    </resultMap>
    <insert id="insertBatch">
        insert into stl_baseinfo_vpn
        (POSPECNUMBER, POSPECNAME, SOSPECNUMBER, SOSPECNAME, SOID, CUSTOMERNUMBER, START_PROV_NM, START_PROV, START_CITY, MANAGER_NM, MANAGER_CON, END_PROV_NM, END_PROV, END_CITY, END_DISTR, CP_NM, CP_CON, BANDWIDTH, END_ELE_RENT_RATE, END_PE_RENT_RATE, END_SERV_RENT_RATE, END_SERV_ONCE_RATE, ADDRESS, CUSTOMERNAME, START_ELE_RENT_RATE, START_PE_RENT_RATE, START_SERV_RENT_RATE, START_SERV_ONCE_RATE, END_SETUP_RATE, START_SETUP_RATE, END_RENT_RATE, START_RENT_RATE, SETTLEMONTH)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.pospecnumber},
            #{item.pospecname},
            #{item.sospecnumber},
            #{item.sospecname},
            #{item.soid},
            #{item.customernumber},
            #{item.startProvNm},
            #{item.startProv},
            #{item.startCity},
            #{item.managerNm},
            #{item.managerCon},
            #{item.endProvNm},
            #{item.endProv},
            #{item.endCity},
            #{item.endDistr},
            #{item.cpNm},
            #{item.cpCon},
            #{item.bandwidth},
            #{item.endEleRentRate},
            #{item.endPeRentRate},
            #{item.endServRentRate},
            #{item.endServOnceRate},
            #{item.address},
            #{item.customername},
            #{item.startEleRentRate},
            #{item.startPeRentRate},
            #{item.startServRentRate},
            #{item.startServOnceRate},
            #{item.endSetupRate},
            #{item.startSetupRate},
            #{item.endRentRate},
            #{item.startRentRate},
            #{item.settlemonth}
            )
        </foreach>
    </insert>
    <delete id="deleteHis" parameterType="string">
        delete from stl_baseinfo_vpn_his where settlemonth = #{acctMonth}
    </delete>

</mapper>
