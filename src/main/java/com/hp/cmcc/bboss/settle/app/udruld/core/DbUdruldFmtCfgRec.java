package com.hp.cmcc.bboss.settle.app.udruld.core;

import com.hp.cmcc.bboss.settle.bo.GsonObj;
import com.hp.cmcc.bboss.settle.bo.UdrFmt;
import com.hp.cmcc.bboss.settle.dto.OdbSystemParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DbUdruldFmtCfgRec extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(DbUdruldFmtCfgRec.class);
	public static final String SRC_TYPE_U = "U";
	public static final String SRC_TYPE_E = "E";
	public static final String SRC_TYPE_A = "A";

	private Integer type_id;
	private String biz_type;
	private String aux_key;
	private Integer uld_idx;
	private String uld_mimoni;
	private Integer is_num;
	private Integer max_len;
	private String dft_val;
	private String src_type;
	private Integer src_idx;
	private String eval;

	public boolean validateRec() {
		boolean rc_ = true;
		if (SRC_TYPE_U.equals(src_type)) {
			if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
				if (src_idx >= UdrFmt.U_FIELD_CNT_57) {
					L.warn("[{},{}], for SRC_TYPE [{}], SRC_IDX {} should lt {}",
							new Object[] { type_id, uld_idx, src_type, src_idx, UdrFmt.U_FIELD_CNT_57 });
					rc_ = false;
				}
			} else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
				if (src_idx >= UdrFmt.S_FIELD_CNT_40) {
					L.warn("[{},{}], for SRC_TYPE [{}], SRC_IDX {} should lt {}",
							new Object[] { type_id, uld_idx, src_type, src_idx, UdrFmt.S_FIELD_CNT_40 });
					rc_ = false;
				}
			} else {
				L.warn("ukn SUB_SYSTEM_TYPE {}", OdbSystemParam.GetInstance()._subSystemType);
				rc_ = false;
			}
		} else if (SRC_TYPE_E.equals(src_type)) {
			if (src_idx >= UdrFmt.E_FIELD_CNT_33) {
				L.warn("[{},{}], for SRC_TYPE [{}], SRC_IDX {} should lt {}",
						new Object[] { type_id, uld_idx, src_type, src_idx, UdrFmt.E_FIELD_CNT_33 });
				rc_ = false;
			}
		} else if (!SRC_TYPE_A.equals(src_type)) {
			L.warn("[{},{}], SRC_TYPE [{}] not supported yet", type_id, uld_idx, src_type);
			rc_ = false;
		}

		if (is_num == 1) {
			if (max_len >= 21) {
				L.warn("[{},{}], for IS_NUM [{}], MAX_LEN should lt {}", type_id, uld_idx, is_num, 21);
				rc_ = false;
			}
		}
		return rc_;
	}

	public String getScriptKey() {
		return String.format("%03d:%s:%d", type_id, aux_key, uld_idx);
	}

	public Integer getType_id() {
		return type_id;
	}

	public void setType_id(Integer type_id) {
		this.type_id = type_id;
	}

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public String getAux_key() {
		return aux_key;
	}

	public void setAux_key(String aux_key) {
		this.aux_key = aux_key;
	}

	public Integer getUld_idx() {
		return uld_idx;
	}

	public void setUld_idx(Integer uld_idx) {
		this.uld_idx = uld_idx;
	}

	public String getUld_mimoni() {
		return uld_mimoni;
	}

	public void setUld_mimoni(String uld_mimoni) {
		this.uld_mimoni = uld_mimoni;
	}

	public Integer getIs_num() {
		return is_num;
	}

	public void setIs_num(Integer is_num) {
		this.is_num = is_num;
	}

	public Integer getMax_len() {
		return max_len;
	}

	public void setMax_len(Integer max_len) {
		this.max_len = max_len;
	}

	public String getDft_val() {
		return dft_val;
	}

	public void setDft_val(String dft_val) {
		this.dft_val = dft_val;
	}

	public String getSrc_type() {
		return src_type;
	}

	public void setSrc_type(String src_type) {
		this.src_type = src_type;
	}

	public Integer getSrc_idx() {
		return src_idx;
	}

	public void setSrc_idx(Integer src_idx) {
		this.src_idx = src_idx;
	}

	public String getEval() {
		return eval;
	}

	public void setEval(String eval) {
		this.eval = eval;
	}
}
