<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.SyncInterfaceMcP2PDao">

    <resultMap type="com.settle.server.entity.SyncInterfaceMcP2P" id="SyncInterfaceMcP2PMap">
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="settlementPartyIn" column="SETTLEMENT_PARTY_IN" jdbcType="VARCHAR"/>
        <result property="settlementPartyOut" column="SETTLEMENT_PARTY_OUT" jdbcType="VARCHAR"/>
        <result property="settlementRate" column="SETTLEMENT_RATE" jdbcType="VARCHAR"/>
        <result property="settlementType" column="SETTLEMENT_TYPE" jdbcType="VARCHAR"/>
        <result property="settlementAmount" column="SETTLEMENT_AMOUNT" jdbcType="VARCHAR"/>
        <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="queryAllByCondition" resultMap="SyncInterfaceMcP2PMap">
        select ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,FILE_NAME,STATUS
            from SYNC_INTERFACE_MC_P2P_${acctMonth}
        <where>
            <if test="id != null and id != ''">
                and ID = #{id}
            </if>
            <if test="provCode != null and provCode != ''">
                and PROV_CODE = #{provCode}
            </if>
        </where>
    </select>

    <insert id="insert"  useGeneratedKeys="false" parameterType="com.settle.server.entity.SyncInterfaceMcP2P">
        insert into SYNC_INTERFACE_MC_P2P_${acctMonth}(ID, SETTLEMENT_PARTY_IN, SETTLEMENT_PARTY_OUT, SETTLEMENT_RATE,
                                                   SETTLEMENT_TYPE, SETTLEMENT_AMOUNT, FILE_NAME, STATUS)
        values (#{id}, #{settlementPartyIn}, #{settlementPartyOut}, #{settlementRate}, #{settlementType},
                #{settlementAmount}, #{fileName}, #{status})
    </insert>

    <insert id="insertBatch"  useGeneratedKeys="false" parameterType="com.settle.server.entity.SyncInterfaceMcP2P">
        insert into SYNC_INTERFACE_MC_P2P_${acctMonth}(ID, SETTLEMENT_PARTY_IN, SETTLEMENT_PARTY_OUT, SETTLEMENT_RATE,
                                                       SETTLEMENT_TYPE, SETTLEMENT_AMOUNT, FILE_NAME, STATUS)
        values
        <foreach collection="syncInterfaceMcP2PLst" item="item" index="index" separator=",">
            (#{item.id}, #{item.settlementPartyIn}, #{item.settlementPartyOut}, #{item.settlementRate},
            #{item.settlementType}, #{item.settlementAmount}, #{item.fileName}, #{item.status})
        </foreach>
    </insert>

    <update id="updateStausByAcctMonth">
        update stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
        set status='23'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '23')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStatusByFileNameAndId">
        update stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
        set status='24'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '24')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStatusByFileName">
        update stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
        set status='25'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '25')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStatusByMcStatus">
        update stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
        set status='99'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '99')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStausByStatus">
        update stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
        set status='0'
        where status is null
        and file_name = #{fileName}
    </update>

    <delete id="deleteByFileNameMonth" parameterType="java.lang.String">
        delete from stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
        <where>
            <if test="fileName != null and fileName != ''">
                AND file_name = #{fileName, jdbcType = VARCHAR}
            </if>
        </where>
    </delete>
</mapper>