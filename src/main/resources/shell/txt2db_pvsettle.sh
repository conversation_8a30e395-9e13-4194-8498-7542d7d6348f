#!/bin/bash
export TXT2DB_HOME=/home/<USER>/workspace/txt2db
export BIN_PATH=${TXT2DB_HOME}/bin
export DATA_PATH=${TXT2DB_HOME}/data
export BACKUP_PATH=${TXT2DB_HOME}/backupData
export ERROR_PATH=${TXT2DB_HOME}/error
export CONF_PATH=${TXT2DB_HOME}/conf
export TABLE_PATH=${TXT2DB_HOME}/conf/table
export RESP_PATH=${TXT2DB_HOME}/resp
export LOG_PATH=${TXT2DB_HOME}/log
export WORK_PATH=${TXT2DB_HOME}/work

####4.30
log_file=${LOG_PATH}/txt2db_pvsettle_`date +%Y%m%d`.log
echo $log_file
response_file=${WORK_PATH}/txt2db_pvsettle_response.txt

function f_Print
{
    cur_ts=`date +%Y%m%d%H%M%S`
    echo "${cur_ts} - $1" >> $log_file
}

function f_init_config
{
    f_Print "f_init_config starts"

    if [ ! -f "${CONF_PATH}/txt2db_pvsettle.conf" ]; then
        f_Print "f_init_config - config file not found - ${CONF_PATH}/txt2db_pvsettle.conf"
        exit
    fi 

    . ${CONF_PATH}/txt2db_pvsettle.conf

    f_Print "f_init_config finishes"
}  ## f_init_config

function f_truncate_table
{

    f_Print "f_truncate_table starts"

    echo "truncate table stludr.SYNC_INTERFACE_PVS_${acct_month};
    truncate table stludr.SYNC_INTERFACE_PVS_TOC_${acct_month}; " | mysql -u$db_user -p$db_passwd -h$db_host -P$db_port
    
    f_Print "f_truncate_table finishes"
    
}  ## f_truncate_table

function f_load_data
{

    f_Print "f_load_data starts"

    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_PVS_tmpl.ctl  > ${CONF_PATH}/SYNC_INTERFACE_PVS.ctl
    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_PVS_TOC_tmpl.ctl  > ${CONF_PATH}/SYNC_INTERFACE_PVS_TOC.ctl

    for filename in `find ${DATA_PATH} -type f -name "${naming_convention}"`
    do
        filename_only=`basename $filename`
        f_Print "f_load_data - ${filename_only} - Starting loading file"

        tr -d '\r' < $filename > ${WORK_PATH}/${filename_only}.mod

        grep 'NoneToC!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $0"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.tmp > ${WORK_PATH}/${filename_only}

	grep 'IsToC!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.TOC.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $0"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.TOC.tmp > ${WORK_PATH}/${filename_only}.TOC

        cd ${WORK_PATH}

        cp ${WORK_PATH}/${filename_only} ${WORK_PATH}/SYNC_INTERFACE_PVS.dat
        ####sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_PVS.ctl log=${LOG_PATH}/sqlldr_log_${filename_only} bad=${WORK_PATH}/bad.${filename_only}

        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_PVS.dat' into table stludr.SYNC_INTERFACE_PVS_${acct_month}  fields terminated by '!!!!' lines terminated by '\n' (TAG,PROV_CODE,IS_TO_C,CUSTOMER_PROVINCE_NUMBER,CUSTOMER_NAME,PRODUCT_SUBS_ID,PRODUCT_ID,PRODUCT_NAME,DB_PRODUCT_ID,DB_PRODUCT_NAME,CO_PRODUCT_ID,BL_PRODUCT_ID,ONE_PRODUCT_ID,BILLING_TERM,PAY_TERM,PROD_CHARGE_CODE,PO_CHARGE_CODE_NAME,ONE_PRO_CHARGE_CODE,ONE_PRO_CHARGE_NAME,DB_PROD_CHARGE_CODE,FEE_VAL,TAX_RATE,TAX,FEE_NO_TAX,FEE_FLAG,DISCOUNT_AMOUNT,STANDARD_FEE,SETTLE_FEE,GH_FEE_TYPE,RATEPLAN_ID,RATEPLAN_NAME,STANDARD_SALE_PRICE,SETTLE_PRICE,ORIGINAL_BILL_MONTH,FEE_SEQ,SALES_BASE_DISCOUNT,PV_SETTLE_RATE,PV_SETTLE_VALUE,ON_PRODUCT_CODE,ON_PRODUCT_NAME,EJ_PRODUCT_CODE,EJ_PRODUCT_NAME,SJ_PRODUCT_CODE,SJ_PRODUCT_NAME,GH_CODE,PRODUCT_CLASS,PRODUCT_REPORT_NAME,PRODUCT_REPORT_ITEM_NAME,ISSUE_TIME,EXPIRE_TIME,PRODUCT_TYPE,BUSI_TYPE,CONTRACT_MAIN,PV_PRODUCT_CLASS,CITY_CODE,CREATOR_NAME,EC_ID,FILE_NAME);" 

        cp ${WORK_PATH}/${filename_only}.TOC ${WORK_PATH}/SYNC_INTERFACE_PVS_TOC.dat
        ####sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_PVS_TOC.ctl log=${LOG_PATH}/sqlldr_log_${filename_only}.TOC bad=${WORK_PATH}/bad.${filename_only}.TOC
        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_PVS_TOC.dat' into table stludr.SYNC_INTERFACE_PVS_TOC_${acct_month}  fields terminated by '!!!!' lines terminated by '\n' (TAG,PROV_CODE,IS_TO_C,PRODUCT_SUBS_ID,PRODUCT_ID,PRODUCT_NAME,DB_PRODUCT_ID,DB_PRODUCT_NAME,PV_PRODUCT_CLASS,BILLING_TERM,PAY_TERM,FEE_VAL,TAX_RATE,TAX,FEE_NO_TAX,PV_SETTLE_RATE,PV_SETTLE_VALUE,ISSUE_TIME,EXPIRE_TIME,FILE_NAME);" 

        cd -

        ##original data file move to backup dir
        mv  $filename ${BACKUP_PATH}/${acct_month}/

        ##delete file from work dir
        rm ${WORK_PATH}/${filename_only} ${WORK_PATH}/${filename_only}.tmp
        rm ${WORK_PATH}/${filename_only}.TOC*
        rm ${WORK_PATH}/SYNC_INTERFACE_PVS*.dat

        f_Print "f_load_data - ${filename_only} - loading file - done"
    done

    f_Print "f_load_data finishes" 

}  ## f_load_data

function f_validation
{

    f_Print "f_validation starts"

    echo "update stludr.SYNC_INTERFACE_PVS_${acct_month} set status='23'  where prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) and status is null;
    update stludr.SYNC_INTERFACE_PVS_TOC_${acct_month} set status='23' where prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) and status is null;
     update stludr.SYNC_INTERFACE_PVS_${acct_month} a set status = '0' where status is null;
     update stludr.SYNC_INTERFACE_PVS_TOC_${acct_month} set status='0' where status is null;
      commit; " | mysql -u$db_user -p$db_passwd -h$db_host -P$db_port

    f_Print "f_validation finishes"
    
}  ## f_validation

function f_get_response_error
{

    f_Print "f_get_response_error starts"

    mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -N -s -e "select file_name ||':DR:<ErrorRecord><ErrorCode>'|| status ||'</ErrorCode><ErrorNode>' ||  
        substr( TAG			|| ',' ||
	    PROV_CODE           || ',' ||
        IS_TO_C			|| ',' ||
	    CUSTOMER_PROVINCE_NUMBER || ',' ||
		CUSTOMER_NAME		|| ',' ||
		PRODUCT_SUBS_ID		|| ',' ||
		PRODUCT_ID		|| ',' ||
		PRODUCT_NAME		|| ',' ||
		DB_PRODUCT_ID		|| ',' ||
		DB_PRODUCT_NAME		|| ',' ||
		CO_PRODUCT_ID		|| ',' ||
		BL_PRODUCT_ID		|| ',' ||
		ONE_PRODUCT_ID		|| ',' ||
		BILLING_TERM		|| ',' ||
		PAY_TERM		|| ',' ||
		PROD_CHARGE_CODE	|| ',' ||
		ONE_PRO_CHARGE_CODE	|| ',' ||
		ONE_PRO_CHARGE_NAME	|| ',' ||
		DB_PROD_CHARGE_CODE	|| ',' ||
		FEE_VAL			|| ',' ||
		TAX_RATE		|| ',' ||
		TAX			|| ',' ||
		FEE_NO_TAX		|| ',' ||
		FEE_FLAG		|| ',' ||
		DISCOUNT_AMOUNT		|| ',' ||
		STANDARD_FEE		|| ',' ||
		SETTLE_FEE		, 0,256)  || '</ErrorNode></ErrorRecord>' 
    from stludr.sync_interface_pvs_${acct_month} 
   where status <> '0' 
   union all 
   select file_name ||':DR:<ErrorRecord><ErrorCode>'|| status ||'</ErrorCode><ErrorNode>'
     || substr(	TAG	|| ',' ||
	PROV_CODE	|| ',' ||
	IS_TO_C		|| ',' ||
	PRODUCT_SUBS_ID || ',' ||
	PRODUCT_ID	|| ',' ||
	PRODUCT_NAME	|| ',' ||
	DB_PRODUCT_ID	|| ',' ||
	DB_PRODUCT_NAME	|| ',' ||
	PV_PRODUCT_CLASS|| ',' ||
	BILLING_TERM	|| ',' ||
	PAY_TERM	|| ',' ||
	FEE_VAL		|| ',' ||
	TAX_RATE	|| ',' ||
	TAX		|| ',' ||
	FEE_NO_TAX	|| ',' ||
	PV_SETTLE_RATE	|| ',' ||
	PV_SETTLE_VALUE || ',' ||
	ISSUE_TIME	|| ',' ||
	EXPIRE_TIME	, 0,256)  || '</ErrorNode></ErrorRecord>'
	from stludr.sync_interface_pvs_toc_${acct_month}  where status <> '0'; " >  ${response_file};

    f_Print "f_get_response_error finishes"
    
}  ## f_get_response_error

function f_get_response_file_error 
{
    f_Print "f_get_response_file_error starts"

    ##Remove empty lines and spaces
    sed '/^  *$/d' ${response_file} | sed '/^ *$/d' | sed 's/ //g' > ${response_file}.tmp
    mv ${response_file}.tmp ${response_file}

    response_header_1="<BillList>\n\t<Org_FileName>"
    response_header_2="</Org_FileName>\n\t<Resp_Date>"
    response_header_3="</Resp_Date>\n\t<FileStatus>2</FileStatus>\n\t<ErrorRecords>"
    response_tail="\t</ErrorRecords>\n</BillList>"

    ##Create file-level response files
    for filename in `awk -F ":DR:" '{print $1}' ${response_file} | sort | uniq`
    do
        f_Print "f_get_response_file_error - ${filename} - generating response file"
        current_ts=`date +%Y%m%d%H%M%S`
        echo "filename=$filename"
        echo -e "${response_header_1}${filename}${response_header_2}${current_ts}${response_header_3}" > ${WORK_PATH}/Resp_${filename}
        grep "^$filename"  ${response_file} | awk -F ":DR:" '{print "\t\t"$2}'   >> ${WORK_PATH}/Resp_${filename}
        if [ -f "${WORK_PATH}/bad.$filename" ]; then
            awk -F ","  '{for (i=2;i<=NF;i++)printf("%s,", $i);print ""}'  ${WORK_PATH}/bad.$filename | cut -c1-256 | while read line
            do
                echo -e "\t\t<ErrorRecord><ErrorCode>23</ErrorCode><ErrorNode>$line</ErrorNode></ErrorRecord>" >> ${WORK_PATH}/Resp_${filename}
            done
        fi 
        echo -e "${response_tail}" >> ${WORK_PATH}/Resp_${filename}
        mv ${WORK_PATH}/Resp_${filename} ${RESP_PATH}/Resp_${filename}
        f_Print "f_get_response_file_error - ${filename} - response file created - ${RESP_PATH}/Resp_${filename}"
    done

    rm ${response_file}

    f_Print "f_get_response_file_error finishes"

} ## f_get_response_file_error

function f_get_response
{

    f_Print "f_get_response starts"

    mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -N -s -e "  select *  from (
        select distinct file_name from stludr.sync_interface_pvs_${acct_month} where status = '0'
         union all 
        select distinct file_name from  stludr.sync_interface_pvs_toc_${acct_month} where status = '0' 
    ) order by file_name; " >  ${response_file};

    f_Print "f_get_response finishes"
    
}  ## f_get_response

function f_get_response_file 
{
    f_Print "f_get_response_file starts"

    ##Remove empty lines and spaces
    sed '/^  *$/d' ${response_file} | sed '/^ *$/d' | sed 's/ //g' > ${response_file}.tmp
    mv ${response_file}.tmp ${response_file}

    response_header_1="<BillList>\n\t<Org_FileName>"
    response_header_2="</Org_FileName>\n\t<Resp_Date>"
    response_header_3="</Resp_Date>\n\t<FileStatus>0</FileStatus>\n</BillList>"


    ##Create file-level response files
    for filename in `awk -F "," '{print $1}' ${response_file} | sort | uniq`
    do
        f_Print "f_get_response_file - ${filename} - generating response file"
        current_ts=`date +%Y%m%d%H%M%S`
        echo "filename=$filename"
        echo -e "${response_header_1}${filename}${response_header_2}${current_ts}${response_header_3}" > ${WORK_PATH}/Resp_${filename}
        mv ${WORK_PATH}/Resp_${filename} ${RESP_PATH}/Resp_${filename}
        f_Print "f_get_response_file - ${filename} - response file created - ${RESP_PATH}/Resp_${filename}"
    done

    rm ${response_file}

    f_Print "f_get_response_file finishes"

} ## f_get_response_file

##main starts here
if (( $# < 1 )); then
    echo "usage: txt2db_pvsettle.sh <acct_month>"
    exit
fi

f_Print "*****************************************************"
f_Print "                txt2db_pvsettle.sh starts                    "
f_Print "*****************************************************"

##Read input param acct_month
acct_month=$1
f_Print "acct_month=$acct_month"

##Create backup data path
mkdir -p ${BACKUP_PATH}/${acct_month}
f_Print "backup data path ${BACKUP_PATH}/${acct_month} created"


f_init_config
if (( $? != 0)); then
   f_Print "f_init_config failure. Exit now." 
fi

##Check input dir, exit when no input
input_file_no=`find ${DATA_PATH} -type f -name "${naming_convention}" | wc -l`
echo input_file_no=$input_file_no
if (( input_file_no < 1 )); then
    f_Print "No input file found under dir ${DATA_PATH}. Exit now."
    exit
else
    f_Print "No of input file found under dir ${DATA_PATH}: ${input_file_no}"
fi

##Truncate data table before loading data files into DB
f_truncate_table
if (( $? != 0)); then
   f_Print "f_truncate_table failure. Exit now." 
fi

f_load_data
if (( $? != 0)); then
   f_Print "f_load_data failure. Exit now." 
fi

f_validation
if (( $? != 0)); then
   f_Print "f_validation failure. Exit now." 
fi

f_get_response_error
if (( $? != 0)); then
   f_Print "f_get_response_error failure. Exit now." 
fi

f_get_response_file_error
if (( $? != 0)); then
   f_Print "f_get_response_file_error failure. Exit now." 
fi

f_get_response
if (( $? != 0)); then
   f_Print "f_get_response failure. Exit now." 
fi

f_get_response_file
if (( $? != 0)); then
   f_Print "f_get_response_file failure. Exit now." 
fi

f_Print "*****************************************************"
f_Print "                txt2db_pvsettle.sh finishes                   "
f_Print "*****************************************************"
f_Print ""
f_Print ""


