package com.settle.server.module.bigdata.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.settle.server.config.BigDataConfig;
import com.settle.server.entity.bigdata.BigDataFileLog;
import com.settle.server.entity.bigdata.BigdataBroadbandPriceCfg;
import com.settle.server.entity.bigdata.StlBigdataSettle;
import com.settle.server.entity.bigdata.SyncInterfaceBigdataFlow;
import com.settle.server.enums.BigDataConstants;
import com.settle.server.enums.ProvinceCode;
import com.settle.server.module.bigdata.dto.BigDataDTO;
import com.settle.server.module.bigdata.dto.BigDataSettleDTO;
import com.settle.server.service.impl.BigDataSettServiceImpl;
import com.settle.server.utils.EnumUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/9/6
 * @since 1.0.0
 */
@Service
@Slf4j
public class BigDataFlowService {

    @Autowired
    private BigDataFileLogService dataFileLogService;
    @Autowired
    private BigDataConfig bigDataConfig;
    @Autowired
    private SyncInterfaceBigdataFlowService syncInterfaceBigdataFlowService;
    @Autowired
    private StlBigdataSettleService stlBigdataSettleService;
    @Autowired
    private BigdataBroadbandPriceCfgService bigdataBroadbandPriceCfgService;

    private static final BlockingQueue<String> QUEUE = new LinkedBlockingQueue<>();


    private static ThreadPoolExecutor executor;

    @PostConstruct
    public void init() {
        ThreadFactoryBuilder builder = new ThreadFactoryBuilder();
        builder.setNamePrefix("BD-FLOW-");
        ThreadFactory threadFactory = builder.build();
        executor = new ThreadPoolExecutor(5, 5, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), threadFactory);

    }

    private StlBigdataSettle genBigDataSettle(BigDataSettleDTO dto, String respFileName, Map<String, BigdataBroadbandPriceCfg> priceCfgMap) {
        BigDecimal avgFlow = dto.getAvgFlow();

        String bizType = dto.getBizType();
        BigdataBroadbandPriceCfg priceCfg = priceCfgMap.get(bizType);
        if (priceCfg == null) {
            log.error("bigdata_broadband_price_cfg is null,bizType:{}", bizType);
            return null;
        }
        BigDecimal settlePrice = priceCfg.getSettlePrice();
        String settleAmount = NumberUtil.round(NumberUtil.mul(avgFlow, settlePrice), 2).toString();

        StlBigdataSettle bigdataSettle = new StlBigdataSettle();
        bigdataSettle.setId(IdUtil.getSnowflakeNextId());
        bigdataSettle.setSettleAmount(settleAmount);
        bigdataSettle.setAvgFlow(String.valueOf(dto.getAvgFlow()));
        bigdataSettle.setBusinessType(dto.getBizType());
        bigdataSettle.setInProvinceName(dto.getInProvName());
        bigdataSettle.setOutPorvinceName(dto.getOutProvName());
        bigdataSettle.setCreatedTime(LocalDateTime.now());
        bigdataSettle.setOpTime(dto.getOpTime());
        bigdataSettle.setTaxRate("0.06");
        bigdataSettle.setRespFileName(respFileName);

        return bigdataSettle;
    }


    public void process(String acctMonth, List<BigDataDTO> bigDataDTOS) {
        //1.校验文件内容
        ArrayListMultimap<String, BigDataDTO> listMultimap = doProcess(bigDataDTOS);
        if (listMultimap.isEmpty()) {
            return;
        }
        //2.删除数据
        deleteData(acctMonth);
        //3.插入数据
        List<SyncInterfaceBigdataFlow> flows = listMultimap
                .values()
                .stream()
                .flatMap(dto -> {
                    return dto.getSyncInterfaceBigdataFlows().stream();
                }).collect(Collectors.toList());
        syncInterfaceBigdataFlowService.insertBatch(flows);

        //4.生成响应数据
        CompletableFuture.runAsync(() -> {
            this.produceRespData(flows);
        }, executor);

    }

    private void deleteData(String acctMonth) {
        syncInterfaceBigdataFlowService.deleteByAcctMonth(acctMonth);

    }

    public void produceRespData(List<SyncInterfaceBigdataFlow> flows) {
        //按照流入省，流出省，业务类型 进行分组
        Map<String, Double> avgMap = flows.stream()
                .collect(
                        Collectors.groupingBy(dto -> {
                            return dto.getInProvinceName() + "|" + dto.getOutProvinceName() + "|" + dto.getBusType();
                        }, Collectors.averagingDouble(dto -> Double.parseDouble(dto.getDownDataTraffic()))));
        String settleMonth = flows.get(0).getSettleMonth();

        ////PrePaymentSettlementStatement_{yyyymm}.csv
        String respFileFormat = bigDataConfig.getRespFileFormat();
        String respFileName = respFileFormat.replace("{yyyymm}", settleMonth);

        //查下单价
        List<BigdataBroadbandPriceCfg> priceCfgList = bigdataBroadbandPriceCfgService.selectAll();
        Map<String, BigdataBroadbandPriceCfg> priceCfgMap = priceCfgList
                .stream()
                .collect(Collectors.toMap(BigdataBroadbandPriceCfg::getBusType, Function.identity(), (a, b) -> a));

        List<StlBigdataSettle> list = Lists.newArrayList();
        Set<Map.Entry<String, Double>> entries = avgMap.entrySet();
        for (Map.Entry<String, Double> entry : entries) {
            String key = entry.getKey();
            String[] split = key.split("\\|");
            String inProvName = split[0];
            String outProvName = split[1];
            String busType = split[2];
            Double downDataTraffic = entry.getValue();
            //保留4位小数
            BigDecimal avgFlow = NumberUtil.round(downDataTraffic, 4);
            BigDataSettleDTO dataSettleDTO = new BigDataSettleDTO();
            dataSettleDTO.setInProvName(inProvName);
            dataSettleDTO.setOutProvName(outProvName);
            dataSettleDTO.setBizType(busType);
            dataSettleDTO.setOpTime(settleMonth);
            dataSettleDTO.setAvgFlow(avgFlow);

            StlBigdataSettle bigdataSettle = genBigDataSettle(dataSettleDTO, respFileName,priceCfgMap);
            list.add(bigdataSettle);
        }

        stlBigdataSettleService.deleteByAcctMonth(settleMonth);
        stlBigdataSettleService.insertBatch(list);

        genRspFile(respFileName, list);

    }

    private void genRspFile(String respFileName, List<StlBigdataSettle> list) {
        File descFile = new File(bigDataConfig.getRespPath(), respFileName);
        FileUtil.mkParentDirs(descFile);
        String firstLine = "inProvinceName,outProvinceName,businessType,avgFlow,opTime,settleAmount,taxRate";
        List<String> lines = Lists.newArrayList();
        lines.add(firstLine);
        StringBuilder sb = new StringBuilder();
        for (StlBigdataSettle bigdataSettle : list) {
            sb.append(bigdataSettle.getInProvinceName()).append(",");
            sb.append(bigdataSettle.getOutPorvinceName()).append(",");
            sb.append(bigdataSettle.getBusinessType()).append(",");
            sb.append(bigdataSettle.getAvgFlow()).append(",");
            sb.append(bigdataSettle.getOpTime()).append(",");
            sb.append(bigdataSettle.getSettleAmount()).append(",");
            sb.append(bigdataSettle.getTaxRate());

            lines.add(sb.toString());
            sb.delete(0, sb.length());
        }
        //最后一行添加换行
        String lastLine = lines.get(lines.size() - 1);
        lastLine = lastLine + System.lineSeparator();
        lines.remove(lines.size() - 1);
        lines.add(lastLine);
        File tempFile = new File(bigDataConfig.getRespPath(), ".temp" + respFileName);
        FileUtil.writeLines(lines, tempFile, StandardCharsets.UTF_8);
        FileUtil.rename(tempFile, respFileName, true);
        log.info("生成响应文件：{} 成功", respFileName);
    }

    private ArrayListMultimap<String, BigDataDTO> doProcess(List<BigDataDTO> bigDataDTOS) {
        ArrayListMultimap<String, BigDataDTO> multimap = ArrayListMultimap.create();

        List<BigDataDTO> list = bigDataDTOS.stream()
                .filter(dto -> dto.getFileLog().getFileStatus().equals(BigDataConstants.ErrorMsg.F00.getErrorCode()))
                .collect(Collectors.toList());

        for (BigDataDTO dto : list) {
            try {
                List<SyncInterfaceBigdataFlow> stlBigdataFlows = this.doCheckOnce(dto);
                dto.setSyncInterfaceBigdataFlows(stlBigdataFlows);
            } catch (Exception e) {
                dto.getFileLog().setFileStatus(BigDataConstants.ErrorMsg.F99.getErrorCode());
                dto.getFileLog().setMessage(StrUtil.subSufByLength(ExceptionUtil.stacktraceToString(e), 200));
                log.error("校验文件异常,文件 :{}", dto.getSrcFile().getName(), e);
            } finally {
                FileUtil.del(dto.getUngzipFile());
                updateLog(dto.getFileLog());
                moveFile(dto);
                multimap.put(dto.getSrcFile().getName(), dto);
            }

        }
        return multimap;
    }

    private void moveFile(BigDataDTO dto) {
        BigDataFileLog fileLog = dto.getFileLog();
        String fileStatus = fileLog.getFileStatus();
        String backPath = "";
        if (BigDataConstants.ErrorMsg.F00.getErrorCode().equals(fileStatus)) {
            backPath = bigDataConfig.getBackPath();
        } else {
            backPath = bigDataConfig.getErrPath();
        }
        if (!backPath.endsWith("/")) {
            backPath += "/";
        }
        Path desc = Paths.get(backPath, dto.getAcctMonth());
        FileUtil.mkdir(desc);
        FileUtil.move(dto.getSrcFile().toPath(), desc, true);
        log.info("文件移动成功：{} to {}", dto.getSrcFile().getName(), desc);
    }

    private void updateLog(BigDataFileLog dataFileLog) {
        if (BigDataConstants.ErrorMsg.F00.getErrorCode().equals(dataFileLog.getFileStatus())) {
            //成功的不需要更新
            return;
        }
        dataFileLogService.updateLog(dataFileLog);

    }

    private List<SyncInterfaceBigdataFlow> doCheckOnce(BigDataDTO dto) {
        List<SyncInterfaceBigdataFlow> list = Lists.newArrayList();

        File srcFile = dto.getSrcFile();
        File ungzipFile = dto.getUngzipFile();
        BigDataFileLog fileLog = dto.getFileLog();
        List<String> lines = FileUtil.readLines(ungzipFile, StandardCharsets.UTF_8);
        int lineNum = 0;
        String fileName = srcFile.getName();
        for (String line : lines) {
            lineNum++;
            List<String> result = StrUtil.split(line, BigDataSettServiceImpl.VERTICAL_BAR);
            if (CollectionUtils.isEmpty(result) || result.size() != 5) {
                fileLog.setFileStatus(BigDataConstants.ErrorMsg.F06.getErrorCode());
                fileLog.setMessage("行数:" + lineNum + ",不是5个参数");
                log.warn("Verification Error fileName: {}, 该行不是5个参数, lineNum :{}, Line is {}", fileName, lineNum, lines);
                break;
            }
            String inProvName = result.get(0);
            String outProvName = result.get(1);
            String busType = result.get(2);
            String downDataTraffic = result.get(3);
            String settleMonth = result.get(4);
            Collection<String> provinceCodeAll = ProvinceCode.provinceCodeMap.values();
            //流入省编码
            if (!provinceCodeAll.contains(inProvName)) {
                fileLog.setFileStatus(BigDataConstants.ErrorMsg.F09.getErrorCode());
                fileLog.setMessage("行数:" + lineNum + ",流入省编码错误");
                log.warn("Verification Error fileName: {}, 该行的流入省编码错误, lineNum :{}, Line is {}", fileName, lineNum, lines);
                break;
            }
            if (!provinceCodeAll.contains(outProvName)) {
                fileLog.setFileStatus(BigDataConstants.ErrorMsg.F10.getErrorCode());
                fileLog.setMessage("行数:" + lineNum + ",流出省编码错误");
                log.warn("Verification Error fileName: {}, 该行的流出省编码错误, lineNum :{}, Line is {}", fileName, lineNum, lines);
                break;
            }
            if (!EnumUtils.isInclude(BigDataConstants.BusType.class, busType)) {
                fileLog.setFileStatus(BigDataConstants.ErrorMsg.F08.getErrorCode());
                fileLog.setMessage("行数:" + lineNum + ",业务类型错误");
                log.warn("Verification Error fileName: {}, 该行的业务类型错误, lineNum :{}, Line is {}", fileName, lineNum, lines);
                break;
            }
            //下行流量
            Pattern pattern = Pattern.compile(BigDataSettServiceImpl.flowRegx);
            Matcher matcher = pattern.matcher(downDataTraffic);
            if (!matcher.matches()) {
                fileLog.setFileStatus(BigDataConstants.ErrorMsg.F07.getErrorCode());
                fileLog.setMessage("行数:" + lineNum + ",宽带流量格式错误");
                log.warn("Verification Error fileName: {}, 宽带流量格式错误, lineNum :{}, Line is {}", fileName, lineNum, lines);
                break;
            }
            //统计日期（年月）
            if (settleMonth.length() != 6 || !settleMonth.equals(dto.getAcctMonth())) {
                fileLog.setFileStatus(BigDataConstants.ErrorMsg.F97.getErrorCode());
                fileLog.setMessage("行数:" + lineNum + ",统计日期错误");
                log.warn("Verification Error fileName: {}, 统计日期错误, lineNum :{}, Line is {}", fileName, lineNum, lines);
                break;
            }

            //记录校验没问题的数据
            SyncInterfaceBigdataFlow bigdataFlow = new SyncInterfaceBigdataFlow();
            bigdataFlow.setId(IdUtil.getSnowflakeNextId());
            bigdataFlow.setInProvinceName(inProvName);
            bigdataFlow.setInProvinceCode(ProvinceCode.getCode(inProvName));
            bigdataFlow.setOutProvinceName(outProvName);
            bigdataFlow.setOutProvinceCode(ProvinceCode.getCode(outProvName));
            bigdataFlow.setBusType(busType);
            bigdataFlow.setDownDataTraffic(downDataTraffic);
            bigdataFlow.setSettleMonth(settleMonth);
            bigdataFlow.setFileName(fileName);
            list.add(bigdataFlow);
        }
        return list;
    }
}