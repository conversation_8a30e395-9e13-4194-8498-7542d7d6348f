package com.hp.cmcc.bboss.settle.bo;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.hp.cmcc.bboss.settle.config.redis.RedisClusterPool;
import com.hp.cmcc.bboss.settle.dto.OdbSystemParam;
import com.hp.cmcc.bboss.settle.dto.UdrDef;
import com.hp.cmcc.bboss.settle.util.PubMethod;
import com.hp.cmcc.bboss.settle.util.StrTruncator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class UdrFmt extends GsonObj implements Serializable {
	private static final long serialVersionUID = 6892272603010600711L;
	private static final Logger L = LoggerFactory.getLogger(UdrFmt.class);

	public static final int E_FIELD_CNT_33 = 35;
	public static final int E_00_A01_RAW_FILE_NM = 0;
	public static final int E_01_A02_RAW_LINE_NUM = 1;
	public static final int E_02_A03_ERR_CODE = 2;
	public static final int E_03_A04_ORG_FILE_ID = 3;
	public static final int E_04_A05_FILE_ID = 4;
	public static final int E_05_A06_LINE_NUM = 5;
	public static final int E_06_A07_RCV_MM = 6;
	public static final int E_07_A08_RCV_YMDH = 7;
	public static final int E_08_A09_RCV_TM = 8;
	public static final int E_09_A10_FILE_YMD = 9;
	public static final int E_10_A11_FILE_PROV = 10;
	public static final int E_11_A12_ERCY_TIMES = 11;
	public static final int E_12_A13_ERCY_TIME = 12;
	public static final int E_13_A14_PROC_FLAG = 13;
	public static final int E_14_A15_ACUMLT = 14;
	public static final int E_15_E01_ERR_IDX = 15;
	public static final int E_16_E02_ERR_VAL = 16;
	public static final int E_17_E03_START_TM = 17;
	public static final int E_18_E04_EC_CODE = 18;
	public static final int E_19_E05_PROV = 19;
	public static final int E_20_E06_ACCT_DAY = 20;
	public static final int E_21_E07_PP_FILE_ID = 21;
	public static final int E_22_E08_ERR_REASON = 22; // for dup ==> E_04_A05_FILE_ID:E_05_A06_LINE_NUM
	public static final int E_23_E09_SPARE1 = 23;
	public static final int E_24_R01_RAW_UDR = 24;
	public static final int E_25_R02_RAW_ATTACHMENT = 25;
	public static final int E_26_X01_SHPARM_VER = 26;
	public static final int E_27_X02_BIZRCY_CHARGE = 27;
	public static final int E_28_X03_BIZRCY_ACUMLT = 28;
	public static final int E_29_X04_ERCY_FILE_NM = 29;
	public static final int E_30_X05_ACNT_YM = 30;
	public static final int E_31_X06_RATE_ID = 31;
	public static final int E_32_X07_FEEDBACK_TAG = 32;

	public static final int E_33_X08_RATE_TYPE = 33; //RATE_TYPE
	public static final int E_34_X09_TARIFF_TYPE = 34; //TARIFF_TYPE

	public static final int S_FIELD_MIN_40 = 42;
	public static final int S_FIELD_CNT_40 = 43; //结算单
	public static final int S_00_ERROR_CODE_01 = 0;
	public static final int S_01_ERROR_LINE_02 = 1;
	public static final int S_02_BIZ_TYPE_03 = 2;
	public static final int S_03_DATA_SOURCE_04 = 3;
	public static final int S_04_STREAM_ID_05 = 4;
	public static final int S_05_EC_CODE_06 = 5;
	public static final int S_06_EC_PROV_CODE_07 = 6;
	public static final int S_07_OFFER_CODE_08 = 7;
	public static final int S_08_PRODUCT_CODE_09 = 8;
	public static final int S_09_OFFER_ORDER_ID_10 = 9;
	public static final int S_10_PRODUCT_ORDER_ID_11 = 10;
	public static final int S_11_ORDER_PROV_12 = 11;
	public static final int S_12_ACCOUNT_ID_13 = 12;
	public static final int S_13_MEM_NUMBER_14 = 13;
	public static final int S_14_MEM_PROV_15 = 14;
	public static final int S_15_ORDER_MODE_16 = 15;
	public static final int S_16_SIGN_ENTITY_17 = 16;
	public static final int S_17_CHARGE_ITEM_18 = 17;
	public static final int S_18_CHARGE_19 = 18;
	public static final int S_19_AMOUNT_NOTAX_20 = 19;
	public static final int S_20_AMOUNT_TAX_21 = 20;
	public static final int S_21_TAX_RATE_22 = 21;
	public static final int S_22_ORG_MONTH_23 = 22;
	public static final int S_23_PAID_MONTH_24 = 23;
	public static final int S_24_TICKET_ID_25 = 24;
	public static final int S_25_SETTLE_MONTH_26 = 25;
	public static final int S_26_OUT_OBJECT_27 = 26;
	public static final int S_27_IN_OBJECT_28 = 27;
	public static final int S_28_RECORD_ID_29 = 28;
	public static final int S_29_SETTLE_NOTAX_30 = 29;
	public static final int S_30_SETTLE_TAX_31 = 30;
	public static final int S_31_FILE_ID_32 = 31;
	public static final int S_32_RULE_ID_33 = 32;
	public static final int S_33_DEST_SOURCE_34 = 33;
	public static final int S_34_PHASE_35 = 34;
	public static final int S_35_START_TIME_36 = 35;
	public static final int S_36_RES1_37 = 36;
	public static final int S_37_RES2_38 = 37;
	public static final int S_38_RES3_39 = 38;
	public static final int S_39_COUNTY_CODE_40 = 39;
	public static final int S_40_RES4_41 = 40;
	public static final int S_41_ATTACHMENT_42 = 41;
	//单独配置的字段
	public static final int S_40_ROUTE_CODE_41 = 40;

	public static final int U_FIELD_MIN_56 = 56;
	public static final int U_FIELD_CNT_57 = 57; //详单
	public static final int U_00_ERROR_CODE_01 = 0;
	public static final int U_01_ERROR_LINE_02 = 1;
	public static final int U_02_HOME_PARTY_03 = 2;
	public static final int U_03_FEE1_04 = 3;
	public static final int U_04_FEE2_05 = 4;
	public static final int U_05_FEE3_06 = 5;
	public static final int U_06_FEE4_07 = 6;
	public static final int U_07_CHARGE_CODE1_08 = 7;
	public static final int U_08_CHARGE_CODE2_09 = 8;
	public static final int U_09_CHARGE_CODE3_10 = 9;
	public static final int U_10_CHARGE_CODE4_11 = 10;
	// add gya start 20230216
	public static final int U_11_DISCOUNT1_12 = 11;
	public static final int U_12_DISCOUNT2_13 = 12;
	public static final int U_13_DISCOUNT3_14 = 13;
	public static final int U_14_DISCOUNT4_15 = 14;
	public static final int U_15_IS_MEM_CHARGE_PLAN1_16 = 15;
	public static final int U_16_IS_MEM_CHARGE_PLAN2_17 = 16;
	public static final int U_17_IS_MEM_CHARGE_PLAN3_18 = 17;
	public static final int U_18_IS_MEM_CHARGE_PLAN4_19 = 18;
	public static final int U_19_PRODUCT_OFFER1_20 = 19;
	public static final int U_20_PRODUCT_OFFER2_21 = 20;
	public static final int U_21_PRODUCT_OFFER3_22 = 21;
	public static final int U_22_PRODUCT_OFFER4_23 = 22;
	// add gya end 20230216
	public static final int U_23_EC_CODE_24 = 23;
	public static final int U_24_EC_PROV_CODE_25 = 24;
	public static final int U_25_PRODUCT_CODE_26 = 25;
	public static final int U_26_MEMBER_CODE_27 = 26; // eboss:U_14_SUBSCRIBER_NUMBER_15
	public static final int U_27_MEMBER_PROV_CODE_28 = 27; // eboss:U_15_AREA_CODE_16
	public static final int U_28_START_TIME_29 = 28;
	public static final int U_29_END_TIME_30 = 29;
	public static final int U_30_DURATION_31 = 30;
	public static final int U_31_VOLUME_UP_32 = 31;
	public static final int U_32_VOLUME_DOWN_33 = 32;
	public static final int U_33_TICKET_ID_34 = 33;
	public static final int U_34_PP_FILE_ID_35 = 34;
	public static final int U_35_ACCT_DAY_36 = 35;
	public static final int U_36_BIZ_TYPE_37 = 36;
	public static final int U_37_PROCESS_FLAG_38 = 37;
	public static final int U_38_BILL_FLAG_39 = 38;
	public static final int U_39_ACCUMULATION_KEY_40 = 39;
	public static final int U_40_DUP_TIME_41 = 40;
	public static final int U_41_EC_GROUP_42 = 41;
	public static final int U_42_OFFER_CODE_43 = 42;
	public static final int U_43_OFFER_ORDER_ID_44 = 43;
	public static final int U_44_PRODUCT_ORDER_ID_45 = 44;
	public static final int U_45_PROD_ORDER_MODE_46 = 45; // eboss:U_33_OTHER_PARTY_34
	public static final int U_46_RATE_BACK_ID_47 = 46;
	public static final int U_47_SUB_GROUP_NUM_48 = 47; // eboss:U_35_OTHER_AREA_CODE_36
	public static final int U_48_PRODUCT_ORDER_ID2_49 = 48;
//	public static final int U_49_IS_MEM_CP_50 = 49; // eboss:U_37_IP_PREFIX_38
	public static final int U_49_IS_SUB_GROUP_FLAG_50 = 49;
	public static final int U_50_SERVICE_51 = 50; // eboss:U_39_ATTACHMENT_40 bboss2:U_39_PROD_BILL_MODE_40
	public static final int U_51_SERVICE_OPTION_52 = 51; // eboss:U_40_RESERVE1_41 bboss2:U_40_PROD_PAY_MODE_41
	public static final int U_52_FREE_RESOURCE_53 = 52; // eboss:U_41_RESERVE2_42 bboss2:U_41_PRESERVED_COLUMN1_42
//	public static final int U_54_DISCOUNT_55 = 54; // eboss:U_42_RESERVE3_43 bboss2:U_42_ATTACHMENT_43
//	public static final int U_55_PRODUCT_OFFER_56 = 55; // eboss:U_43_RESERVE4_44 product_offer
	public static final int U_53_QUATITLY_54 = 53; // eboss:U_43_RESERVE4_44 product_offer
	public static final int U_54_RES1_55 = 54; // eboss:U_44_RESERVE5_45
	public static final int U_55_RES2_56 = 55; // eboss:U_45_RESERVE6_46
	public static final int U_56_ATTACHEMENT_57 = 56; // eboss:U_46_RESERVE7_47

	public static final String S_NM_ERROR_CODE_01 = "ERROR_CODE_01";
	public static final String S_NM_ERROR_LINE_02 = "ERROR_LINE_02";
	public static final String S_NM_BIZ_TYPE_03 = "BIZ_TYPE_03";
	public static final String S_NM_DATA_SOURCE_04 = "DATA_SOURCE_04";
	public static final String S_NM_STREAM_ID_05 = "STREAM_ID_05";
	public static final String S_NM_EC_CODE_06 = "EC_CODE_06";
	public static final String S_NM_EC_PROV_CODE_07 = "EC_PROV_CODE_07";
	public static final String S_NM_OFFER_CODE_08 = "OFFER_CODE_08";
	public static final String S_NM_PRODUCT_CODE_09 = "PRODUCT_CODE_09";
	public static final String S_NM_OFFER_ORDER_ID_10 = "OFFER_ORDER_ID_10";
	public static final String S_NM_PRODUCT_ORDER_ID_11 = "PRODUCT_ORDER_ID_11";
	public static final String S_NM_ORDER_PROV_12 = "ORDER_PROV_12";
	public static final String S_NM_ACCOUNT_ID_13 = "ACCOUNT_ID_13";
	public static final String S_NM_MEM_NUMBER_14 = "MEM_NUMBER_14";
	public static final String S_NM_MEM_PROV_15 = "MEM_PROV_15";
	public static final String S_NM_ORDER_MODE_16 = "ORDER_MODE_16";
	public static final String S_NM_SIGN_ENTITY_17 = "SIGN_ENTITY_17";
	public static final String S_NM_CHARGE_ITEM_18 = "CHARGE_ITEM_18";
	public static final String S_NM_CHARGE_19 = "CHARGE_19";
	public static final String S_NM_AMOUNT_NOTAX_20 = "AMOUNT_NOTAX_20";
	public static final String S_NM_AMOUNT_TAX_21 = "AMOUNT_TAX_21";
	public static final String S_NM_TAX_RATE_22 = "TAX_RATE_22";
	public static final String S_NM_ORG_MONTH_23 = "ORG_MONTH_23";
	public static final String S_NM_PAID_MONTH_24 = "PAID_MONTH_24";
	public static final String S_NM_TICKET_ID_25 = "TICKET_ID_25";
	public static final String S_NM_SETTLE_MONTH_26 = "SETTLE_MONTH_26";
	public static final String S_NM_OUT_OBJECT_27 = "OUT_OBJECT_27";
	public static final String S_NM_IN_OBJECT_28 = "IN_OBJECT_28";
	public static final String S_NM_RECORD_ID_29 = "RECORD_ID_29";
	public static final String S_NM_SETTLE_NOTAX_30 = "SETTLE_NOTAX_30";
	public static final String S_NM_SETTLE_TAX_31 = "SETTLE_TAX_31";
	public static final String S_NM_FILE_ID_32 = "FILE_ID_32";
	public static final String S_NM_RULE_ID_33 = "RULE_ID_33";
	public static final String S_NM_DEST_SOURCE_34 = "DEST_SOURCE_34";
	public static final String S_NM_PHASE_35 = "PHASE_35";
	public static final String S_NM_START_TIME_36 = "START_TIME_36";
	public static final String S_NM_RES1_37 = "RES1_37";
	public static final String S_NM_RES2_38 = "RES2_38";
	public static final String S_NM_RES3_39 = "RES3_39";
	public static final String S_NM_COUNTY_CODE_40 = "COUNTY_CODE_40";
	public static final String S_NM_RES4_41 = "RES4_41";
	public static final String S_NM_ATTACHMENT_42 = "ATTACHMENT_40";

	// 2023/2/23 start 原始话单需要改
	public static final String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
	public static final String CSV_WITH_ESC_PATTERN = "#\\|#";
//	public static final String CSV_WITH_ESC_PATTERN = "\\\\,";
	// 2023/2/23 end 原始话单需要改

	public static final String V000_NML_UDR = "V000";
	public static final String V009_DUP_UDR = "V009";
	public static final String V070_RATING_NO_MATCH = "V070";
	public static final String V081_EXP_VALIDATION = "V081";
	public static final String V082_EXP_CHKDUP = "V082";
	public static final String V083_EXP_GUIDING = "V083";
	public static final String V084_EXP_RATING = "V084";
	public static final String V085_EXP_ERHNDL = "V085";
	public static final String V099_FMT_ERR = "V099";
	public static final String F030_HOME_PARTY_EMPTY = "F030";
	public static final String F031_HOME_PARTY_BAD_FMT = "F031";
	public static final String F032_HOME_PARTY_INVALID_PROV = "F032";
	public static final String F120_EC_CODE_EMPTY = "F120";
	public static final String F121_EC_CODE_INVALID = "F121";
	public static final String F122_EC_CODE_INEFFECTIVE = "F122";
	public static final String F130_EC_PROV_CODE_EMPTY = "F130";
	public static final String F131_EC_PROV_CODE_INCONSISTENT = "F131";
	public static final String F170_START_TIME_BAD_FMT = "F170";
	public static final String F171_START_TIME_INVALID = "F171";
	public static final String F172_START_TIME_EXPIRE = "F172";
	public static final String F173_START_TIME_AHEAD = "F173";
	public static final String F180_END_TIME_BAD_FMT = "F180";
	public static final String F181_END_TIME_INVALID = "F181";
	public static final String F184_END_TIME_LT_START = "F184";
	public static final String F190_DURATION_BAD_FMT = "F190";
	public static final String F191_DURATION_TOO_LARGE = "F191";
	public static final String F192_DURATION_INVALID = "F192";
	public static final String F200_VOLUME_UP_BAD_FMT = "F200";
	public static final String F210_VOLUME_DOWN_BAD_FMT = "F210";
	public static final String F220_PP_FILE_ID_EMPTY = "F220";
	public static final String F240_ACCT_DAY_BAD_FMT = "F240";
	public static final String F241_ACCT_DAY_INVALID = "F241";
	public static final String F242_ACCT_DAY_INCONSISTENT = "F242";
	public static final String F250_BIZ_TYPE_EMPTY = "F250";
	public static final String F280_ACCUMULATION_KEY_EMPTY = "F280";
	public static final String F290_DUP_TIME_BAD_FMT = "F290";
	public static final String F291_DUP_TIME_INVALID = "F291";
	public static final String F292_DUP_TIME_EXPIRE = "F292";
	public static final String F293_DUP_TIME_AHEAD = "F293";
	public static final String F330_PRODUCT_ORDER_ID_EMPTY = "F330";
	public static final String F331_PRODUCT_ORDER_ID_INVALID = "F331";
	public static final String F332_PRODUCT_ORDER_ID_INEFFECTIVE = "F332";

	public static final String S030_BIZ_TYPE_EMPTY = "F030";
	public static final String S031_NO_BIZ_TYPE_CFG = "F031";
	public static final String S032_BIZ_TYPE_INEFFECTIVE = "F032";
	public static final String S033_BIZ_TYPE_INCONSISTENT = "F033";
	public static final String S040_DATA_SOURCE_EMPTY = "F040";
	public static final String S041_DATA_SOURCE_INVALID = "F041";
	public static final String S060_EC_CODE_EMPTY = "F060";
	public static final String S080_OFFER_CODE_EMPTY = "F080";
	public static final String S090_PRODUCT_CODE_EMPTY = "F090";
	public static final String S160_ORDER_MODE_EMPTY = "F160";
	public static final String S180_CHARGE_ITEM_EMPTY = "F180";
	public static final String S200_AMOUNT_NOTAX_EMPTY = "F200";
	public static final String S201_AMOUNT_NOTAX_INVALID = "F201";
	public static final String S210_AMOUNT_TAX_EMPTY = "F210";
	public static final String S211_AMOUNT_TAX_INVALID = "F211";
	public static final String S220_TAX_RATE_EMPTY = "F220";
	public static final String S221_TAX_RATE_INVALID = "F221";
	public static final String S230_ORG_PAID_MONTH_BOTH_EMPTY = "F230";
	public static final String S231_ORG_MONTH_INVALID = "F231";
	public static final String S241_PAID_MONTH_INVALID = "F241";
	public static final String S260_SETTLE_MONTH_EMPTY = "F260";
	public static final String S261_SETTLE_MONTH_INVALID = "F261";
	public static final String S320_FILE_ID_EMPTY = "F320";
	public static final String S321_FILE_ID_INVALID = "F321";
	public static final String S360_START_TIME_EMPTY = "F360";
	public static final String S361_START_TIME_INVALID = "F361";
	public static final String S500_NO_OFFER_CFG = "F500";
	public static final String S501_OFFER_CFG_INEFFECTIVE = "F501";
	public static final String S502_MULTI_OFFER_RULE_ID_DUP = "F502";
	public static final String S503_INVALID_ROUTE_CODE_FOR_FEEDBACK_UDR = "F503";
	public static final String S504_MULTI_RATE_TYPE_FOR_ONE_RULE = "F504";
	public static final String S510_NO_RULE_CFG = "F510";
	public static final String S511_RULE_CFG_INEFFECTIVE = "F511";
	public static final String S512_NO_RULE_ITEM_CFG = "F512";
	public static final String S513_RULE_ITEM_INEFFECTIVE = "F513";
	// public static final String S514_MULTI_RULES_MATCHED = "F514";
	public static final String S520_NO_OBJECT_CFG = "F520";
	public static final String S530_NO_RATE_CFG = "F530";
	public static final String S531_RATE_CFG_INEFFECTIVE = "F531";
	public static final String S532_RATE_NO_COR_IN_OBJECT_CFG = "F532";
	public static final String S600_OBJECT_TYPE_UKN = "F600";
	public static final String S601_OBJECT_OWNER_UKN = "F601";
	public static final String S602_OBJECT_FIELD_UKN = "F602";
	public static final String S603_OBJECT_PAY_PROV_PRODUCT_ORDER_ID_EMPTY = "F603";
	public static final String S604_OBJECT_PAY_PROV_NO_CFG = "F604";
	public static final String S605_OBJECT_PAY_PROV_INEFFECTIVE = "F605";
	public static final String S606_CAN_NOT_REBALANCE = "F606";
	public static final String S607_OBJECT_IDD_FIELD_EMPTY = "F607";
	public static final String S610_RATE_TYPE_UKN = "F610";
	public static final String S611_NO_FLAT_RATE_CFG = "F611";
	public static final String S612_FLAT_RATE_VAL_OOR = "F612";
	public static final String S613_TARIFF_RATE_MULTI_TARIFF_TYPES = "F613";
	public static final String S614_REPART_RATE_MULTI_TARIFF_TYPES = "F614";
	public static final String S621_NO_TARIFF_RATE_CFG = "F621";
	public static final String S622_TARIFF_RATE_TYPE_INVALID = "F622";
	public static final String S623_TARIFF_RATE_MATCH_MODE_INVALID = "F623";
	public static final String S624_NO_TARIFF_PARAMETER_CFG = "F624";
	public static final String S625_TARIFF_PARAMETER_INEFFECTIVE = "F625";
	public static final String S626_TARIFF_PARAMETER_VALUE_INVALID = "F626";
	public static final String S627_TARIFF_PARAMETER_IDD_FIELD_EMPTY = "F627";
	public static final String S631_NO_REPART_RATE_CFG = "F631";
	public static final String S632_REPART_RATE_TYPE_INVALID = "F632";
	public static final String S633_REPART_RATE_MATCH_MODE_INVALID = "F633";
	public static final String S634_NO_REPART_PARTITION_CFG = "F634";
	public static final String S635_NO_REPART_PARAMETER_CFG = "F635";
	public static final String S636_REPART_PARAMETER_INEFFECTIVE = "F636";
	public static final String S637_REPART_PARAMETER_VALUE_INVALID = "F637";
	public static final String S638_REPART_PARAMETER_IDD_FIELD_EMPTY = "F638";
	public static final String S639_ESP_RATE_MATCH_MODE_INVALID = "F639";
	public static final String S640_NO_ESP_PARAMETER_CFG = "F640";
	public static final String S641_ESP_PARAMETER_INEFFECTIVE = "F641";
	public static final String S642_ESP_PARAMETER_VALUE_INVALID = "F642";
	public static final String S643_ESP_PARAMETER_IDD_FIELD_EMPTY = "F643";

	public static final String PROC_FLAG_VALIDATION = "V";
	public static final String PROC_FLAG_CHKDUP = "C";
	public static final String PROC_FLAG_GUIDING = "G";
	public static final String PROC_FLAG_RATING = "R";

	public static final String AUX_MAP_KEY_OFFER = "OFFER"; // V:List<EdbStlOfferRec>
	public static final String AUX_MAP_KEY_RULE = "RULE:%d"; // K:rule_id V:EdbStlRuleRec
	public static final String AUX_MAP_KEY_O_OBJECT = "O_OBJ:%d"; // K:rule_id V:EdbStlObjectRec
	public static final String AUX_MAP_KEY_RATE = "RATE:%d"; // K:rule_id V:List<EdbStlRateRec>
	public static final String AUX_MAP_KEY_I_OBJECT = "I_OBJ:%d"; // k:object_id V:EdbStlObjectRec

	public static final String AUX_KEY_NULL_FILLER = "NULL";

	// update #|# 改为 ]|[ geyongan 2023/3/31
	public static final String ULD_DELIMETER_ERR = "]|[";
	public static final String ULD_DELIMETER_NML = "]|[";
	public static final String ULD_DELIMETER_EOL = "\n";

	@Expose
	public String[] _eFields;
	@Expose
	public String[] _uFields;
	public String _gsonStr;
	public Map<String, Object> _auxMap;  //存储 AUX_相关库表内容 如：AUX_MAP_KEY_OFFER、AUX_MAP_KEY_RULE 参考（319-323行）
	@Expose
	public List<String[]> _uList;

	@Override
	public String toGsonStr() {
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().setDateFormat(GsonObj._DATE_TIME_FMT).create();
		return gson.toJson(this);
	}

	public String toGsonStrFull() {
		Gson gson = new GsonBuilder().setDateFormat(GsonObj._DATE_TIME_FMT).create();
		return gson.toJson(this);
	}

	public UdrFmt() {
		_eFields = new String[E_FIELD_CNT_33];
		_uFields = null; // will be expanded in validation Bolt
	}

	public boolean isErr() {
		if (PubMethod.IsEmpty(_eFields[E_02_A03_ERR_CODE]))
			return false;
		if (_eFields[E_02_A03_ERR_CODE].equals(V000_NML_UDR))
			return false;
		return true;
	}

	public boolean isErcy() {
		if (PubMethod.IsEmpty(_eFields[UdrFmt.E_03_A04_ORG_FILE_ID]) || PubMethod.IsEmpty(_eFields[UdrFmt.E_04_A05_FILE_ID])) {
			return false;
		} else if (_eFields[UdrFmt.E_04_A05_FILE_ID].equals(_eFields[UdrFmt.E_03_A04_ORG_FILE_ID])) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 反馈标记	用于结算单二次结算
	 * @return true 需要二次结算 false 不需要二次结算
	 */
	public boolean isFeedback() {
		return PubMethod.IsEmpty(_eFields[UdrFmt.E_32_X07_FEEDBACK_TAG]) ? false : true;
	}

	public String getFileType() {
		return _eFields[UdrFmt.E_04_A05_FILE_ID].substring(8, 11);
	}

	public String getBizlogCid() {
		return "C:" + getFileType();
	}

	public String getBizlogLid() {
		if (RedisClusterPool.isCluster()) {
			return "L:" + _eFields[E_07_A08_RCV_YMDH] + ":{" + _eFields[E_04_A05_FILE_ID]+"}";
		}
		return "L:" + _eFields[E_07_A08_RCV_YMDH] + ":" + _eFields[E_04_A05_FILE_ID];
	}

	public String getBizlogUid() {
		if (isFeedback()) {
			return "U:" + _eFields[E_07_A08_RCV_YMDH] + ":" + _getId() + ":" + _eFields[UdrFmt.E_32_X07_FEEDBACK_TAG];
		} else {
			return "U:" + _eFields[E_07_A08_RCV_YMDH] + ":" + _getId();
		}
	}

	public String getUldKey(String aux_key) {
		String aux_ = aux_key;
		if (PubMethod.IsEmpty(aux_key))
			aux_ = AUX_KEY_NULL_FILLER;
		return getFileType() + ":" + aux_ + ":" + _eFields[UdrFmt.E_07_A08_RCV_YMDH] + ":" + getAcntYm();
	}

	public String getUldProcKey(String aux_key) {
		String aux_ = aux_key;
		if (PubMethod.IsEmpty(aux_key))
			aux_ = AUX_KEY_NULL_FILLER;
		return _eFields[UdrFmt.E_04_A05_FILE_ID] + ":" + _eFields[UdrFmt.E_03_A04_ORG_FILE_ID] + ":" + aux_ + ":"
				+ _eFields[UdrFmt.E_21_E07_PP_FILE_ID];
	}

	public String getAcntYm() {
		if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
			if (PubMethod.IsEmpty(_uFields[UdrFmt.S_25_SETTLE_MONTH_26]) || _uFields[UdrFmt.S_25_SETTLE_MONTH_26].length() < 6) {
				return "197001";
			} else {
				return _uFields[UdrFmt.S_25_SETTLE_MONTH_26].substring(0, 6);
			}
		} else {
			if (PubMethod.IsEmpty(_uFields[UdrFmt.U_35_ACCT_DAY_36]) || _uFields[UdrFmt.U_35_ACCT_DAY_36].length() < 6) {
				return "197001";
			} else {
				return _uFields[UdrFmt.U_35_ACCT_DAY_36].substring(0, 6);
			}
		}
	}



	public boolean hasProcFlag(String proc_flag) {
		if (proc_flag == null)
			return false;
		if (proc_flag.length() != 1)
			return false;
		if (_eFields[E_13_A14_PROC_FLAG] == null)
			return false;
		return _eFields[E_13_A14_PROC_FLAG].contains(proc_flag);
	}

	public void setProcFlag(String proc_flag) {
		if (proc_flag == null)
			return;
		if (proc_flag.length() != 1) {
			return;
		}
		if (_eFields[E_13_A14_PROC_FLAG] == null) {
			_eFields[E_13_A14_PROC_FLAG] = proc_flag;
		} else if (!_eFields[E_13_A14_PROC_FLAG].contains(proc_flag)) {
			_eFields[E_13_A14_PROC_FLAG] = _eFields[E_13_A14_PROC_FLAG] + proc_flag;
		}
	}

	public void dumpErr(PrintWriter pw, String charset) {
		StringBuilder sb_ = new StringBuilder();
		StrTruncator truncator_ = StrTruncator.GetStrTruncator(charset);
		for (int i = 0; i < UdrDef.E_DEF.length; i++) {
			UdrDef def_ = UdrDef.E_DEF[i];
			if (_eFields[def_._idx] == null) {
				if (def_._dftVal != null) {
					sb_.append(def_._dftVal);
				}
			} else if (def_._isNum) {
				sb_.append(_eFields[def_._idx]); // no truncate for numbers
			} else {
				sb_.append(truncator_.truncate(_eFields[def_._idx], def_._maxLen));
			}
			if (i < UdrDef.E_DEF.length - 1)
				sb_.append(ULD_DELIMETER_ERR);
		}
		sb_.append(ULD_DELIMETER_EOL);
		pw.print(sb_.substring(0));
	}


	public void dumpIDD(PrintWriter pw, String charset) {
		L.info("dumpIDD!!!， SystemType= [{}]", OdbSystemParam.GetInstance()._subSystemType);
		if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
			_dumpSIDD(pw, charset);
		} else {
			_dumpUIDD(pw, charset);
		}
	}

	private void _dumpUIDD(PrintWriter pw, String charset) {
		StringBuilder sb_ = new StringBuilder();
		StrTruncator truncator_ = StrTruncator.GetStrTruncator(charset);
		_dumpUFields(_uFields, sb_, truncator_);
		pw.print(sb_.substring(0));
	}

	private void _dumpUFields(String[] fields, StringBuilder sb, StrTruncator truncator) {
		for (int i = 0; i < UdrDef.U_DEF.length; i++) {
			UdrDef def_ = UdrDef.U_DEF[i];
			if (fields[def_._idx] == null) {
				if (def_._dftVal != null) {
					sb.append(def_._dftVal);
				}
			} else if (def_._isNum) {
				sb.append(fields[def_._idx]); // no truncate for numbers
			} else {
				sb.append(truncator.truncate(fields[def_._idx], def_._maxLen));
			}
			if (i < UdrDef.U_DEF.length - 1)
				sb.append(ULD_DELIMETER_NML);
		}
		sb.append(ULD_DELIMETER_EOL);
	}

	private void _dumpSIDD(PrintWriter pw, String charset) {
		StringBuilder sb_ = new StringBuilder();
		StrTruncator truncator_ = StrTruncator.GetStrTruncator(charset);
		_dumpSFields(_uFields, sb_, truncator_);
		if (_uList != null && !_uList.isEmpty()) {
			L.info("_uList.size()={}", _uList.size());
			for (int i = 0; i < _uList.size(); ++i) {
				String[] fields_ = (String[]) _uList.get(i);
				_dumpSFields(fields_, sb_, truncator_);
			}
		}
		pw.print(sb_.substring(0));
	}

	private void _dumpSFields(String[] fields, StringBuilder sb, StrTruncator truncator) {
		for (int i = 0; i < UdrDef.S_DEF.length; i++) {
			UdrDef def_ = UdrDef.S_DEF[i];
			if (fields[def_._idx] == null) {
				if (def_._dftVal != null) {
					sb.append(def_._dftVal);
				}
			} else if (def_._isNum) {
				sb.append(fields[def_._idx]); // no truncate for numbers
			} else {
				sb.append(truncator.truncate(fields[def_._idx], def_._maxLen));
			}
			if (i < UdrDef.S_DEF.length - 1)
				sb.append(ULD_DELIMETER_NML);
		}
		sb.append(ULD_DELIMETER_EOL);
	}

	private String _getId() {
		if (RedisClusterPool.isCluster()) {
			return "{" + _eFields[E_04_A05_FILE_ID] + "}:" + _eFields[E_05_A06_LINE_NUM];
		}
		return _eFields[E_04_A05_FILE_ID] + ":" + _eFields[E_05_A06_LINE_NUM];
	}
}

