/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 1.2</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.resp;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;

/**
 * Class ErrorRecords.
 * 
 * @version $Revision$ $Date$
 */
public class ErrorRecords implements java.io.Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _errorRecordList.
     */
    private java.util.List _errorRecordList;


      //----------------/
     //- Constructors -/
    //----------------/

    public ErrorRecords() {
        super();
        this._errorRecordList = new java.util.ArrayList();
    }


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * 
     * 
     * @param vErrorRecord
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     */
    public void addErrorRecord(
            final ErrorRecord vErrorRecord)
    throws IndexOutOfBoundsException {
        this._errorRecordList.add(vErrorRecord);
    }

    /**
     * 
     * 
     * @param index
     * @param vErrorRecord
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     */
    public void addErrorRecord(
            final int index,
            final ErrorRecord vErrorRecord)
    throws IndexOutOfBoundsException {
        this._errorRecordList.add(index, vErrorRecord);
    }

    /**
     * Method enumerateErrorRecord.
     * 
     * @return an Enumeration over all possible elements of this
     * collection
     */
    public java.util.Enumeration enumerateErrorRecord(
    ) {
        return java.util.Collections.enumeration(this._errorRecordList);
    }

    /**
     * Method getErrorRecord.
     * 
     * @param index
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     * @return the value of the com.hp.xmlusage.resp.ErrorRecord at
     * the given index
     */
    public ErrorRecord getErrorRecord(
            final int index)
    throws IndexOutOfBoundsException {
        // check bounds for index
        if (index < 0 || index >= this._errorRecordList.size()) {
            throw new IndexOutOfBoundsException("getErrorRecord: Index value '" + index + "' not in range [0.." + (this._errorRecordList.size() - 1) + "]");
        }
        
        return (ErrorRecord) _errorRecordList.get(index);
    }

    /**
     * Method getErrorRecord.Returns the contents of the collection
     * in an Array.  <p>Note:  Just in case the collection contents
     * are changing in another thread, we pass a 0-length Array of
     * the correct type into the API call.  This way we <i>know</i>
     * that the Array returned is of exactly the correct length.
     * 
     * @return this collection as an Array
     */
    public ErrorRecord[] getErrorRecord(
    ) {
        ErrorRecord[] array = new ErrorRecord[0];
        return (ErrorRecord[]) this._errorRecordList.toArray(array);
    }

    /**
     * Method getErrorRecordCount.
     * 
     * @return the size of this collection
     */
    public int getErrorRecordCount(
    ) {
        return this._errorRecordList.size();
    }

    /**
     * Method isValid.
     * 
     * @return true if this object is valid according to the schema
     */
    public boolean isValid(
    ) {
        try {
            validate();
        } catch (org.exolab.castor.xml.ValidationException vex) {
            return false;
        }
        return true;
    }

    /**
     * Method iterateErrorRecord.
     * 
     * @return an Iterator over all possible elements in this
     * collection
     */
    public java.util.Iterator iterateErrorRecord(
    ) {
        return this._errorRecordList.iterator();
    }

    /**
     * 
     * 
     * @param out
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void marshal(
            final java.io.Writer out)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, out);
    }

    /**
     * 
     * 
     * @param handler
     * @throws java.io.IOException if an IOException occurs during
     * marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     */
    public void marshal(
            final org.xml.sax.ContentHandler handler)
    throws java.io.IOException, org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, handler);
    }

    /**
     */
    public void removeAllErrorRecord(
    ) {
        this._errorRecordList.clear();
    }

    /**
     * Method removeErrorRecord.
     * 
     * @param vErrorRecord
     * @return true if the object was removed from the collection.
     */
    public boolean removeErrorRecord(
            final ErrorRecord vErrorRecord) {
        boolean removed = _errorRecordList.remove(vErrorRecord);
        return removed;
    }

    /**
     * Method removeErrorRecordAt.
     * 
     * @param index
     * @return the element removed from the collection
     */
    public ErrorRecord removeErrorRecordAt(
            final int index) {
        Object obj = this._errorRecordList.remove(index);
        return (ErrorRecord) obj;
    }

    /**
     * 
     * 
     * @param index
     * @param vErrorRecord
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     */
    public void setErrorRecord(
            final int index,
            final ErrorRecord vErrorRecord)
    throws IndexOutOfBoundsException {
        // check bounds for index
        if (index < 0 || index >= this._errorRecordList.size()) {
            throw new IndexOutOfBoundsException("setErrorRecord: Index value '" + index + "' not in range [0.." + (this._errorRecordList.size() - 1) + "]");
        }
        
        this._errorRecordList.set(index, vErrorRecord);
    }

    /**
     * 
     * 
     * @param vErrorRecordArray
     */
    public void setErrorRecord(
            final ErrorRecord[] vErrorRecordArray) {
        //-- copy array
        _errorRecordList.clear();
        
        for (int i = 0; i < vErrorRecordArray.length; i++) {
                this._errorRecordList.add(vErrorRecordArray[i]);
        }
    }

    /**
     * Method unmarshal.
     * 
     * @param reader
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @return the unmarshaled com.hp.xmlusage.resp.ErrorRecords
     */
    public static ErrorRecords unmarshal(
            final java.io.Reader reader)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        return (ErrorRecords) Unmarshaller.unmarshal(ErrorRecords.class, reader);
    }

    /**
     * 
     * 
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void validate(
    )
    throws org.exolab.castor.xml.ValidationException {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    }

}
