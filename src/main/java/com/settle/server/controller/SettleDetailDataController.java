package com.settle.server.controller;

import com.google.common.base.Splitter;
import com.settle.server.service.SettleDetailDataGenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 */
@Slf4j
@RestController
@RequestMapping("/settleDetailData")
public class SettleDetailDataController {

    @Autowired
    private SettleDetailDataGenService service;

    /**
     * @param acctMonth 出账账期，如202309
     * @return
     */
    @GetMapping(value = "/handle")
    public String handle(@RequestParam(value = "acctMonth") String acctMonth,
                         @RequestParam(value = "rkey") String rkey) {
        try {
            List<String> rkeys = Splitter.on(",").splitToList(rkey);
            service.handleSpecialLineData(acctMonth, rkeys);
            return "success";
        } catch (Exception e) {
            log.error("SettleDetailDataController --->handle error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }


}
