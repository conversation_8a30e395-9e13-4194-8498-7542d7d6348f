server:
  port: 1401
  undertow:
    io-threads: 16
    worker-threads: 256
    buffer-size: 1024
    direct-buffers: true

log:
  config: classpath:logback-spring.xml
  path: /home/<USER>/workspace/logs/${spring.application.name}

jdbc:
  oracle:
    stludr:
      driverClassName: oracle.jdbc.OracleDriver
      url: ******************************************
      username: stludr
      password: <PERSON><PERSON>(y3UFOunZNZMuHjgMII60HpYN+XAw577J)
      maxActive: 300
      initialSize: 1
      minIdle: 0
      maxWait: 30000
      timeBetweenEvictionRunsMillis: 6000
      minEvictableIdleTimeMillis: 30000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
    stlusers:
      driverClassName: oracle.jdbc.OracleDriver
      url: ******************************************
      username: stlusers
      password: <PERSON><PERSON>(y3UFOunZNZMuHjgMII60HpYN+XAw577J)
      maxActive: 300
      initialSize: 1
      minIdle: 0
      maxWait: 30000
      timeBetweenEvictionRunsMillis: 6000
      minEvictableIdleTimeMillis: 30000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
#jar包放63结算主机连os的sftp
ftp:
  os:
    host: ************
    port: 12000
    username: BBSS
    password: ENC(TF67osQfz+NoPJN+spkQoAIUOk5DeXU5)
    remoteUploadPath:
      CARD: /incoming/bbssdcsy/CARD
    remoteDownloadPath:
      CARD: /outgoing/bbssdcsy/CARD
    tempDir: /home/<USER>/workspace/Cmiot/data/cardTemp
    bakDir: /home/<USER>/workspace/Cmiot/data/cardTemp_bak
    feedBackDir: /home/<USER>/workspace/Cmiot/data/cardTemp_feedBack
  ht:
    tempDir: /home/<USER>/workspace/Cmiot/data/bigDataTemp
    gzBakDir: /home/<USER>/workspace/Cmiot/data/bigDataGzTemp_bak
    bakDir: /home/<USER>/workspace/Cmiot/data/bigDataTemp_bak
    feedBackDir: /home/<USER>/workspace/Cmiot/data/bigDataTemp_feedBack