package com.settle.server.module.clearup.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/22
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "clear.log")
@NacosConfigurationProperties(dataId = "settle-service-tools", autoRefreshed = true)
@Data
public class ClearUpTableConfig {

    List<ClearLogDTO> tables;

    private Integer backTrackDay = 1;
}