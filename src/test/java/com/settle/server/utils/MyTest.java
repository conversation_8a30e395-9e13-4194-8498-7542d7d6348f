package com.settle.server.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.Setting;
import com.google.common.collect.Lists;
import com.settle.server.dto.XmlResp;
import com.settle.server.entity.esp.SyncInterfaceEspP2c;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.mnp.dto.FileType;
import com.settle.server.service.impl.MobileCloudServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.regex.Pattern;

import static com.settle.server.service.impl.IotSettServiceImpl.VERTICAL_BAR;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/2
 * @since 1.0.0
 */
@Slf4j
public class MyTest {


    @Test
    public void test1() {
        try {
            FileType value = FileType.valueOf("xx");
            System.out.println(value);
        } catch (IllegalArgumentException e) {
            // 当枚举值不存在时,valueOf会抛出IllegalArgumentException异常
            System.out.println("无效的枚举值: xx");
        }
    }

    @Test
    public void test2() {
        String json = "{\"acctMonth\":\"202203\"}";
        System.out.println(json);
        JSONObject entries = JSONUtil.parseObj(json);
        String acctMonth = entries.getStr("acctMonth");
        System.out.println(acctMonth);
    }

    @Test
    public void test3() {
        String s = "IOTSC_SETT_202404.001|10|-8000000";
        List<String> result = StrUtil.split(s, '|', false, false);
        System.out.println(result);

        String a = "202404|100011|01|210|12|||22|6|";
        List<String> r = StrUtil.split(a, '|', false, false);
        System.out.println(r.size());

    }

    @Test
    public void test4() {
        String s = "CMIOT_SETT_[0-9]{6}\\.[0-9]{3}.?";
        String file = "CMIOT_SETT_202404.002";
        System.out.println(file.matches(s));

    }

    @Test
    public void test5() {
        Integer n = -8000000;
        BigDecimal decimal = new BigDecimal(n);
        System.out.println(decimal);
    }

    @Test
    public void test6() {
        HashMap paramMap = new HashMap<>();
        paramMap.put("R_KEY", "10047");
        paramMap.put("settlemonth", "202401");
        paramMap.put("sign", "0317");
        String s = HttpUtil.get("http://127.0.0.1:9232/settleReport/erpService", paramMap);
        System.out.println(s);


    }

    @Test
    public void test7() {
        JSONObject entries = JSONUtil.parseObj("{}");
        int acctMonth = entries.getInt("acctMonth");
        System.out.println(acctMonth);
    }

    @Test
    public void test8() {
        String iotLine = "202404|1100011|10|371|12|||70215|6||";
        List<String> result = StrUtil.split(iotLine, VERTICAL_BAR, false, false);
        System.out.println(result);
        System.out.println(result.size());

        String[] split = iotLine.split("\\|", -1);
        System.out.println(split.length);

    }

    @Test
    public void test9() {
        Setting setting = new Setting("D:\\05code\\newsettle\\settle-service-tools\\src\\main\\resources\\application.properties");

        Set<String> strings = setting.keySet();
        for (String key : strings) {
            System.out.println(key);
        }
        List<String> groups = setting.getGroups();
        for (String group : groups) {
            System.out.println(group);
        }
    }

    @Test
    public void test10() {
        Setting setting = new Setting();
        HashMap<String, String> settingMap = new HashMap<>();
        settingMap.put("acctMonth", "202203");
        setting.putAll("CDN", settingMap);

        List<String> groups = setting.getGroups();
        for (String group : groups) {
            System.out.println(group);
        }

    }

    @Test
    public void test11() {
        String str = "1-1-1-4-1!!!!891!!!!1!!!!891A113720000505389!!!!luyc沈阳嘉泰市场经营（西藏）有限公司!!!!8910!!!!张林!!!!E8912021081750003935!!!!38910200022!!!!1010402!!!!移动云!!!!10000443605!!!!9207076!!!!快速营销!!!!2359!!!!其他集团SaaS产品!!!!99891500025!!!!!!!!111601!!!!202306!!!!!!!!ABcB!!!!单价!!!!1565!!!!移动云（SaaS）功能费!!!!060!!!!260000!!!!0.06!!!!14720!!!!245280!!!!1!!!!0!!!!260000!!!!130000!!!!功能费收入!!!!ZQ1118000077!!!!广州市单元信息科技有限公司!!!!0.5!!!!3!!!!65000!!!!0.2!!!!5!!!!9207076034!!!!个人版100G云空间月套餐!!!!第1档单价金额(元)--260-元;数量属性编码--0-0;第1档结束量--9999999999999999-个!!!!第1档单价金额(元)--130-元;数量属性编码---0;第1档结束量---个!!!!!!!!10051264132!!!!!!!!0!!!!!!!!!!!!!!!!10009!!!!SaaS!!!!1000120!!!!SaaS!!!!100012020138!!!!快速营销!!!!130400!!!!1!!!!移动云!!!!1!!!!20230601000000!!!!20230630235959!!!!CSMP!!!!移动云!!!!!!!!!!!!";
        String[] split = str.split("!!!!", -1);
        System.out.println(split.length);
        System.out.println(split[split.length - 1]);
        String[] split1 = str.split("!!!!");
        System.out.println(split1.length);
        System.out.println(split[split1.length - 1]);
        System.out.println(split1[66]);

    }

    @Test
    public void test12() {
        XmlResp xmlResp = new XmlResp();
        xmlResp.setOrgFileName("test");
        xmlResp.setRespDate(new Date());
        xmlResp.setFileStatus("0");
        xmlResp.setErrorCode("22");
        xmlResp.setErrorNode("xxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
        XmlResp.ErrorRecord errorRecord = new XmlResp.ErrorRecord();
        errorRecord.setErrorCode("22");
        errorRecord.setErrorNode("xxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
        XmlResp.ErrorRecord errorRecord2 = new XmlResp.ErrorRecord();

        errorRecord2.setErrorCode("21");
        errorRecord2.setErrorNode("aaaaaaaaaaaaaaa");
        xmlResp.setErrorRecords(Arrays.asList(errorRecord, errorRecord2));
        String s = JAXBUtil.objectToXML(xmlResp, false);
        System.out.println(s);
    }

    @Test
    public void test13() {
        String file = "D:\\000zy\\Desktop\\xslt\\报文\\4.16\\EBOSS_BILL_BBOSS_M_DETAIL_202403.0012.txt";
        List<String> lines = FileUtil.readLines(file, "UTF-8");
        System.out.println(lines.size());
        lines.removeAll(Arrays.asList("", null));
        System.out.println(lines.size());
    }

    @Test
    public void test14() {
        String line = "1-1!!!!P2C!!!!CY!!!!931!!!!1!!!!3!!!!800000!!!!1";
        System.out.println(line.contains("!!!!P2C!!!!"));
    }

    @Test
    public void test15() {
        String s = "CMIOT_SETT_[0-9]{6}\\.[0-9]{3}.*";
        String file = "CMIOT_SETT_202405.000.txt";
        System.out.println(file.matches(s));
    }

    @Test
    public void test16() {
        LocalDateTime now = LocalDateTime.now();
        // 上一个月
        LocalDateTime localDateTime = now.minusMonths(1);
        // 格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        String acctMonth = localDateTime.format(formatter);
        System.out.println(acctMonth);

        DateTime dateTime = DateUtil.offsetMonth(new Date(), -1);
        System.out.println(DateUtil.format(dateTime, "yyyyMM"));
    }

    @Test
    public void test17() {
        Path errorPath = Paths.get("D:\\billing\\settle\\workspace\\membership", "202406");
        System.out.println(errorPath.toString());
        File file = errorPath.resolve("fileName").toFile();
        System.out.println(file.getAbsolutePath());

    }

    @Test
    public void test18() {
        String line = "01011304#|#跨省互联网专线#|#111208#|#跨省互联网专线#|#9001482267#|#471680007471471000#|#内蒙古#|#471#|# #|#张智#|#13704716906#|#山东#|#531#|#济南#|# #|#济南大明湖#|#毕颖#|#15805311053#|#10Gbit/s#|#100#|#100#|# #|# ";
        String[] split = line.split("#\\|#", -1);
        System.out.println(split.length);
    }

    @Test
    public void test19() throws IOException {
        String filePath = "D:\\billing\\settle\\workspace\\data\\MNPSerer\\backupData\\20240620";
        File file = new File("D:\\billing\\settle\\workspace\\data\\MNPSerer\\data\\stl_baseinfo_internet_202405.111208");
        FileUtil.move(file, new File(filePath), true);

//        FileUtils.moveToDirectory(file, new File(filePath), true);
    }

    @Test
    public void test20() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(2, 2, 0, TimeUnit.SECONDS, new SynchronousQueue<>(), new BlockPolicy());
        ThreadPoolExecutor executor2 = new ThreadPoolExecutor(3, 3, 0, TimeUnit.SECONDS, new SynchronousQueue<>(), new BlockPolicy());

        for (int i = 1; i <= 5; i++) {
            int finalI = i;
            CompletableFuture.supplyAsync(() -> {
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                System.out.println(finalI);
                return finalI;

            }, executor).thenAcceptAsync(n -> {
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                System.out.println(n * n);
            }, executor2);
        }
        System.out.println("main");
        LockSupport.park();
    }

    @Test
    public void test21() {
        String settlemonth = "202406";
        String raqList = "10001,10002";
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("settlemonth", settlemonth);
        paramMap.put("raqList", raqList);
        String jsonStr = JSONUtil.toJsonStr(paramMap);
        System.out.println(jsonStr);
    }

    @Test
    public void test22() {
        String line = "2-1!!!!P2C!!!!CY!!!!931!!!!1!!!!3!!!!800000!!!!";
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        SyncInterfaceEspP2c espP2c = new SyncInterfaceEspP2c();
        espP2c.setId(split[0]);
        espP2c.setSettlementPartyIn(split[2]);
        espP2c.setSettlementPartyOut(split[3]);
        espP2c.setSettlementType(split[4]);
        espP2c.setSettlementRate(split[5]);
        espP2c.setSettlementAmount(StringUtils.isNotBlank(split[6]) ? Long.valueOf(split[6]) : null);
        espP2c.setSettleClass(null);
        espP2c.setFileName("xxx");
        espP2c.setStatus("0");

        System.out.println(espP2c.getSettleClass());
    }

    @Test
    public void test23() {
        String reg = "^a_10000_HDO_75822_[0-9]{6}_\\d{2}_\\d{3}\\.dat\\.gz";
        String file = "a_10000_HDO_75822_202408_00_001.dat.gz";
        System.out.println(file.matches(reg));

        String file2 = "a_10000_HDO_75824_202409_00_001.dat-0.gz";
        String reg2 = "^a_10000_HDO_75824_[0-9]{6}_\\d{2}_\\d{3}\\.dat.*gz";
        System.out.println(file2.matches(reg2));
    }

    @Test
    public void test24() {
        BigDecimal round = NumberUtil.round(1.05, 4);
        System.out.println(round);

        BigDecimal round1 = NumberUtil.round(1.52156, 4);
        System.out.println(round1);
    }

    @Test
    public void test25() throws InterruptedException {
        File csvFile = new File("D:\\billing\\settle\\workspace\\Bigdata\\resp", "aaa.csv");
        String line = "\t01,\t02,\t03";
        FileUtil.writeUtf8String(line, csvFile);
        TimeUnit.SECONDS.sleep(1);

        List<String> lines = FileUtil.readUtf8Lines(csvFile);
        for (String s : lines) {
            System.out.println(s);
        }
    }

    @Test
    public void test26() {
        File f = new File("D:\\billing\\settle\\workspace\\Bigdata\\resp\\PrePaymentSettlementStatement_202408.csv");
        List<String> lines = FileUtil.readUtf8Lines(f);
        for (String line : lines) {
            System.out.println(line);
        }
        System.out.println(lines.size());
    }

    @Test
    public void test27() {
        ArrayList<String> strings = Lists.newArrayList("aaa", "bbb");
        log.warn("item:{}", strings);
    }

    @Test
    public void test28() {
        String filename = "MembershipInterests_20241101.csv";
        String pattern = "MembershipInterests_[0-9]{8}\\.csv";
        Pattern p = Pattern.compile(pattern);
        boolean b = p.matcher(filename).find();
        System.out.println(b);
    }

    @Test
    public void test29() {
        DbNameEnum[] values = DbNameEnum.values();
        System.out.println(values);
        System.out.println(JSONUtil.toJsonStr(values));
        System.out.println(Arrays.toString(values));
        ExceptionCast.cast("dbName参数不合法,只支持以下参数: %s", Arrays.toString(values));
    }

    @Test
    public void test30() {
        int partitionNum = 6;
        String partitionFormat = "p%04d";
        //删除后 5 个分区
        DateTime now = DateTime.of(DateTime.now());
        ArrayList<String> partitionList = Lists.newArrayList();
        for (int i = 0; i < partitionNum; i++) {
            Integer mmdd = Integer.parseInt(DateUtil.format(DateUtil.offsetDay(now, i + 1), "MMdd"));
            String partition = String.format(partitionFormat, mmdd);
            partitionList.add(partition);
        }
        System.out.println(partitionList);

        ArrayList<String> partitionList2 = Lists.newArrayList();
        for (int i = 0; i < 1; i++) {
            Integer mm = Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(now, i + 1), "MM"));
            String partition = String.format("p%02d", mm);
            partitionList2.add(partition);
        }
        System.out.println(partitionList2);
    }

    @Test
    public void test31() {
        DateTime dateTime = DateUtil.offsetMonth(new Date(), 1);
        int month = DateUtil.month(new Date()) + 1;
        System.out.println(DateUtil.format(dateTime, "MM"));
        System.out.println(month);
    }

    @Test
    public void test32() {
        String s = "alter table {} truncate partition {}";
        String format = StrUtil.format(s, "aaa", "bbb");
        System.out.println(format);
    }

    @Test
    public void test33() {
        DateTime halfMonthDay = DateUtil.offsetDay(DateTime.now(), -15);
        DateTime twoMonthHalfOfDate = DateUtil.offsetMonth(halfMonthDay, -2);
        int partition = Integer.parseInt(DateUtil.format(twoMonthHalfOfDate, "MMdd"));
        System.out.println(twoMonthHalfOfDate);

        System.out.println(partition);

        String json = "{'a':[]}";
        JSONObject entries = JSONUtil.parseObj(json);
        List<String> a = entries.getBeanList("a", String.class);
        System.out.println(a);

    }

    @Test
    public void test34() {
        String start = "0501";
        String end = "0601";

        DateTime startDate = DateUtil.parse(start, "MMdd");
        DateTime endDate = DateUtil.parse(end, "MMdd");
        System.out.println(startDate + "-------" + endDate);
        DateRange range = DateUtil.range(startDate, endDate, DateField.DAY_OF_YEAR);
        range.forEach(System.out::println);
    }

    @Test
    public void test35() {
        List<String> partitions = Lists.newArrayList();
        partitions.add("p01");
        if (!partitions.isEmpty()) {
            partitions.clear();
        }
        String start = "0501";
        String end = "0601";
        DateTime startDate = DateUtil.parse(start, "MMdd");
        DateTime endDate = DateUtil.parse(end, "MMdd");
        List<String> finalPartitions = partitions;
        DateUtil.rangeConsume(startDate, endDate, DateField.DAY_OF_YEAR, date -> {
            String mmdd = DateUtil.format(date, "MMdd");
            finalPartitions.add("p" + mmdd);
        });
        System.out.println(partitions);
    }

    @Test
    public void test36() {
        //冒泡排序
        int[] arr = {3, 1, 2, 5, 4};
        for (int i = 0; i < arr.length - 1; i++) {
            for (int j = 0; j < arr.length - 1 - i; j++) {
                if (arr[j] > arr[j + 1]) {
                    int temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
        System.out.println(Arrays.toString(arr));
    }

}