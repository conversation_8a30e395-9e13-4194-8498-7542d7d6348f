package com.settle.server.module.report.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/9
 * @since 1.0.0
 */
@Service
public class ReportFileBeanFactory {

    @Autowired
    private Map<String, ReportFileService> reportFileServiceMap;


    public ReportFileService getReportFileService(ReportFileType type) {
        return reportFileServiceMap.get(type.getBeanName());
    }


    public enum ReportFileType {
        ERP("erpReportFileService"),
        PREDICTION("predictionReportFileService"),
        EVICTREPORT("evictReportFileService"),
        ;
        private String beanName;

        ReportFileType(String beanName) {
            this.beanName = beanName;
        }

        public String getBeanName() {
            return beanName;
        }
    }
}