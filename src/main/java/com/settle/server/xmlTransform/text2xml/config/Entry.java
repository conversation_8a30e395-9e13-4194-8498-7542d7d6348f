/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 1.2</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.text2xml.config;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;

/**
 * Class Entry.
 * 
 * @version $Revision$ $Date$
 */
public class Entry implements java.io.Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _name.
     */
    private String _name;

    /**
     * Field _type.
     */
    private String _type;

    /**
     * Field _basepath.
     */
    private String _basepath;

    /**
     * Field _in.
     */
    private String _in;

    /**
     * Field _out.
     */
    private String _out;

    /**
     * Field _back.
     */
    private String _back;

    /**
     * Field _regexp.
     */
    private String _regexp;


      //----------------/
     //- Constructors -/
    //----------------/

    public Entry() {
        super();
    }


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * Returns the value of field 'back'.
     * 
     * @return the value of field 'Back'.
     */
    public String getBack(
    ) {
        return this._back;
    }

    /**
     * Returns the value of field 'basepath'.
     * 
     * @return the value of field 'Basepath'.
     */
    public String getBasepath(
    ) {
        return this._basepath;
    }

    /**
     * Returns the value of field 'in'.
     * 
     * @return the value of field 'In'.
     */
    public String getIn(
    ) {
        return this._in;
    }

    /**
     * Returns the value of field 'name'.
     * 
     * @return the value of field 'Name'.
     */
    public String getName(
    ) {
        return this._name;
    }

    /**
     * Returns the value of field 'out'.
     * 
     * @return the value of field 'Out'.
     */
    public String getOut(
    ) {
        return this._out;
    }

    /**
     * Returns the value of field 'regexp'.
     * 
     * @return the value of field 'Regexp'.
     */
    public String getRegexp(
    ) {
        return this._regexp;
    }

    /**
     * Returns the value of field 'type'.
     * 
     * @return the value of field 'Type'.
     */
    public String getType(
    ) {
        return this._type;
    }

    /**
     * Method isValid.
     * 
     * @return true if this object is valid according to the schema
     */
    public boolean isValid(
    ) {
        try {
            validate();
        } catch (org.exolab.castor.xml.ValidationException vex) {
            return false;
        }
        return true;
    }

    /**
     * 
     * 
     * @param out
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void marshal(
            final java.io.Writer out)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, out);
    }

    /**
     * 
     * 
     * @param handler
     * @throws java.io.IOException if an IOException occurs during
     * marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     */
    public void marshal(
            final org.xml.sax.ContentHandler handler)
    throws java.io.IOException, org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, handler);
    }

    /**
     * Sets the value of field 'back'.
     * 
     * @param back the value of field 'back'.
     */
    public void setBack(
            final String back) {
        this._back = back;
    }

    /**
     * Sets the value of field 'basepath'.
     * 
     * @param basepath the value of field 'basepath'.
     */
    public void setBasepath(
            final String basepath) {
        this._basepath = basepath;
    }

    /**
     * Sets the value of field 'in'.
     * 
     * @param in the value of field 'in'.
     */
    public void setIn(
            final String in) {
        this._in = in;
    }

    /**
     * Sets the value of field 'name'.
     * 
     * @param name the value of field 'name'.
     */
    public void setName(
            final String name) {
        this._name = name;
    }

    /**
     * Sets the value of field 'out'.
     * 
     * @param out the value of field 'out'.
     */
    public void setOut(
            final String out) {
        this._out = out;
    }

    /**
     * Sets the value of field 'regexp'.
     * 
     * @param regexp the value of field 'regexp'.
     */
    public void setRegexp(
            final String regexp) {
        this._regexp = regexp;
    }

    /**
     * Sets the value of field 'type'.
     * 
     * @param type the value of field 'type'.
     */
    public void setType(
            final String type) {
        this._type = type;
    }

    /**
     * Method unmarshal.
     * 
     * @param reader
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @return the unmarshaled com.hp.xmlusage.text2xml.config.Entry
     */
    public static Entry unmarshal(
            final java.io.Reader reader)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        return (Entry) Unmarshaller.unmarshal(Entry.class, reader);
    }

    /**
     * 
     * 
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void validate(
    )
    throws org.exolab.castor.xml.ValidationException {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    }

}
