package com.hp.cmcc.bboss.settle.app.validation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stl_customer")
public class StlCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("CUSTOMER_ID")
    private BigDecimal customerId;

    @TableField("EFFECTIVE_DATE")
    private LocalDateTime effectiveDate;

    @TableField("FIRST_NAME")
    private String firstName;

    @TableField("EXPIRY_DATE")
    private LocalDateTime expiryDate;

    @TableField("CUSTOMER_CODE")
    private String customerCode;

    @TableField("CREATION_DATE")
    private LocalDateTime creationDate;

    @TableField("EXTERNAL_CUSTOMER_ID")
    private String externalCustomerId;

    @TableField("PROVINCE_CODE")
    private String provinceCode;

    @TableField("ID")
    private String id;

    @TableField("location")
    private String location;

    /**
     * 县区单位核算编码
     */
    @TableField("COUNTY_CODE")
    private String countyCode;


}
