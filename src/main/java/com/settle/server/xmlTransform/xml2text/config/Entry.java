/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 0.9.6</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.xml2text.config;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import java.io.IOException;
import java.io.Reader;
import java.io.Serializable;
import java.io.Writer;
import org.exolab.castor.xml.MarshalException;
import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;
import org.exolab.castor.xml.ValidationException;
import org.xml.sax.ContentHandler;

/**
 * Class Entry.
 * 
 * @version $Revision$ $Date$
 */
public class Entry implements Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _name
     */
    private String _name;

    /**
     * Field _basepath
     */
    private String _basepath;

    /**
     * Field _in
     */
    private String _in;

    /**
     * Field _out
     */
    private String _out;

    /**
     * Field _back
     */
    private String _back;

    /**
     * Field _resp
     */
    private String _resp;

    /**
     * Field _xslt
     */
    private String _xslt;

    /**
     * Field _xsd
     */
    private String _xsd;

    /**
     * Field _regexp
     */
    private String _regexp;


      //----------------/
     //- Constructors -/
    //----------------/

    public Entry() {
        super();
    } //-- Entry()


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * Returns the value of field 'back'.
     * 
     * @return String
     * @return the value of field 'back'.
     */
    public String getBack()
    {
        return this._back;
    } //-- java.lang.String getBack() 

    /**
     * Returns the value of field 'basepath'.
     * 
     * @return String
     * @return the value of field 'basepath'.
     */
    public String getBasepath()
    {
        return this._basepath;
    } //-- java.lang.String getBasepath() 

    /**
     * Returns the value of field 'in'.
     * 
     * @return String
     * @return the value of field 'in'.
     */
    public String getIn()
    {
        return this._in;
    } //-- java.lang.String getIn() 

    /**
     * Returns the value of field 'name'.
     * 
     * @return String
     * @return the value of field 'name'.
     */
    public String getName()
    {
        return this._name;
    } //-- java.lang.String getName() 

    /**
     * Returns the value of field 'out'.
     * 
     * @return String
     * @return the value of field 'out'.
     */
    public String getOut()
    {
        return this._out;
    } //-- java.lang.String getOut() 

    /**
     * Returns the value of field 'regexp'.
     * 
     * @return String
     * @return the value of field 'regexp'.
     */
    public String getRegexp()
    {
        return this._regexp;
    } //-- java.lang.String getRegexp() 

    /**
     * Returns the value of field 'resp'.
     * 
     * @return String
     * @return the value of field 'resp'.
     */
    public String getResp()
    {
        return this._resp;
    } //-- java.lang.String getResp() 

    /**
     * Returns the value of field 'xsd'.
     * 
     * @return String
     * @return the value of field 'xsd'.
     */
    public String getXsd()
    {
        return this._xsd;
    } //-- java.lang.String getXsd() 

    /**
     * Returns the value of field 'xslt'.
     * 
     * @return String
     * @return the value of field 'xslt'.
     */
    public String getXslt()
    {
        return this._xslt;
    } //-- java.lang.String getXslt() 

    /**
     * Method isValid
     * 
     * 
     * 
     * @return boolean
     */
    public boolean isValid()
    {
        try {
            validate();
        }
        catch (ValidationException vex) {
            return false;
        }
        return true;
    } //-- boolean isValid() 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param out
     */
    public void marshal(Writer out)
        throws MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, out);
    } //-- void marshal(java.io.Writer) 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param handler
     */
    public void marshal(ContentHandler handler)
        throws IOException, MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, handler);
    } //-- void marshal(org.xml.sax.ContentHandler) 

    /**
     * Sets the value of field 'back'.
     * 
     * @param back the value of field 'back'.
     */
    public void setBack(String back)
    {
        this._back = back;
    } //-- void setBack(java.lang.String) 

    /**
     * Sets the value of field 'basepath'.
     * 
     * @param basepath the value of field 'basepath'.
     */
    public void setBasepath(String basepath)
    {
        this._basepath = basepath;
    } //-- void setBasepath(java.lang.String) 

    /**
     * Sets the value of field 'in'.
     * 
     * @param in the value of field 'in'.
     */
    public void setIn(String in)
    {
        this._in = in;
    } //-- void setIn(java.lang.String) 

    /**
     * Sets the value of field 'name'.
     * 
     * @param name the value of field 'name'.
     */
    public void setName(String name)
    {
        this._name = name;
    } //-- void setName(java.lang.String) 

    /**
     * Sets the value of field 'out'.
     * 
     * @param out the value of field 'out'.
     */
    public void setOut(String out)
    {
        this._out = out;
    } //-- void setOut(java.lang.String) 

    /**
     * Sets the value of field 'regexp'.
     * 
     * @param regexp the value of field 'regexp'.
     */
    public void setRegexp(String regexp)
    {
        this._regexp = regexp;
    } //-- void setRegexp(java.lang.String) 

    /**
     * Sets the value of field 'resp'.
     * 
     * @param resp the value of field 'resp'.
     */
    public void setResp(String resp)
    {
        this._resp = resp;
    } //-- void setResp(java.lang.String) 

    /**
     * Sets the value of field 'xsd'.
     * 
     * @param xsd the value of field 'xsd'.
     */
    public void setXsd(String xsd)
    {
        this._xsd = xsd;
    } //-- void setXsd(java.lang.String) 

    /**
     * Sets the value of field 'xslt'.
     * 
     * @param xslt the value of field 'xslt'.
     */
    public void setXslt(String xslt)
    {
        this._xslt = xslt;
    } //-- void setXslt(java.lang.String) 

    /**
     * Method unmarshal
     * 
     * 
     * 
     * @param reader
     * @return Object
     */
    public static Object unmarshal(Reader reader)
        throws MarshalException, ValidationException
    {
        return (Entry) Unmarshaller.unmarshal(Entry.class, reader);
    } //-- java.lang.Object unmarshal(java.io.Reader) 

    /**
     * Method validate
     * 
     */
    public void validate()
        throws ValidationException
    {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    } //-- void validate() 

}
