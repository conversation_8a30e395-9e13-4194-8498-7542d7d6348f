package com.settle.server.module.bigdata.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.settle.server.dao.stludr.bigdata.BigDataFileLogMapper;
import com.settle.server.entity.bigdata.BigDataFileLog;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Service
public class BigDataFileLogService extends ServiceImpl<BigDataFileLogMapper, BigDataFileLog>  {

    public void updateLog(BigDataFileLog dataFileLog) {
        getBaseMapper().update(null, new LambdaUpdateWrapper<>(BigDataFileLog.class)
                .set(BigDataFileLog::getFileStatus, dataFileLog.getFileStatus())
                .set(BigDataFileLog::getMessage, dataFileLog.getMessage())
                .set(BigDataFileLog::getUpdateTime, LocalDateTime.now())
                .eq(BigDataFileLog::getId, dataFileLog.getId()));
    }

    public void saveOne(BigDataFileLog log) {
        getBaseMapper().insert(log);
    }
}
