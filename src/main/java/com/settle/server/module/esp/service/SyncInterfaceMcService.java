package com.settle.server.module.esp.service;

import cn.hutool.core.util.IdUtil;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.SyncInterfaceMcDao;
import com.settle.server.dao.stludr.SyncInterfaceMcNMGDao;
import com.settle.server.dao.stludr.SyncInterfaceMcP2CDao;
import com.settle.server.dao.stludr.SyncInterfaceMcP2PDao;
import com.settle.server.entity.SyncInterfaceMc;
import com.settle.server.entity.SyncInterfaceMcNMG;
import com.settle.server.entity.SyncInterfaceMcP2C;
import com.settle.server.entity.SyncInterfaceMcP2P;
import com.settle.server.module.esp.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/16
 * @since 1.0.0
 */
@Service
@Slf4j
public class SyncInterfaceMcService {
    @Autowired
    private SyncInterfaceMcDao syncInterfaceMcDao;

    @Autowired
    private SyncInterfaceMcP2PDao syncInterfaceMcP2PDao;

    @Autowired
    private SyncInterfaceMcP2CDao syncInterfaceMcP2CDao;

    @Autowired
    private SyncInterfaceMcNMGDao syncInterfaceMcNMGDao;

    public void saveSyncInterfaceMc(String month, List<SyncInterfaceMc> syncInterfaceMcLst) {
        if (CollectionUtils.isEmpty(syncInterfaceMcLst)) {
            return;
        }
        Lists.partition(syncInterfaceMcLst, 1000).forEach(list -> {
            syncInterfaceMcDao.insertBatch(month, list);
        });

    }

    public void saveSyncInterfaceMcP2P(String month, List<SyncInterfaceMcP2P> p2pLst) {
        if (CollectionUtils.isEmpty(p2pLst)) {
            return;
        }
        Lists.partition(p2pLst, 1000).forEach(list -> {
            syncInterfaceMcP2PDao.insertBatch(month, list);
        });
    }

    public void saveSyncInterfaceMcP2C(String month, List<SyncInterfaceMcP2C> p2cList) {
        if (CollectionUtils.isEmpty(p2cList)) {
            return;
        }
        Lists.partition(p2cList, 1000).forEach(list -> {
            syncInterfaceMcP2CDao.insertBatch(month, list);
        });
    }
    public void saveSyncInterfaceMcNMG(String month, List<SyncInterfaceMcNMG> syncInterfaceMcNMGLst) {
        if (CollectionUtils.isEmpty(syncInterfaceMcNMGLst)) {
            return;
        }
        Lists.partition(syncInterfaceMcNMGLst, 1000).forEach(list -> {
            syncInterfaceMcNMGDao.insertBatch(month, list);
        });
    }
    public void updateStatus23(String month,String filename) {
        //更新状态为23
        syncInterfaceMcDao.updateStatusByAcctMonth(month,filename);
        syncInterfaceMcP2CDao.updateStausByAcctMonth(month,filename);
        syncInterfaceMcP2PDao.updateStausByAcctMonth(month,filename);
    }

    public void updateStatus24(String acctMonth,String filename) {
        //更新状态为24
        syncInterfaceMcDao.updateStatusByAcctMonthAndProductId(acctMonth,filename);
        syncInterfaceMcP2CDao.updateStatusByFileNameAndId(acctMonth,filename);
        syncInterfaceMcP2PDao.updateStatusByFileNameAndId(acctMonth,filename);
    }

    public void updateStatus25(String acctMonth,String filename) {
        //更新状态为25
        syncInterfaceMcDao.updateStatusByOrderMode(acctMonth,filename);
        syncInterfaceMcP2CDao.updateStatusByFileName(acctMonth,filename);
        syncInterfaceMcP2PDao.updateStatusByFileName(acctMonth,filename);
    }

    public void updateStatus99(String acctMonth,String filename) {
        //更新状态为99
        syncInterfaceMcDao.updateStatusByAcctMonthAndId(acctMonth,filename);
        syncInterfaceMcP2CDao.updateStatusByMcStatus(acctMonth,filename);
        syncInterfaceMcP2PDao.updateStatusByMcStatus(acctMonth,filename);
    }

    public void updateStatus0(String acctMonth,String filename) {
        //更新状态为正常状态
        syncInterfaceMcP2CDao.updateStausByStatus(acctMonth,filename);
        syncInterfaceMcP2PDao.updateStausByStatus(acctMonth,filename);
        syncInterfaceMcDao.updateStatusModeByAcctMonthAndStatus(acctMonth,filename);
    }

    public void updateOrderMode(String acctMonth,String filename) {
        syncInterfaceMcDao.updateOrderModeByAcctMonth(acctMonth,filename);

    }

    public void deleteByFileNameMonth(String acctMonth, String fileName) {
        syncInterfaceMcDao.deleteByFileNameMonth(acctMonth, fileName);
        syncInterfaceMcP2CDao.deleteByFileNameMonth(acctMonth, fileName);
        syncInterfaceMcP2PDao.deleteByFileNameMonth(acctMonth, fileName);
        syncInterfaceMcNMGDao.deleteByFileNameMonth(acctMonth, fileName);
    }

    public void deleteMcByMonth(String acctMonth,String filename) {
        syncInterfaceMcDao.deleteMcByMonth(acctMonth,filename);
        syncInterfaceMcDao.deleteMcP2PByMonth(acctMonth,filename);
        syncInterfaceMcDao.deleteMcP2CByMonth(acctMonth,filename);
        syncInterfaceMcDao.deleteMcP2CNMGByMonth(acctMonth,filename);
    }

    public List<SyncInterfaceMc> queryErrorByFileName(String acctMonth, String fileName) {
        return syncInterfaceMcDao.queryErrorByFileName(acctMonth, fileName);
    }

    public void optP2C(String acctMonth, String fileName, List<SyncInterfaceMcP2C> syncInterfaceMcP2CLst, String line) {
        String[] split = line.split(Constant.FOUR_BAR,-1);
        if (split.length == 8) {
            SyncInterfaceMcP2C syncInterfaceMcP2C = new SyncInterfaceMcP2C();
            syncInterfaceMcP2C.setId(split[0]);
            syncInterfaceMcP2C.setSettlementPartyIn(split[2]);
            syncInterfaceMcP2C.setSettlementPartyOut(split[3]);
            syncInterfaceMcP2C.setSettlementRate(split[4]);
            syncInterfaceMcP2C.setSettlementType(split[5]);
            syncInterfaceMcP2C.setSettlementAmount(split[6]);
            syncInterfaceMcP2C.setProSettleDisvalue(split[7]);
            syncInterfaceMcP2C.setFileName(fileName);
            syncInterfaceMcP2CLst.add(syncInterfaceMcP2C);
        }else {
            log.info("P2C 内容长度不对:{} acctMonth:{}", fileName, acctMonth);
            throw new RuntimeException(split[0] + "长度不正确！");
        }
    }

    public void optP2P(String acctMonth, String fileName, List<SyncInterfaceMcP2P> syncInterfaceMcP2PLst, String line) {
        String[] split = line.split(Constant.FOUR_BAR,-1);
        if (split.length == 7) {
            SyncInterfaceMcP2P syncInterfaceMcP2P = new SyncInterfaceMcP2P();
            syncInterfaceMcP2P.setId(split[0]);
            syncInterfaceMcP2P.setSettlementPartyIn(split[2]);
            syncInterfaceMcP2P.setSettlementPartyOut(split[3]);
            syncInterfaceMcP2P.setSettlementRate(split[4]);
            syncInterfaceMcP2P.setSettlementType(split[5]);
            syncInterfaceMcP2P.setSettlementAmount(split[6]);
            syncInterfaceMcP2P.setFileName(fileName);
            syncInterfaceMcP2PLst.add(syncInterfaceMcP2P);
        }else{
            log.info("P2P内容长度不对:{} acctMonth:{}", fileName, acctMonth);
            throw new RuntimeException(split[0] + "长度不正确！");
        }
    }

    public void optNMG(String acctMonth, String fileName, List<SyncInterfaceMcNMG> syncInterfaceMcNMGLst, String line) {
        String[] split = line.split(Constant.FOUR_BAR,-1);
        //if (split.length == 8) {
            SyncInterfaceMcNMG syncInterfaceMcNMG = new SyncInterfaceMcNMG();
            syncInterfaceMcNMG.setId(IdUtil.getSnowflakeNextIdStr());
            syncInterfaceMcNMG.setParentId(split[0]);
            syncInterfaceMcNMG.setSettlementPartyIn(split[2]);
            syncInterfaceMcNMG.setSettlementPartyOutType(split[3]);
            syncInterfaceMcNMG.setSettlementPartyOut(split[4]);
            syncInterfaceMcNMG.setSettlementRate(split[5]);
            syncInterfaceMcNMG.setSettlementType(split[6]);
            syncInterfaceMcNMG.setSettlementAmount(split[7]);
            syncInterfaceMcNMG.setProSettleDisvalue(split[8]);
            syncInterfaceMcNMG.setFileName(fileName);
            syncInterfaceMcNMG.setStatus("0");
            syncInterfaceMcNMGLst.add(syncInterfaceMcNMG);
        //}else {
        //    log.info("NMG 内容长度不对:{} acctMonth:{}", fileName, acctMonth);
        //    throw new RuntimeException(split[0] + "长度不正确！");
        //}
    }

    public void optMc(String acctMonth, String fileName, List<SyncInterfaceMc> syncInterfaceMcs, String line) {
        String[] split = line.split(Constant.FOUR_BAR,-1);
        if (split.length == 72) {
            SyncInterfaceMc syncInterfaceMc = new SyncInterfaceMc();
            syncInterfaceMc.setId(split[0]);
            syncInterfaceMc.setProvCode(split[1]);
            if ("000".equals(split[1])){
                syncInterfaceMc.setOrderMode("1");
            }else {
                syncInterfaceMc.setOrderMode("3");
            }
            syncInterfaceMc.setPayTag(split[2]);
            syncInterfaceMc.setCustomerProvinceNumber(split[3]);
            syncInterfaceMc.setCustomerName(split[4]);
            syncInterfaceMc.setCityCode(split[5]);
            syncInterfaceMc.setCreatorName(split[6]);
            syncInterfaceMc.setEcId(split[7]);
            syncInterfaceMc.setPoSubsId(split[8]);
            syncInterfaceMc.setPoId(split[9]);
            syncInterfaceMc.setPoName(split[10]);
            syncInterfaceMc.setProductSubsId(split[11]);
            syncInterfaceMc.setProductId(split[12]);
            syncInterfaceMc.setProductName(split[13]);
            syncInterfaceMc.setDbProductId(split[14]);
            syncInterfaceMc.setDbProductName(split[15]);
            syncInterfaceMc.setCoProductId(split[16]);
            syncInterfaceMc.setBlProductId(split[17]);
            syncInterfaceMc.setOneProductId(split[18]);
            syncInterfaceMc.setBillingTerm(split[19]);
            syncInterfaceMc.setPayTerm(split[20]);
            syncInterfaceMc.setProdChargeCode(split[21]);
            syncInterfaceMc.setPoChargeCodeName(split[22]);
            syncInterfaceMc.setOneProChargeCode(split[23]);
            syncInterfaceMc.setOneProChargeName(split[24]);
            syncInterfaceMc.setDbProdChargeCode(split[25]);
            syncInterfaceMc.setFeeVal(split[26]);
            syncInterfaceMc.setTaxRate(split[27]);
            syncInterfaceMc.setTax(split[28]);
            syncInterfaceMc.setFeeNoTax(split[29]);
            syncInterfaceMc.setFeeFlag(split[30]);
            syncInterfaceMc.setDiscountAmount(split[31]);
            syncInterfaceMc.setStandardFee(split[32]);
            syncInterfaceMc.setSettleFee(split[33]);
            syncInterfaceMc.setGhFeeType(split[34]);
            syncInterfaceMc.setPartnerCode(split[35]);
            syncInterfaceMc.setPartnerName(split[36]);
            syncInterfaceMc.setParSettleRate(split[37]);
            syncInterfaceMc.setSettlementType(split[38]);
            if (StringUtils.isBlank(split[39]) || "null".equals(split[39])){
                split[39] = null;
            }
            syncInterfaceMc.setParSettlAmount(split[39]);
            syncInterfaceMc.setParResSettlRate(split[40]);
            syncInterfaceMc.setSettlementClass(split[41]);
            syncInterfaceMc.setRateplanId(split[42]);
            syncInterfaceMc.setRateplanName(split[43]);
            syncInterfaceMc.setStandardSalePrice(split[44]);
            syncInterfaceMc.setSettlePrice(split[45]);
            syncInterfaceMc.setOriginalBillMonth(split[46]);
            syncInterfaceMc.setFeeSeq(split[47]);
            syncInterfaceMc.setMemberNums(split[48]);
            syncInterfaceMc.setIfFreeResource(split[49]);
            syncInterfaceMc.setDiscountType(split[50]);
            syncInterfaceMc.setSettleDisvalue(split[51]);
            syncInterfaceMc.setBsType(split[52]);
            syncInterfaceMc.setRatePlanGhCode(split[53]);
            syncInterfaceMc.setRatePlanGhName(split[54]);
            syncInterfaceMc.setOnProductCode(split[55]);
            syncInterfaceMc.setOnProductName(split[56]);
            syncInterfaceMc.setEjProductCode(split[57]);
            syncInterfaceMc.setEjProductName(split[58]);
            syncInterfaceMc.setSjProductCode(split[59]);
            syncInterfaceMc.setSjProductName(split[60]);
            syncInterfaceMc.setGhCode(split[61]);
            syncInterfaceMc.setProductClass(split[62]);
            syncInterfaceMc.setProductReportName(split[63]);
            syncInterfaceMc.setProductReportItemName(split[64]);
            syncInterfaceMc.setIssueTime(split[65]);
            syncInterfaceMc.setExpireTime(split[66]);
            syncInterfaceMc.setProductType(split[67]);
            syncInterfaceMc.setBusiType(split[68]);
            syncInterfaceMc.setContractMain(split[69]);
            syncInterfaceMc.setReserved1(split[70]);
            syncInterfaceMc.setReserved2(split[71]);
            syncInterfaceMc.setOrderMode("1");
            syncInterfaceMc.setFileName(fileName);
            syncInterfaceMcs.add(syncInterfaceMc);
        }else {
            log.info("4.16接口文件:{},长度不正确", fileName);
            throw new RuntimeException(split[0] + "长度不正确！");
        }
    }
}