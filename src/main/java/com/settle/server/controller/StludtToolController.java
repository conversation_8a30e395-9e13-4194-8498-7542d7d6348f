package com.settle.server.controller;


import com.settle.server.stludrtool.DatVerfRun;
import com.settle.server.stludrtool.InfRun;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class StludtToolController {


    @PostMapping("/test222")
    public  void StludtToolMain(HttpServletRequest req, @RequestParam Map<String,Object> request){
        String arg= (String) request.get("request");
        List<String> args= Arrays.asList(arg.split(","));
        log.info("*** *** 执行参数: " + StringUtils.join(args, ","));
        if (args.size() == 2 && args.get(0).equalsIgnoreCase("A")) {
            // 由经分提供 运营销售支撑结算类数据
            DatVerfRun dr = new DatVerfRun();
            dr.insertDBData(args.get(0));
        }
        if (args.size() == 2 && args.get(0).equalsIgnoreCase("B")) {
            //inf_00集团短信直销MAS结算数据入库
            InfRun ir = new InfRun();
            ir.insertDBData(args.get(1));
        }
    }

}
