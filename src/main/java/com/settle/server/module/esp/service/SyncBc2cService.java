package com.settle.server.module.esp.service;

import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.SyncInterfaceBc2cPARTDao;
import com.settle.server.dao.stludr.SyncInterfaceBc2cPSDao;
import com.settle.server.entity.SyncInterfaceBc2cPS;
import com.settle.server.entity.SyncInterfaceBc2cPart;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/7/5
 * @since 1.0.0
 */
@Service
public class SyncBc2cService {

    @Autowired
    private SyncInterfaceBc2cPSDao bc2cPSDao;
    @Autowired
    private SyncInterfaceBc2cPARTDao bc2cPARTDao;


    public void saveSyncInterfaceBc2cPS(String acctMonth, List<SyncInterfaceBc2cPS> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Lists.partition(list, 500).forEach(l -> bc2cPSDao.insertBatch(l));
    }

    public void saveSyncInterfaceBc2cPART(String acctMonth, List<SyncInterfaceBc2cPart> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Lists.partition(list, 500).forEach(l -> bc2cPARTDao.insertBatch(l));
    }
}