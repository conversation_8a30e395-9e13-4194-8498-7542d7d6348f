package com.settle.server.module.clearup.config;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.PartitionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/26
 * @since 1.0.0
 */
@Component
@Slf4j
public class ClearTabBizCache {

    public static HashBasedTable<DbNameEnum, PartitionTypeEnum, Set<String>> table = HashBasedTable.create();

    @Autowired
    private ClearUpTableConfig config;

    static {
        table.put(DbNameEnum.STTLBIZ, PartitionTypeEnum.DAILY, Sets.newHashSet("run_log","raw_alm","rmtdis_log"));
        table.put(DbNameEnum.STTLBIZ, PartitionTypeEnum.MONTHLY,Sets.newHashSet("udr2mq_log","erruld_log","dblodr_log","udruld_file_log","udruld_proc_log"));

        table.put(DbNameEnum.STLUSERS, PartitionTypeEnum.DAILY, Sets.newHashSet("run_log","raw_alm","rmtdis_log"));
        table.put(DbNameEnum.STLUSERS, PartitionTypeEnum.MONTHLY, Sets.newHashSet("udr2mq_log","erruld_log","dblodr_log","udruld_file_log","udruld_proc_log"));

        table.put(DbNameEnum.STLUDR, PartitionTypeEnum.DAILY, Sets.newHashSet("run_log","dupchk_udr"));
    }

    @PostConstruct
    public void init() {
        List<ClearLogDTO> tables = config.getTables();
        if (CollectionUtil.isEmpty(tables)) {
            return;
        }
        config.getTables().forEach(clearLogDTO -> {
            DbNameEnum dbNameEnum = DbNameEnum.getDbName(clearLogDTO.getDbName().toUpperCase());
            if (dbNameEnum == null) {
                log.error("dbName参数不合法,只支持以下参数: {}", Arrays.toString(DbNameEnum.values()));
                return;
            }
            PartitionTypeEnum partitionTypeEnum = PartitionTypeEnum.getByType(clearLogDTO.getPartitionType().toLowerCase());
            if (partitionTypeEnum == null) {
                log.error("partitionType参数不合法,只支持以下参数: {}", Arrays.toString(PartitionTypeEnum.values()));
                return;
            }
            Set<String> _tables = table.get(dbNameEnum, partitionTypeEnum);
            if (CollectionUtil.isEmpty(_tables)) {
                _tables = new HashSet<>();
            }
            _tables.add(clearLogDTO.getTableName());
            table.put(dbNameEnum, partitionTypeEnum, _tables);
        });
    }
}