<?xml version="1.0" encoding="UTF-8"?>
<!--
- author:   <PERSON> Kitt
- date:     2020/09/24
- verson:   v1.0
- descprition:
    this file is used to transform the incoming inventory XML file to txt file.
-->
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:fn="http://www.w3.org/2005/xpath-functions" exclude-result-prefixes="fn xs xsi xsl">
	<xsl:output method="text" encoding="UTF-8" media-type="text/plain"/>
	<xsl:variable name="sep">
		<xsl:text>!!!!</xsl:text>
	</xsl:variable>
	<xsl:variable name="zero">
		<xsl:text>0</xsl:text>
	</xsl:variable>
	<xsl:variable name="hyphen">
		<xsl:text>-</xsl:text>
	</xsl:variable>
	<xsl:variable name="crlf">
		<xsl:text>&#xd;&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="carr">
		<xsl:text>&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="P2P">
		<xsl:text>P2P</xsl:text>
	</xsl:variable>
	<xsl:variable name="P2C">
		<xsl:text>P2C</xsl:text>
	</xsl:variable>
	<xsl:variable name="NMG">
		<xsl:text>NMG_TAG</xsl:text>
	</xsl:variable>
      
    <xsl:template match="/BillList">
		<xsl:variable name="v_ProvCode" select="ProvCode"/>
		<xsl:variable name="v_PayTag" select="PayTag"/>
		<xsl:for-each select="ECList">
			<xsl:for-each select="ECInfo">
				<xsl:variable name="v_CustomerProvinceNumber" select="CustomerProvinceNumber"/>
				<xsl:variable name="v_CustomerName" select="CustomerName"/>
				<xsl:variable name="v_CityCode" select="CityCode"/>
				<xsl:variable name="v_CreatorName" select="CreatorName"/>
				<xsl:variable name="v_ECid" select="ECid"/>
				<xsl:variable name="pos_ECInfo" select="position()"/>
				<xsl:for-each select="BizList">
					<xsl:for-each select="BizInfo">
						<xsl:variable name="pos_BizInfo" select="position()"/>
						<xsl:for-each select="ProductSubsList">
							<xsl:for-each select="ProductInfo">
								<xsl:variable name="v_ProductSubsID" select="ProductSubsID"/>
								<xsl:variable name="v_ProductID" select="ProductID"/>
								<xsl:variable name="v_ProductName" select="ProductName"/>
								<xsl:variable name="v_BillingTerm" select="BillingTerm"/>
								<xsl:variable name="v_PayTerm" select="PayTerm"/>
								<xsl:variable name="pos_ProductInfo" select="position()"/>
								<xsl:for-each select="SubProductInfo">
									<xsl:variable name="s_ProductSubsID" select="ProductSubsID"/>
									<xsl:variable name="s_ProductID" select="ProductID"/>
									<xsl:variable name="s_ProductName" select="ProductName"/>
									<xsl:variable name="s_DbProductID" select="DbProductID"/>
									<xsl:variable name="s_DbProductName" select="DbProductName"/>
									<xsl:variable name="s_CoProductId" select="CoProductId"/>
									<xsl:variable name="s_BlProductID" select="BlProductID"/>
									<xsl:variable name="s_OneProductID" select="OneProductID"/>
									<xsl:variable name="s_BillingTerm" select="BillingTerm"/>
									<xsl:variable name="s_PayTerm" select="PayTerm"/>
									<xsl:variable name="s_OnProductCode" select="OnProductCode"/>
									<xsl:variable name="s_OnProductName" select="OnProductName"/>
									<xsl:variable name="s_EjProductCode" select="EjProductCode"/>
									<xsl:variable name="s_EjProductName" select="EjProductName"/>
									<xsl:variable name="s_SjProductCode" select="SjProductCode"/>
									<xsl:variable name="s_SjProductName" select="SjProductName"/>
									<xsl:variable name="s_GhCode" select="GhCode"/>
									<xsl:variable name="s_ProductClass" select="ProductClass"/>
									<xsl:variable name="s_ProductReportName" select="ProductReportName"/>
									<xsl:variable name="s_ProductReportItemName" select="ProductReportItemName"/>
									<xsl:variable name="s_IssueTime" select="IssueTime"/>
									<xsl:variable name="s_ExpireTime" select="ExpireTime"/>
									<xsl:variable name="s_ProductType" select="ProductType"/>
									<xsl:variable name="s_BusiType" select="BusiType"/>
									<xsl:variable name="s_ContractMain" select="ContractMain"/>
									<xsl:variable name="pos_SubProductInfo" select="position()"/>
									<xsl:for-each select="FeeList">
										<xsl:for-each select="FeeInfo">
											<xsl:variable name="s_ProdChargeCode" select="ProdChargeCode"/>
											<xsl:variable name="s_PoChargeCodeName" select="PoChargeCodeName"/>
											<xsl:variable name="s_OneProChargeCode" select="OneProChargeCode"/>
											<xsl:variable name="s_OneProChargeName" select="OneProChargeName"/>
											<xsl:variable name="s_DbProdChargeCode" select="DbProdChargeCode"/>
											<xsl:variable name="s_FeeVal" select="FeeVal"/>
											<xsl:variable name="s_TaxRate" select="TaxRate"/>
											<xsl:variable name="s_Tax" select="Tax"/>
											<xsl:variable name="s_FeeNoTax" select="FeeNoTax"/>
											<xsl:variable name="s_FeeFlag" select="FeeFlag"/>
											<xsl:variable name="s_DiscountAmount" select="DiscountAmount"/>
											<xsl:variable name="s_StandardFee" select="StandardFee"/>
											<xsl:variable name="s_SettleFee" select="SettleFee"/>
											<xsl:variable name="s_GhFeeType" select="GhFeeType"/>
											<xsl:variable name="s_PartnerCode" select="PartnerCode"/>
											<xsl:variable name="s_PartnerName" select="PartnerName"/>
											<xsl:variable name="s_ParSettleRate" select="ParSettleRate"/>
											<xsl:variable name="s_SettlementType" select="SettlementType"/>
											<xsl:variable name="s_ParSettlAmount" select="ParSettlAmount"/>
											<xsl:variable name="s_ParResSettlRate" select="ParResSettlRate"/>
											<xsl:variable name="s_SettlementClass" select="SettlementClass"/>
											<xsl:variable name="s_RateplanID" select="RateplanID"/>
											<xsl:variable name="s_RateplanName" select="RateplanName"/>
											<xsl:variable name="s_StandardSalePrice" select="StandardSalePrice"/>
											<xsl:variable name="s_SettlePrice" select="SettlePrice"/>
											<xsl:variable name="s_OriginalBillMonth" select="OriginalBillMonth"/>
											<xsl:variable name="s_Reserved1" select="Reserved1"/>
											<xsl:variable name="s_Reserved2" select="Reserved2"/>
											<xsl:variable name="s_FeeSeq" select="FeeSeq"/>
											<xsl:variable name="s_MemberNums" select="MemberNums"/>
											<xsl:variable name="s_IfFreeResource" select="IfFreeResource"/>
											<xsl:variable name="s_DiscountType" select="DiscountType"/>
											<xsl:variable name="s_SettleDisvalue" select="SettleDisvalue"/>
											<xsl:variable name="s_BsType" select="BsType"/>
											<xsl:variable name="s_RatePlanGhCode" select="RatePlanGhCode"/>
											<xsl:variable name="s_RatePlanGhName" select="RatePlanGhName"/>
											<xsl:variable name="pos_FeeInfo" select="position()"/>
											
											<xsl:value-of select="$pos_ECInfo"/>
											<xsl:value-of select="$hyphen"/>
											<xsl:value-of select="$pos_BizInfo"/>
											<xsl:value-of select="$hyphen"/>
											<xsl:value-of select="$pos_ProductInfo"/>
											<xsl:value-of select="$hyphen"/>
											<xsl:value-of select="$pos_SubProductInfo"/>
											<xsl:value-of select="$hyphen"/>
											<xsl:value-of select="$pos_FeeInfo"/>
											
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_ProvCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_PayTag"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_CustomerProvinceNumber"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_CustomerName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_CityCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_CreatorName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_ECid"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_ProductSubsID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_ProductID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$v_ProductName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductSubsID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_DbProductID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_DbProductName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_CoProductId"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_BlProductID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_OneProductID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_BillingTerm"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_PayTerm"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProdChargeCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="normalize-space($s_PoChargeCodeName)"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_OneProChargeCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_OneProChargeName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_DbProdChargeCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_FeeVal"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_TaxRate"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_Tax"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_FeeNoTax"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_FeeFlag"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_DiscountAmount"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_StandardFee"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SettleFee"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_GhFeeType"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_PartnerCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_PartnerName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ParSettleRate"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SettlementType"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ParSettlAmount"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ParResSettlRate"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SettlementClass"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_RateplanID"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="normalize-space($s_RateplanName)"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_StandardSalePrice"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SettlePrice"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_OriginalBillMonth"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_FeeSeq"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_MemberNums"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_IfFreeResource"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_DiscountType"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SettleDisvalue"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_BsType"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_RatePlanGhCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_RatePlanGhName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_OnProductCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_OnProductName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_EjProductCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_EjProductName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SjProductCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_SjProductName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_GhCode"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductClass"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductReportName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductReportItemName"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_IssueTime"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ExpireTime"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ProductType"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_BusiType"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_ContractMain"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_Reserved1"/>
											<xsl:value-of select="$sep"/>
											<xsl:value-of select="$s_Reserved2"/>
											<xsl:value-of select="$crlf"/>
											
											<xsl:for-each select="Province2CloudSettle">
												<xsl:for-each select="Province2CloudSettleinfo">
													<xsl:variable name="s_SettlementPartyIn1" select="SettlementPartyIn"/>
													<xsl:variable name="s_SettlementPartyOut1" select="SettlementPartyOut"/>
													<xsl:variable name="s_SettlementRate1" select="SettlementRate"/>
													<xsl:variable name="s_SettlementType1" select="SettlementType"/>
													<xsl:variable name="s_SettlementAmount1" select="SettlementAmount"/>
													<xsl:variable name="s_PrdSettleDisvalue1" select="PrdSettleDisvalue"/>
													
													<xsl:value-of select="$pos_ECInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_BizInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_ProductInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_SubProductInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_FeeInfo"/>
											
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$P2C"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyIn1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyOut1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementRate1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementType1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementAmount1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_PrdSettleDisvalue1"/>
													<xsl:value-of select="$crlf"/>
												</xsl:for-each>
											</xsl:for-each>
											
											<xsl:for-each select="Province2ProvinceSettle">
												<xsl:for-each select="Province2ProvinceSettleinfo">
													<xsl:variable name="s_SettlementPartyIn2" select="SettlementPartyIn"/>
													<xsl:variable name="s_SettlementPartyOut2" select="SettlementPartyOut"/>
													<xsl:variable name="s_SettlementRate2" select="SettlementRate"/>
													<xsl:variable name="s_SettlementType2" select="SettlementType"/>
													<xsl:variable name="s_SettlementAmount2" select="SettlementAmount"/>
													
													<xsl:value-of select="$pos_ECInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_BizInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_ProductInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_SubProductInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_FeeInfo"/>
													
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$P2P"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyIn2"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyOut2"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementRate2"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementType2"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementAmount2"/>
													<xsl:value-of select="$crlf"/>
												</xsl:for-each>
											</xsl:for-each>

											<xsl:for-each select="ZhiSuan2neiMengGu">
												<xsl:for-each select="ZhiSuan2neiMengGuInfo">
													<xsl:variable name="s_SettlementPartyIn1" select="SettlementPartyIn"/>
													<xsl:variable name="s_SettlementPartyOutType1" select="SettlementPartyOutType"/>
													<xsl:variable name="s_SettlementPartyOut1" select="SettlementPartyOut"/>
													<xsl:variable name="s_SettlementRate1" select="SettlementRate"/>
													<xsl:variable name="s_SettlementType1" select="SettlementType"/>
													<xsl:variable name="s_SettlementAmount1" select="SettlementAmount"/>
													<xsl:variable name="s_PrdSettleDisvalue1" select="PrdSettleDisvalue"/>

													<xsl:value-of select="$pos_ECInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_BizInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_ProductInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_SubProductInfo"/>
													<xsl:value-of select="$hyphen"/>
													<xsl:value-of select="$pos_FeeInfo"/>

													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$NMG"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyIn1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyOutType1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementPartyOut1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementRate1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementType1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_SettlementAmount1"/>
													<xsl:value-of select="$sep"/>
													<xsl:value-of select="$s_PrdSettleDisvalue1"/>
													<xsl:value-of select="$crlf"/>
												</xsl:for-each>
											</xsl:for-each>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:for-each>
							</xsl:for-each>
						</xsl:for-each>
					</xsl:for-each>
				</xsl:for-each>
			</xsl:for-each>
		</xsl:for-each>
    </xsl:template>
</xsl:stylesheet>
