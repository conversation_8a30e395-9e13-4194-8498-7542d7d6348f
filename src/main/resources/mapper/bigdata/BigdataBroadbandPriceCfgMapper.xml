<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.bigdata.BigdataBroadbandPriceCfgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.bigdata.BigdataBroadbandPriceCfg">
        <id column="id" property="id" />
        <result column="bus_type" property="busType" />
        <result column="settle_price" property="settlePrice" />
        <result column="bus_desc" property="busDesc" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, bus_type, settle_price, bus_desc, update_time, create_time, enable
    </sql>

</mapper>
