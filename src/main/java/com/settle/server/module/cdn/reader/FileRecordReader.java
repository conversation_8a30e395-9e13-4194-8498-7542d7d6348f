package com.settle.server.module.cdn.reader;

import com.settle.server.module.cdn.record.FileRecord;
import com.settle.server.module.cdn.record.Header;
import com.settle.server.utils.Utils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.stream.Stream;

import static java.lang.String.format;

/**
 * 读取目录中文件的 {@link RecordReader}
 * 此读取器生成 {@link FileRecord} 实例
 *
 * <AUTHOR>
 */
public class FileRecordReader implements RecordReader<Path> {

    private Path directory;
    private Iterator<Path> iterator;
    private long currentRecordNumber;
    private boolean recursive;
    private Stream<Path> pathStream;

    /**
     * 创建一个新的 {@link FileRecordReader}.
     *
     * @param path 从中读取文件
     */
    public FileRecordReader(final Path path) {
        this(path, false);
    }

    /**
     * 创建一个新的 {@link FileRecordReader}.
     *
     * @param path      从中读取文件
     * @param recursive 读取是否应该递归
     */
    public FileRecordReader(final Path path, final boolean recursive) {
        Utils.checkNotNull(path, "path");
        this.directory = path;
        this.recursive = recursive;
    }

    @Override
    public void open() throws Exception {
        checkDirectory();
        pathStream = recursive ? Files.walk(directory) : Files.list(directory);
        iterator = pathStream.filter(Files::isRegularFile).iterator();
        currentRecordNumber = 0;
    }

    @Override
    public void close() throws Exception {
        if (pathStream != null) {
            pathStream.close();
        }
    }

    private void checkDirectory() {
        Utils.checkArgument(Files.exists(directory), format("Directory %s does not exist.", getDataSourceName()));
        Utils.checkArgument(Files.isDirectory(directory), format("%s is not a directory.", getDataSourceName()));
        Utils.checkArgument(Files.isReadable(directory), format("Unable to read files from directory %s. Permission denied.", getDataSourceName()));
    }

    @Override
    public FileRecord readRecord() {
        Header header = new Header(++currentRecordNumber, getDataSourceName(), LocalDateTime.now());
        if (iterator.hasNext()) {
            return new FileRecord(header, iterator.next());
        } else {
            return null;
        }
    }

    private String getDataSourceName() {
        return directory.toAbsolutePath().toString();
    }

}
