package com.settle.server.module.cdn.converter;

import com.settle.server.utils.Utils;

/**
 * Byte 类型转换器
 * 不接受 {@code null} 或空字符串
 *
 * <AUTHOR>
 */
public class ByteTypeConverter implements TypeConverter<String, Byte> {

    /**
     * {@inheritDoc}
     */
    @Override
    public Byte convert(final String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        return Double.valueOf(value).byteValue();
    }

}
