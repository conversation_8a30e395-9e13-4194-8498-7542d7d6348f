package com.settle.server.module.cdn.record;


import com.settle.server.utils.Utils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static java.util.Collections.addAll;

/**
 * 批量记录
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class Batch<P> implements Iterable<Record<P>> {

    private List<Record<P>> records = new ArrayList<>();

    /**
     * 创建一个新的 {@link Batch}
     */
    public Batch() {
    }

    /**
     * 创建一个新的 {@link Batch}
     *
     * @param records 放入批次
     */
    public Batch(Record<P>... records) {
        addAll(this.records, records);
    }

    /**
     * 创建一个新的 {@link Batch}.
     *
     * @param records 放入批次
     */
    public Batch(List<Record<P>> records) {
        this.records = records;
    }

    /**
     * 向批次添加记录
     *
     * @param record 加上
     */
    public void addRecord(final Record<P> record) {
        records.add(record);
    }

    /**
     * 从批次中移除记录
     *
     * @param record 移除
     */
    public void removeRecord(final Record<P> record) {
        records.remove(record);
    }

    /**
     * 检查批次是否为空
     *
     * @return 如果批次为空，则为true，否则为false
     */
    public boolean isEmpty() {
        return records.isEmpty();
    }

    /**
     * 获取批量的大小
     *
     * @return 批量大小
     */
    public long size() {
        return records.size();
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Batch batch = (Batch) o;

        return records.equals(batch.records);

    }

    @Override
    public int hashCode() {
        return records.hashCode();
    }


    @Override
    public Iterator<Record<P>> iterator() {
        return records.iterator();
    }

    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("Batch: {");
        stringBuilder.append(Utils.LINE_SEPARATOR);
        for (Record<P> record : records) {
            stringBuilder.append('\t');
            stringBuilder.append(record);
            stringBuilder.append(Utils.LINE_SEPARATOR);
        }
        stringBuilder.append('}');
        return stringBuilder.toString();
    }
}
