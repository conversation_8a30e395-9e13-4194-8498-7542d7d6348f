package com.settle.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.settle.server.config.CardConfig;
import com.settle.server.dao.stludr.StlIotCardSettDao;
import com.settle.server.entity.StlIotCardSett;
import com.settle.server.enums.IotCardConstants;
import com.settle.server.enums.ProvinceCode;
import com.settle.server.service.IotCardSettService;
import com.settle.server.utils.EnumUtils;
import com.settle.server.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Service
public class IotCardSettServiceImpl implements IotCardSettService {

    public static final char VERTICAL_BAR = '|';

    public static final String amountRegx = "^[0-9]+(.[0-9]{1,2})?$";

    @Autowired
    private CardConfig cardConfig;

    @Resource
    private StlIotCardSettDao stlIotCardSettDao;


    @Override
    public void cardUpload(String acctMonth) {
        long startTime = System.currentTimeMillis();
        try {
            String fileNameRegex = "CARD_BBOSS_SETT_BILL_" + acctMonth + "\\.\\d{3}";

            List<Boolean> flags = new ArrayList();
            File dataFile = FileUtil.file(cardConfig.getTempPath());
            FileUtil.mkdir(dataFile);
            log.info("CARD_BBOSS_SETT_BILL 文件路径：{}",dataFile);
            File[] cardFiles = dataFile.listFiles(new FilenameFilter() {
                // 将被选出来，其余被过滤掉
                @Override
                public boolean accept(File dir, String fileName) {
                    Pattern pattern = Pattern.compile(fileNameRegex);
                    boolean isValid = pattern.matcher(fileName).matches();
                    if (isValid) {
                        flags.add(true);
                    } else {
                        flags.add(false);
                    }
                    return isValid;
                }
            });
            if (cardFiles == null) {
                log.info("账期：{},路径下：{},无CARD_BBOSS_SETT_BILL文件",acctMonth,dataFile);
            }
            log.info("账期：{},下载到本地文件数量有：{}个。", acctMonth, cardFiles.length);
            List<StlIotCardSett> stlIotCardSettList = null;
            if (cardFiles != null && cardFiles.length > 0) {
                stlIotCardSettDao.deleteBySettleMonth(acctMonth, "", IotCardConstants.busType);
                //读取文件数据并生成反馈文件
                stlIotCardSettList = this.readFileDataAndGenFeedBack(cardFiles, acctMonth);
            }
            if (!CollectionUtils.isEmpty(stlIotCardSettList)) {
                if(stlIotCardSettList.size() >500){
                    List<List<StlIotCardSett>> lists = ListUtils.splitList(stlIotCardSettList,500);
                    lists.forEach(list->{
                        stlIotCardSettDao.batchInsert(list);
                    });
                }else{
                    //入库
                    stlIotCardSettDao.batchInsert(stlIotCardSettList);
                }
            }
            this.moveFileToBak(cardFiles,acctMonth);

            long endSendTime = System.currentTimeMillis();
            long runSendTime = (endSendTime - startTime) / 1000;
            log.info(" ******文件处理完成,总用时:{}S，结束！", runSendTime);
        } catch (Exception e) {
            log.error("====>处理文件失败",  e);
            stlIotCardSettDao.deleteBySettleMonth(acctMonth, "", IotCardConstants.busType);
            throw new RuntimeException(" deal fail!" + e.getMessage());
        }
    }

    private void moveFileToBak(File[] localFiles, String acctMonth) throws IOException {
        Path backPath = Paths.get(cardConfig.getBakPath(), acctMonth);
        Files.createDirectories(backPath);
        log.info("账期：{}，本地文件数量：{}，开始移动到备份目录:{}",acctMonth,localFiles.length,backPath);
        for (File localFile : localFiles) {
            Files.move(localFile.toPath(),backPath.resolve(localFile.getName()), StandardCopyOption.REPLACE_EXISTING);
        }
    }

    private List<StlIotCardSett> readFileDataAndGenFeedBack(File[] localFiles, String acctMonth) throws Exception {
        List<StlIotCardSett> stlIotCardSettList = new ArrayList<>();
        //每个账期内，所有文件里的流水号
        Set<String> serialNumbers = new HashSet<>();
        for (File localFile : localFiles) {
            InputStream inputStream = new FileInputStream(localFile);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String line;
            StringBuilder filteredData = new StringBuilder();
            String filename = localFile.getName();
            while ((line = reader.readLine()) != null) {
                List<String> result = StrUtil.split(line, VERTICAL_BAR, false, false);
                if (result.size() != 8) {
                    throw new RuntimeException(String.format("Verification Error fileName: %s, 该行不是8个参数, is %s, Line is %s", filename, result.size(), line));
                }

                this.checkFileData(result, filteredData, acctMonth, serialNumbers, filename, stlIotCardSettList);
            }
            inputStream.close();
            reader.close();
            Path dir1 = Paths.get(cardConfig.getFeedBackPath());
            Files.createDirectories(dir1);
            String localFilePath = dir1 + File.separator + "RESP_" + filename; // 本地新文件路径
            // 写入筛选后的数据到新文件
            BufferedWriter writer = new BufferedWriter(new FileWriter(localFilePath));
            writer.write(String.valueOf(filteredData));
            writer.flush();
            writer.close();
        }
        return stlIotCardSettList;
    }

    private void checkFileData(List<String> result, StringBuilder filteredData, String acctMonth, Set<String> serialNumbers, String filename, List<StlIotCardSett> stlIotCardSetts) {
        //流水号
        String serialNumber = result.get(0);
        String settleMonth = result.get(1);
        String busType = result.get(2);
        String settleType = result.get(3);
        String settleOut = result.get(4);
        String settleIn = result.get(5);
        String settleAmountInTax = result.get(6);
        String taxRate = result.get(7);
        boolean containSerialNumber = serialNumbers.contains(serialNumber);
        if (containSerialNumber || serialNumber.length()>30) {
            filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F001.getErrorCode()).append("\n");
            return ;
        } else {
            serialNumbers.add(serialNumber);
            //结算账期
            if (settleMonth.length() != 6 || !settleMonth.equals(acctMonth)) {
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F002.getErrorCode()).append("\n");
                return ;
            }
            //业务编码
            if (!StringUtils.equals(busType, IotCardConstants.busType)) {
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F003.getErrorCode()).append("\n");
                return ;
            }
            //结算类型
            if (!StringUtils.equals(settleType, IotCardConstants.settleType)) {
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F004.getErrorCode()).append("\n");
                return ;
            }
            Set<String> provinceCode = ProvinceCode.provinceCodeMap.keySet();
            //结出方
            if (!provinceCode.contains(settleOut)) {
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F005.getErrorCode()).append("\n");
                return ;
            }
            //结入方
            if (!StringUtils.equals(settleIn, IotCardConstants.settleIn)) {
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F006.getErrorCode()).append("\n");
                return ;
            }
            //结算含税金额
            Pattern pattern = Pattern.compile(amountRegx);
            Matcher matcher = pattern.matcher(settleAmountInTax);
            if (!matcher.matches() || settleAmountInTax.length()>20 ){
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F007.getErrorCode()).append("\n");
                return ;
            }
            //税率
            if (!EnumUtils.isInclude(IotCardConstants.TaxRate.class, taxRate)) {
                filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F008.getErrorCode()).append("\n");
                return ;
            }
        }
        filteredData.append(serialNumber).append("|").append(IotCardConstants.ErrorMsg.F000.getErrorCode()).append("\n");
        //记录校验没问题的数据
        StlIotCardSett stlIotCardSett = new StlIotCardSett();
        stlIotCardSett.setSerialNumber(serialNumber);
        stlIotCardSett.setSettleMonth(settleMonth);
        stlIotCardSett.setBusType(busType);
        stlIotCardSett.setSettleType(settleType);
        stlIotCardSett.setSettleOut(settleOut);
        stlIotCardSett.setSettleIn(settleIn);
        stlIotCardSett.setSettleAmount(settleAmountInTax);
        stlIotCardSett.setTaxRate(taxRate);
        stlIotCardSett.setSourceFileName(filename);
        stlIotCardSetts.add(stlIotCardSett);
    }

//    private void downloadFileAsync(List<ChannelSftp.LsEntry> dirList,String acctMonth) throws IOException {
//
//        Path dir = Paths.get(osSftpConfig.getTempPath(), acctMonth);
//        Path directories = Files.createDirectories(dir);
//        CompletableFuture[] completableFutures = dirList.stream().map(LsEntry ->
//                CompletableFuture.runAsync(() -> {
//                    SFTPUtils sftpUtils1 = new SFTPUtils();
//                    try {
//                        sftpUtils1.connectServer(osSftpConfig.getSftpHost(),osSftpConfig.getSftpPort(),osSftpConfig.getSftpUsername(),osSftpConfig.getSftpPassword());
//                        String filename = LsEntry.getFilename();
//                        //下载到本地
//                        sftpUtils1.downloadFile(osSftpConfig.getRemoteDownloadPath()+ "/"+filename, directories.toAbsolutePath().toString());
//                    } catch (Exception e) {
//                        log.error("上传文件失败！", e.getMessage(), e);
//                        throw new RuntimeException(" 上传文件失败!" + e.getMessage());
//                    }finally {
//                        // Close the SFTP connection
//                        sftpUtils1.close();
//                    }
//                }, executor)).toArray(CompletableFuture[]::new);
//        CompletableFuture.allOf(completableFutures).join();
//    }
//
//    private void uploadFilesAsync(String localDirectoryPath, String acctMonth) {
//
//            // 获取本地目录中的所有文件
//            File localDirectory1 = new File(localDirectoryPath+"_feedBack"+ "/"+acctMonth);
//            File[] localFiles1 = localDirectory1.listFiles();
//            if (localFiles1 != null && localFiles1.length>0) {
//                CompletableFuture[] completableFutures = Arrays.stream(localFiles1).map(localFile ->
//                        CompletableFuture.runAsync(() -> {
//                            SFTPUtils sftpUtils2 = new SFTPUtils();
//                            try {
//                                sftpUtils2.connectServer(osSftpConfig.getSftpHost(),osSftpConfig.getSftpPort(),osSftpConfig.getSftpUsername(),osSftpConfig.getSftpPassword());
//                                // 构建远程文件路径
//                                String localFileName = localFile.getName();
//                                String remoteFilePath = osSftpConfig.getRemoteUploadPath() + "/" + localFileName;
//                                // 上传本地文件到远程SFTP服务器
//                                sftpUtils2.uploadFile(remoteFilePath, localFile.getAbsolutePath());
//                            } catch (Exception e) {
//                                log.error("上传文件失败！", e.getMessage(), e);
//                                throw new RuntimeException(" 上传文件失败!" + e.getMessage());
//                            }finally {
//                                // Close the SFTP connection
//                                sftpUtils2.close();
//                            }
//                        }, executor)).toArray(CompletableFuture[]::new);
//                CompletableFuture.allOf(completableFutures);
//            }
//    }

}
