package com.settle.server.module.cdn;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.setting.Setting;
import com.settle.server.entity.CustProdInfo;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.enums.ActionType;
import com.settle.server.module.cdn.job.Job;
import com.settle.server.module.cdn.job.JobExecutor;
import com.settle.server.module.cdn.worker.WorkJobFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CDNToEBossHandler {
    @Autowired
    @Qualifier("CDNToEBossRule")
    private Setting CDNToEBossRule;

    @Autowired
    @Qualifier("EBossToSettleRule")
    private Setting EBossToSettleRule;
    @Autowired
    private WorkJobFactory workJobFactory;
    @Autowired
    @Qualifier("stludrDataSource")
    private DataSource stludrDataSource;
    private static final int THREAD_POOL_SIZE = 6;


    public void handle(String acctMonth, ActionType actionType){
        Setting argsSetting = getSetting(acctMonth);
        //扫描指定包路径下所有class文件
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put(ClassUtil.getClassName(CustProdInfo.class,true),CustProdInfo.class);
        log.info("::::::::::::扫描到的类: {}", classMap.values());
        List<Job> jobList = new ArrayList<>();
        jobList.addAll(parseRuleSettingGroup(actionType, argsSetting, classMap, stludrDataSource));

        // 使用 6 个工作线程创建一个作业执行程序
        JobExecutor jobExecutor = new JobExecutor(THREAD_POOL_SIZE);
        // 将作业提交给执行器
        jobExecutor.submitAll(jobList);
        // 关闭作业执行器
        jobExecutor.shutdown();
    }

    private List<Job> parseRuleSettingGroup(ActionType actionType, Setting argsSetting, Map<String, Class<?>> classMap, DataSource stludrDataSource) {
        List<Job> jobList = new ArrayList<>();
        switch (actionType) {
            case CSV2DB:
                if (ObjectUtil.isNotEmpty(argsSetting)) {
                    EBossToSettleRule.addSetting(argsSetting);
                }
                //1、hwcdn
                List<String> ruleGroupList = EBossToSettleRule.getGroups();
                for (String ruleGroup : ruleGroupList) {
                    if (StrUtil.isNotEmpty(ruleGroup)) {
                        jobList.addAll(workJobFactory.buildJobList(EBossToSettleRule, ruleGroup, classMap,stludrDataSource));
                    }
                }
                break;
            case DB2CSV:
                if (ObjectUtil.isNotEmpty(argsSetting)) {
                    CDNToEBossRule.addSetting(argsSetting);
                }
                //1、"",2、CDN2EBOSS
                List<String> ruleGroupList1 = CDNToEBossRule.getGroups();
                for (String ruleGroup : ruleGroupList1) {
                    if (StrUtil.isNotEmpty(ruleGroup)) {
                        jobList.addAll(workJobFactory.buildJobList(CDNToEBossRule, ruleGroup, classMap,stludrDataSource));
                    }
                }
                break;
            default:
                break;
        }
        return jobList;
    }

    private static Setting getSetting(String acctMonth) {
        Setting setting = null;
        if (StrUtil.isNotBlank(acctMonth)) {
            setting = Setting.create();
            setting.put(NormalConst.ARG_ACCT_MONTH, acctMonth);
        }
        return setting;
    }


}
