<?xml version="1.0" encoding="UTF-8"?>
<!--
- author:   <PERSON> Kitt
- date:     2023/08/18
- verson:   v1.0
- descprition:
    this file is used to transform the incoming inventory XML file to txt file.
-->
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:fn="http://www.w3.org/2005/xpath-functions" exclude-result-prefixes="fn xs xsi xsl">
	<xsl:output method="text" encoding="UTF-8" media-type="text/plain"/>
	<xsl:variable name="sep">
		<xsl:text>!!!!</xsl:text>
	</xsl:variable>
	<xsl:variable name="zero">
		<xsl:text>0</xsl:text>
	</xsl:variable>
	<xsl:variable name="hyphen">
		<xsl:text>-</xsl:text>
	</xsl:variable>
	<xsl:variable name="crlf">
		<xsl:text>&#xd;&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="carr">
		<xsl:text>&#xa;</xsl:text>
	</xsl:variable>
      
    <xsl:template match="/RecIncome">
		<xsl:variable name="v_BillMonth" select="BillMonth"/>
		
		<xsl:for-each select="RecIncomeList">
			<xsl:for-each select="RecIncomeInfo">
				<xsl:variable name="v_ProvCode" select="ProvCode"/>
				<xsl:variable name="v_DirectRecAmount" select="DirectRecAmount"/>
				<xsl:variable name="v_JointRecAmount" select="JointRecAmount"/>
				
				<xsl:value-of select="$v_BillMonth"/>
				<xsl:value-of select="$sep"/>
				<xsl:value-of select="$v_ProvCode"/>
				<xsl:value-of select="$sep"/>
				<xsl:value-of select="$v_DirectRecAmount"/>
				<xsl:value-of select="$sep"/>
				<xsl:value-of select="$v_JointRecAmount"/>
				<xsl:value-of select="$crlf"/>
			</xsl:for-each>
		</xsl:for-each>
    </xsl:template>
</xsl:stylesheet>
