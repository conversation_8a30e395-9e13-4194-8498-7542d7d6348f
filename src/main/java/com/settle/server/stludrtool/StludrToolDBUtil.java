package com.settle.server.stludrtool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.sql.*;

@Slf4j
public class StludrToolDBUtil {
	@Value("${jdbc.stludrserver.driverClassName}")
	public void setDriverClassName(String driverClassNameValue){
		DRIVER_CLASS_NAME = driverClassNameValue;
	}
	@Value("${jdbc.stludrserver.url}")
	public  void setUrl(String urlValue) {
		DB_URL = urlValue;
	}
	@Value("${jdbc.stludrserver.username}")
	public  void setUsername(String usernameValue) {
		DB_NAME = usernameValue;
	}
	@Value("${jdbc.stludrserver.password}")
	public  void setPassword(String passwordValue) {
		DB_PASS = passwordValue;
	}

	private Connection conn;
	private CallableStatement proc;
	private PreparedStatement pre;
	private ResultSet res;
	public static String DRIVER_CLASS_NAME;
	public static String DB_URL;
	public static String DB_NAME;
	public static String DB_PASS;

	public StludrToolDBUtil() {
		super();
		try {
			Class.forName(DRIVER_CLASS_NAME).newInstance();
			String url = DB_URL;
			String admin = DB_NAME;
			String passwd = DB_PASS;
			conn = DriverManager.getConnection(url, admin, passwd);
		} catch (InstantiationException e) {
			log.error(e.getMessage(), e);
		} catch (IllegalAccessException e) {
			log.error(e.getMessage(), e);
		} catch (ClassNotFoundException e) {
			log.error(e.getMessage(), e);
		} catch (SQLException e) {
			log.error(e.getMessage(), e);
		}
	}

	public boolean close() {
		try {
			if (res != null)
				res.close();
			if (proc != null)
				proc.close();
			if (pre != null)
				pre.close();
			if (conn != null)
				conn.close();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return false;
		}
		return true;
	}

	public PreparedStatement getPre() {
		return pre;
	}

	public void setPre(PreparedStatement pre) {
		this.pre = pre;
	}

	public Connection getConn() {
		return conn;
	}

	public void setConn(Connection conn) {
		this.conn = conn;
	}

	public CallableStatement getProc() {
		return proc;
	}

	public void setProc(CallableStatement proc) {
		this.proc = proc;
	}

	public ResultSet getRes() {
		return res;
	}

	public void setRes(ResultSet res) {
		this.res = res;
	}
}
