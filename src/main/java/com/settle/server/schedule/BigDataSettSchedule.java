package com.settle.server.schedule;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.service.BigDataSettService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class BigDataSettSchedule {

    @Autowired
    private BigDataSettService bigDataSettService;

    @XxlJob("handBigDataFile")
    public ReturnT<String> handBigDataFile() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("大数据平台文件处理开始，接受参数：{}", param);
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if(StringUtils.isBlank(acctMonth)){
                LocalDateTime now = LocalDateTime.now(); // 获取当前日期和时间
                LocalDateTime localDateTime = now.minusMonths(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth =  localDateTime.format(formatter);
            }
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            bigDataSettService.loadUpload(acctMonth);
            XxlJobHelper.log("大数据平台文件处理结束");
        }catch (Exception re){
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("大数据平台文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    
    @XxlJob("broadbandPreHandler")
    public ReturnT<String> broadbandPreHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("带宽型业务省间结算数据预出账处理开始，接受参数：{}", param);
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if(StringUtils.isBlank(acctMonth)){
                LocalDateTime now = LocalDateTime.now(); // 获取当前日期和时间
                LocalDateTime localDateTime = now.minusMonths(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth =  localDateTime.format(formatter);
            }
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            bigDataSettService.broadbandPreProcess(acctMonth);
            XxlJobHelper.log("带宽型业务省间结算数据预出账处理结束");
        }catch (Exception re){
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("带宽型业务省间结算数据预出账处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }
}
