<?xml version="1.0" encoding="UTF-8"?>
<!--Sample XML file generated by XMLSpy v2008 sp1 (http://www.altova.com)-->
<Configure xsi:noNamespaceSchemaLocation="text2xml.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Paths>
		<Log4j>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/t2xlog4j.properties</Log4j>
		<DBConf>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/XML_DB.properties</DBConf>
		<Basepath name="error">
			<In>$BBOSS_HOME/$BBOSS_APPID/data/file_error/BILLLIST</In>
			<Out>$BBOSS_HOME/$BBOSS_APPID/data/txt2xml/BILLLIST</Out>
			<Back>$BBOSS_HOME/$BBOSS_APPID/arch/file_error/BILLLIST/</Back>
		</Basepath>
		<Basepath name="outgoing">
			<In>$BBOSS_HOME/$BBOSS_APPID/data/outgoing/BILLLIST</In>
			<Out>$BBOSS_HOME/$BBOSS_APPID/data/outgoing/BILLLIST</Out>
			<Back>$BBOSS_HOME/$BBOSS_APPID/arch/outgoing/BILLLIST/</Back>
		</Basepath>
	</Paths>
	<Category>
		<Entry name="ERROR" type="F">
			<Basepath>error</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Regexp>(F|E)BBOSS_ACC_(M_LIST|E_BILL)_[0-9]{8}_[0-9]{3}_[0-9]{3}.*</Regexp>
		</Entry>
		<Entry name="OUTGOING" type="F">
			<Basepath>outgoing</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Regexp>(F|E)BBOSS_ACC_(M_LIST|E_BILL)_[0-9]{8}_[0-9]{3}_[0-9]{3}.*</Regexp>
		</Entry>
		<Entry name="ERRORS" type="F">
			<Basepath>error</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Regexp>(F|E)BBOSS_Billing_LIST_[0-9]{8}_[0-9]{3}_000.([0-8]\d\d|9[0-8]\d|99[0-8])$|(F|E)BBOSS_Billing_LIST_[0-9]{8}_[0-9]{3}_000.([0-8]\d\d|9[0-8]\d|99[0-8])_tst$</Regexp>
		</Entry>
		<Entry name="OUTGOINGS" type="F">
			<Basepath>outgoing</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Regexp>(F|E)BBOSS_Billing_LIST_[0-9]{8}_[0-9]{3}_000.([0-8]\d\d|9[0-8]\d|99[0-8])$|(F|E)BBOSS_Billing_LIST_[0-9]{8}_[0-9]{3}_000.([0-8]\d\d|9[0-8]\d|99[0-8])_tst$</Regexp>
		</Entry>
	</Category>
</Configure>
