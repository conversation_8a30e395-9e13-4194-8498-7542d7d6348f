package com.settle.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.settle.server.config.*;
import com.settle.server.dao.stludr.SyncInterfaceBc2cPARTDao;
import com.settle.server.dao.stludr.SyncInterfaceBc2cPSDao;
import com.settle.server.dto.XmlResp;
import com.settle.server.entity.BC2CVo.*;
import com.settle.server.entity.*;
import com.settle.server.entity.esp.SyncInterfaceEsp;
import com.settle.server.entity.esp.SyncInterfaceEspP2c;
import com.settle.server.entity.esp.SyncInterfaceEspP2p;
import com.settle.server.entity.esp.SyncInterfaceEspPart;
import com.settle.server.entity.pvbill.SyncInterfacePvb;
import com.settle.server.entity.pvbill.SyncInterfacePvbAdj;
import com.settle.server.entity.pvsettle.SyncInterfacePvs;
import com.settle.server.entity.pvsettle.SyncInterfacePvsToc;
import com.settle.server.module.esp.CustomerFileFilter;
import com.settle.server.module.esp.cache.EspBizCache;
import com.settle.server.module.esp.dto.LogXml;
import com.settle.server.module.esp.dto.ResultDate;
import com.settle.server.module.esp.enums.BizCode;
import com.settle.server.module.esp.enums.RespCode;
import com.settle.server.module.esp.service.*;
import com.settle.server.service.EbossFileService;
import com.settle.server.utils.JAXBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.xml.sax.SAXException;

import javax.annotation.PostConstruct;
import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Pattern;

@Slf4j
@Service
public class MobileCloudServiceImpl implements EbossFileService {

    public static final String FOUR_BAR = "!!!!";


    @Autowired
    private McConfig mcConfig;
    @Autowired
    private ZqEspBcConfig bcConfig;
    @Autowired
    private PvBillConfig pvBillConfig;
    @Autowired
    private PvSettleConfig pvSettleConfig;
    @Autowired
    private EspAccConfig espAccConfig;

    @Autowired
    private AppContextConfig appContextConfig;

    @Autowired
    private SyncInterfaceMcService syncInterfaceMcService;

    @Autowired
    private SyncInterfaceBc2cPSDao syncInterfaceBc2cPSDao;

    @Autowired
    private SyncInterfaceBc2cPARTDao syncInterfaceBc2cPARTDao;
    @Autowired
    private SyncPvSettleService pvSettleService;
    @Autowired
    private SyncPvBillService pvBillService;
    @Autowired
    private SyncEspService syncEspService;
    @Autowired
    private SyncBc2cService bc2cService;

    @Autowired
    private LogXmlService logXmlService;

    private SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

    private DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();

    private TransformerFactory transformerFactory = TransformerFactory.newInstance();

    private static Map<BizCode, Executor> parseExecutorMap = Maps.newConcurrentMap();
    private static Map<BizCode, Executor> processExecutorMap = Maps.newConcurrentMap();
    @PostConstruct
    public void init() {
        List<AppContextConfig.EBossFile> eBossFiles = appContextConfig.getEBossFiles();
        if (!CollectionUtils.isEmpty(eBossFiles)) {
            for (AppContextConfig.EBossFile eBossFile : eBossFiles) {
                BizCode bizCode = eBossFile.getBizCode();
                ThreadFactory threadFactory = ThreadFactoryBuilder.create().setNamePrefix(bizCode.getBizType() + "-parse-").build();
                ThreadFactory threadFactory2 = ThreadFactoryBuilder.create().setNamePrefix(bizCode.getBizType() + "-process-").build();
                Integer fileThreadNum = eBossFile.getFileThreadNum();
                Integer processThreadNum = eBossFile.getProcessThreadNum();
                if (fileThreadNum != null && fileThreadNum > 1) {
                    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
                            fileThreadNum, fileThreadNum, 0, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000), threadFactory,new BlockPolicy());
                    parseExecutorMap.put(bizCode, threadPoolExecutor);
                }
                if (processThreadNum != null && processThreadNum > 1) {
                    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
                            processThreadNum, processThreadNum, 0, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000), threadFactory2,new BlockPolicy());
                    processExecutorMap.put(bizCode, threadPoolExecutor);
                }
            }
        }

        for (BizCode bizCode : BizCode.values()) {
            if (parseExecutorMap.get(bizCode) == null) {
                ThreadFactory threadFactory = ThreadFactoryBuilder.create().setNamePrefix(bizCode.getBizType() + "-parse-").build();
                ThreadPoolExecutor parseExecutor = new ThreadPoolExecutor(
                        1, 1, 0, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000), threadFactory,new BlockPolicy());
                parseExecutorMap.put(bizCode, parseExecutor);
            }
            if (processExecutorMap.get(bizCode) == null) {
                ThreadFactory threadFactory2 = ThreadFactoryBuilder.create().setNamePrefix(bizCode.getBizType() + "-process-").build();
                ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
                        1, 1, 0, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000), threadFactory2,new BlockPolicy());
                processExecutorMap.put(bizCode, processExecutor);
            }
        }

    }

    @Override
    public void saveBCTOC(String acctMonth) throws Exception {
        Path errorPath = Paths.get(bcConfig.getErrorPath(), acctMonth);
        Path backupPath = Paths.get(bcConfig.getBackupPath(), acctMonth);
        List<Boolean> flags = new ArrayList();
        if (FileUtil.exist(bcConfig.getDataPath())) {
            File dataFile = FileUtil.file(bcConfig.getDataPath());
            File[] bcFiles = dataFile.listFiles(new FilenameFilter() {
                // 将被选出来，其余被过滤掉
                @Override
                public boolean accept(File dir, String fileName) {
                    Pattern pattern = Pattern.compile(bcConfig.getValidName());
                    if (pattern.matcher(fileName).matches()) {
                        String time = fileName.substring(fileName.length() - 11, fileName.length() - 5);
                        //正则匹配完之后校验文件时名称是不是年月 202312
                        if (time.equals(acctMonth)) {
                            flags.add(true);
                            return true;
                        }
                    }
                    flags.add(false);
                    return false;
                }
            });
            if (bcFiles.length == 0) {
                log.info("BC2C No of input file not found under dir path:{}", bcConfig.getDataPath());
                return;
            }
            if (flags.contains(true)) {
                syncInterfaceBc2cPSDao.deleteByMonth(null);
                syncInterfaceBc2cPARTDao.deleteByMonth(null);
            }
            for (File bcFile : bcFiles) {
                //此处为单个文件处理
                String fileName = bcFile.getName();
                List<SyncInterfaceBc2cPS> syncInterfaceBc2cPSList = new ArrayList<>();
                List<SyncInterfaceBc2cPart> syncInterfaceBc2cPartList = new ArrayList<>();

                // 反馈文件
                Path dir1 = Paths.get(bcConfig.getRespPath(), acctMonth);
                Files.createDirectories(dir1);
                String localFilePath = dir1 + File.separator + "Resp_" + fileName; // 本地新文件路径
                JSONObject respErrorInfo = new JSONObject();
                String respXmlInfo = "";
                BufferedWriter writer = new BufferedWriter(new FileWriter(localFilePath));
                StringBuffer errDesc = new StringBuffer();
                if (validateXMLAgainstXSD(bcFile.getAbsolutePath(), errDesc)) {
                    log.info("XML is valid against XSD.");
                } else {
                    JSONObject jsonObject = new JSONObject(true);
                    jsonObject.putOnce("Org_FileName", bcFile.getName());
                    jsonObject.putOnce("Resp_Date", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
                    jsonObject.putOnce("FileStatus", "1");
                    jsonObject.putOnce("ErrorCode", "99");
                    jsonObject.putOnce("ErrorNode", errDesc.toString());
                    respErrorInfo.putOpt("BillList", jsonObject);
                    respXmlInfo = JSONUtil.toXmlStr(respErrorInfo);
                    Files.createDirectories(errorPath);
                    Files.move(bcFile.toPath(), errorPath.resolve(bcFile.getName()), StandardCopyOption.REPLACE_EXISTING);
                    log.info("bc2c create error File finish fileName:{} acctMonth:{}", localFilePath, acctMonth);
                    writer.write(respXmlInfo);
                    writer.newLine();
                    writer.flush();
                    writer.close();
                    continue;
                }


                try {
                    BillList billList = parseXML(bcFile);
                    if (billList.getToCBizList() != null && billList.getToCBizList().size() > 0) {
                        log.info("BC2C 数据开始入库。。。。。。");
                        optPS(acctMonth, fileName, syncInterfaceBc2cPSList, billList);
                        optPART(acctMonth, fileName, syncInterfaceBc2cPartList, billList);

                        bc2cService.saveSyncInterfaceBc2cPS(acctMonth, syncInterfaceBc2cPSList);
                        bc2cService.saveSyncInterfaceBc2cPART(acctMonth, syncInterfaceBc2cPartList);

                        log.info("BC2C 数据开始结束。。。。。。");
                        log.info("BC2C 校验开始。。。。。。");
                        log.info("Step1 省份不符合规范。。。。。。status:23");
                        syncInterfaceBc2cPSDao.updateStausByAcctMonth(acctMonth);
                        syncInterfaceBc2cPARTDao.updateStausByAcctMonth(acctMonth);
                        log.info("Step1 校验结束。。。。。。");
                        log.info("BC2C 校验结束。。。。。。");

                        log.info("正确数据状态更新。。。。。。status:0");
                        syncInterfaceBc2cPSDao.updateStausByStatus(acctMonth);
                        syncInterfaceBc2cPARTDao.updateStausByStatus(acctMonth);
                        log.info("正确数据状态更新完成。。。。。。");

                        log.info("根据状态生成响应文件开始。。。。。。");
                        log.info("获取状态不为0 数据。。。。。。");
                        List<SyncInterfaceBc2cPS> syncInterfaceBc2cPS = syncInterfaceBc2cPSDao.queryErrorByFileName(acctMonth, fileName);
                        List<SyncInterfaceBc2cPart> syncInterfaceBc2cParts = syncInterfaceBc2cPARTDao.queryErrorByFileName(acctMonth, fileName);

                        if (!syncInterfaceBc2cPS.isEmpty() || !syncInterfaceBc2cParts.isEmpty()) {
                            List<JSONObject> jsonObjects = new ArrayList<>();
                            JSONObject jsonObject = new JSONObject(true);
                            jsonObject.putOnce("ErrorCode", "23");
                            String errorNodeStr = "";
                            if (!syncInterfaceBc2cPS.isEmpty()) {
                                errorNodeStr = syncInterfaceBc2cPS.get(0).getProvCode();
                            }
                            if (!syncInterfaceBc2cParts.isEmpty()) {
                                errorNodeStr = syncInterfaceBc2cParts.get(0).getProvCode();
                            }
                            jsonObject.putOnce("ErrorNode", errorNodeStr);
                            JSONObject errorRecord = new JSONObject(true);
                            errorRecord.putOnce("ErrorRecord", jsonObject);
                            jsonObjects.add(errorRecord);
                            JSONObject object = new JSONObject(true);
                            object.putOnce("Org_FileName", fileName);
                            object.putOnce("Resp_Date", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
                            object.putOnce("FileStatus", 2);
                            object.putOnce("ErrorRecords", jsonObjects);
                            respErrorInfo.putOpt("BillList", object);
                            respXmlInfo = JSONUtil.toXmlStr(respErrorInfo);
                            Files.createDirectories(errorPath);
                            Files.move(bcFile.toPath(), errorPath.resolve(bcFile.getName()), StandardCopyOption.REPLACE_EXISTING);
                        } else {
                            JSONObject jsonSucObject = new JSONObject(true);
                            jsonSucObject.putOnce("Org_FileName", fileName);
                            jsonSucObject.putOnce("Resp_Date", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
                            jsonSucObject.putOnce("FileStatus", 0);
                            JSONObject respInfo = new JSONObject(true);
                            respInfo.putOpt("BillList", jsonSucObject);
                            respXmlInfo = JSONUtil.toXmlStr(respInfo);
                            Files.createDirectories(backupPath);
                            Files.move(bcFile.toPath(), backupPath.resolve(bcFile.getName()), StandardCopyOption.REPLACE_EXISTING);
                        }
                    } else {
                        log.warn("BC2C input file is empty dir {}. Exit now.", bcFile.getAbsolutePath());
                        Files.createDirectories(backupPath);
                        Files.move(bcFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                    }
                } catch (Exception e) {
                    log.error("{}文件无法解析错误(文件级错误){}", bcFile.getName(), e.getMessage());
                    JSONObject jsonObject = new JSONObject(true);
                    jsonObject.putOnce("Org_FileName", bcFile.getName());
                    jsonObject.putOnce("Resp_Date", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
                    jsonObject.putOnce("FileStatus", "1");
                    jsonObject.putOnce("ErrorCode", "21");
                    jsonObject.putOnce("ErrorNode", e.getMessage().length() > 255 ? e.getMessage().substring(0, 255) : e.getMessage());
                    respErrorInfo.putOpt("BillList", jsonObject);
                    respXmlInfo = JSONUtil.toXmlStr(respErrorInfo);
                    Files.createDirectories(errorPath);
                    Files.move(bcFile.toPath(), errorPath.resolve(bcFile.getName()), StandardCopyOption.REPLACE_EXISTING);
                    log.info("bc2c create error File finish fileName:{} acctMonth:{}", localFilePath, acctMonth);
                    throw e;
                } finally {
                    writer.write(respXmlInfo);
                    writer.newLine();
                    writer.flush();
                    writer.close();
                    log.info("bc2c create resp File finish fileName:{} acctMonth:{}", localFilePath, acctMonth);
                }
            }
        }
    }

    @Override
    @Async
    public void processCloud_4_30(String acctMonth) {
        ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_30);
        if (!lock.tryLock()) {
            log.info("4.30接口文件正在处理中，本次不处理");
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            //1.扫描4.30接口文件
            List<File> xmlFiles = this.scanXmlFile(acctMonth, BizCode.BIZ_4_30);
            if (CollectionUtils.isEmpty(xmlFiles)) {
                return;
            }
            //清理表数据
            clearData(acctMonth,null, BizCode.BIZ_4_30);
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            for (File xmlFile : xmlFiles) {
                CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
                            //2.处理文件
                            return this.procOnce(xmlFile, acctMonth, BizCode.BIZ_4_30);
                        }, parseExecutorMap.get(BizCode.BIZ_4_30))
                        .exceptionally(e->{
                            log.error("4.30接口文件:{},处理失败", xmlFile.getName(),e);
                            return null;
                        })
                        .thenAcceptAsync(
                                file -> {
                                    if (file != null) {
                                        savePvSettle(acctMonth, xmlFile, file);
                                    }
                                }, processExecutorMap.get(BizCode.BIZ_4_30));
                futureList.add(future);
            }
            //等待执行结束
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("4.30接口文件处理失败", e);
        } finally {
            stopWatch.stop();
            log.info("4.30接口文件,账期：{},处理完成. 耗时：{} ms", acctMonth, stopWatch.getTotalTimeMillis());
            lock.unlock();
        }
    }

    private void savePvSettle(String acctMonth, File xmlFile, File file) {
        List<XmlResp.ErrorRecord> errorRecords = null;
        ResultDate resultDate = ResultDate.success();
        List<SyncInterfacePvs> pvsList = new ArrayList<>();
        List<SyncInterfacePvsToc> pvsTocList = new ArrayList<>();
        long count = 0;
        boolean isToC = false;
        try {
            List<String> lines = FileUtil.readUtf8Lines(file);
            lines.removeAll(Arrays.asList("", null));
            if (lines.isEmpty()) {
                log.warn("PVSettle input file is empty");
                resultDate = ResultDate.fail("PVSettle input file is empty");
                return;
            }
            isToC = lines.get(0).contains("NoneToC!!!!");
            count = lines.size();
            log.info("PVSettle input file details path:{}, row:{}", file.getAbsolutePath(), lines.size());
            for (String line : lines) {
                if (isToC) {
                    pvSettleService.optPvs(acctMonth, xmlFile.getName(), pvsList, line);
                } else {
                    pvSettleService.optPvsToc(acctMonth, xmlFile.getName(), pvsTocList, line);
                }
            }
            //保存数据
            pvSettleService.saveSyncInterfacePvs(acctMonth, pvsList,pvSettleConfig.getBatchSize());
            pvSettleService.saveSyncInterfacePvsToc(acctMonth, pvsTocList,pvSettleConfig.getBatchSize());
            //更新状态为23
            pvSettleService.updateStatus23(acctMonth, xmlFile.getName());
            //更新状态为0
            pvSettleService.updateStatus0(acctMonth, xmlFile.getName());

            if (isToC) {
                List<SyncInterfacePvsToc> errDatas = pvSettleService.queryToCErrorByFileName(acctMonth, xmlFile.getName());
                errorRecords = getPvsTocErrorRecords(errDatas);
            } else {
                List<SyncInterfacePvs> errDatas = pvSettleService.queryErrorByFileName(acctMonth, xmlFile.getName());
                errorRecords = getPvsErrorRecords(errDatas);
            }
            if (CollectionUtils.isNotEmpty(errorRecords)) {
                resultDate = ResultDate.fail(RespCode.ERR_Line, "PVSettle数据校验失败");
            }
            log.info("PVSettle 执行成功 fileName:{} acctMonth:{}", xmlFile.getName(), acctMonth);
        } catch (Exception e) {
            log.error("PVSettle 执行失败 fileName:{} acctMonth:{} error：{}", xmlFile.getName(), acctMonth, e.getMessage(), e);
            resultDate = ResultDate.fail(RespCode.ERR_99_F, "文件处理失败");

        } finally {
            saveLog(xmlFile, BizCode.BIZ_4_30, resultDate, count);
            genRespXml(xmlFile, resultDate, BizCode.BIZ_4_30, errorRecords);
            moveFile(xmlFile, acctMonth, BizCode.BIZ_4_30, resultDate);
            FileUtil.del(file);
        }
    }

    private List<XmlResp.ErrorRecord> getPvsErrorRecords(List<SyncInterfacePvs> errDatas) {
        List<XmlResp.ErrorRecord> errorRecords = Lists.newArrayList();
        for (SyncInterfacePvs pvs : errDatas) {

            XmlResp.ErrorRecord errorRecord = new XmlResp.ErrorRecord();
            String errorNodeStr = pvs.getProvCode() + "," + pvs.getIsToC() + "," + pvs.getCustomerProvinceNumber() + "," +
                    pvs.getCustomerName() + "," + pvs.getProductSubsId() + "," + pvs.getProductId() + "," +
                    pvs.getProductName() + "," + pvs.getDbProductId() + "," + pvs.getDbProductName() + "," +
                    pvs.getCoProductId() + "," + pvs.getBlProductId() + "," + pvs.getOneProductId() + "," +
                    pvs.getBillingTerm() + "," + pvs.getPayTerm() + "," + pvs.getProdChargeCode() + "," +
                    pvs.getOneProChargeCode() + "," + pvs.getOneProChargeName() + "," + pvs.getDbProdChargeCode() + "," +
                    pvs.getFeeVal() + "," + pvs.getTaxRate() + "," + pvs.getTax() + "," +
                    pvs.getFeeNoTax() + "," + pvs.getFeeFlag() + "," + pvs.getDiscountAmount() + "," +
                    pvs.getStandardFee() + "," + pvs.getSettleFee();
            errorRecord.setErrorCode(pvs.getStatus());
            errorRecord.setErrorNode(errorNodeStr);
            errorRecords.add(errorRecord);
        }
        return errorRecords;
    }

    private List<XmlResp.ErrorRecord> getPvsTocErrorRecords(List<SyncInterfacePvsToc> errDatas) {
        List<XmlResp.ErrorRecord> errorRecords = Lists.newArrayList();
        for (SyncInterfacePvsToc pvsToc : errDatas) {

            XmlResp.ErrorRecord errorRecord = new XmlResp.ErrorRecord();
            String errorNodeStr = pvsToc.getProvCode() + "," + pvsToc.getIsToC() + "," + pvsToc.getProductSubsId() + "," +
                    pvsToc.getProductId() + "," + pvsToc.getProductName() + "," + pvsToc.getDbProductId() + "," + pvsToc.getDbProductName() + "," +
                    pvsToc.getPvProductClass() + "," + pvsToc.getBillingTerm() + "," + pvsToc.getPayTerm() + "," + pvsToc.getFeeVal() + "," +
                    pvsToc.getTaxRate() + "," + pvsToc.getTax() + "," + pvsToc.getFeeNoTax() + "," + pvsToc.getPvSettleRate();
            errorRecord.setErrorCode(pvsToc.getStatus());
            errorRecord.setErrorNode(errorNodeStr);
            errorRecords.add(errorRecord);
        }
        return errorRecords;
    }

    @Override
    @Async
    public void processCloud_4_29(String acctMonth) {
        ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_29);
        if (!lock.tryLock()) {
            log.info("4.29接口文件正在处理中，本次不处理");
            return;
        }
        try {
            //1.扫描4.29接口文件
            List<File> xmlFiles = this.scanXmlFile(acctMonth, BizCode.BIZ_4_29);
            if (CollectionUtils.isEmpty(xmlFiles)) {
                return;
            }
            //清理表数据
            clearData(acctMonth, null,BizCode.BIZ_4_29);
            for (File xmlFile : xmlFiles) {
                //2.处理文件
                File file = this.procOnce(xmlFile, acctMonth, BizCode.BIZ_4_29);
                if (file == null) {
                    continue;
                }
                savePvBill(acctMonth, xmlFile, file);
            }
        } catch (Exception e) {
            log.error("4.29接口文件处理失败", e);
        } finally {
            lock.unlock();
        }
    }

    private void savePvBill(String acctMonth, File xmlFile, File file) {
        List<XmlResp.ErrorRecord> errorRecords = null;
        ResultDate resultDate = ResultDate.success();
        List<SyncInterfacePvb> pvbList = new ArrayList<>();
        List<SyncInterfacePvbAdj> pvbAdjList = new ArrayList<>();
        long count = 0;
        try {
            List<String> lines = FileUtil.readUtf8Lines(file);
            //移除空白行
            lines.removeAll(Arrays.asList("", null));
            if (lines.isEmpty()) {
                log.warn("PVBill input file is empty");
                resultDate = ResultDate.fail("PVBill input file is empty");
                return;
            }
            count = lines.size();
            log.info("PVBill input file details path:{}, row:{}", file.getAbsolutePath(), lines.size());
            for (String line : lines) {
                if (line.contains("AdjustFee!!!!")) {
                    pvBillService.pvbAdj(acctMonth, xmlFile.getName(), pvbAdjList, line);
                } else {
                    pvBillService.pvb(acctMonth, xmlFile.getName(), pvbList, line);
                }
            }
            //保存数据
            pvBillService.savePVBAdj(acctMonth, pvbAdjList);
            pvBillService.savePVB(acctMonth, pvbList);
            log.info("PVBill 执行成功 fileName:{} acctMonth:{}", xmlFile.getName(), acctMonth);
        } catch (Exception e) {
            log.error("PVBill 执行失败 fileName:{} acctMonth:{} error：{}", xmlFile.getName(), acctMonth, e.getMessage(), e);
            resultDate = ResultDate.fail(RespCode.ERR_99_F, "文件处理失败");

        } finally {
            saveLog(xmlFile, BizCode.BIZ_4_29, resultDate, count);
            genRespXml(xmlFile, resultDate, BizCode.BIZ_4_29, errorRecords);
            moveFile(xmlFile, acctMonth, BizCode.BIZ_4_29, resultDate);

            FileUtil.del(file);
        }
    }

    @Override
    @Async
    public void process4_6(String acctMonth) {
        ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_6);
        if (!lock.tryLock()) {
            log.info("4.6接口文件正在处理中，本次不处理");
            return;
        }
        try {
            //1.扫描4.6接口文件
            List<File> xmlFiles = this.scanXmlFile(acctMonth, BizCode.BIZ_4_6);
            if (CollectionUtils.isEmpty(xmlFiles)) {
                return;
            }
            //清理表数据
            clearData(acctMonth,null, BizCode.BIZ_4_6);
            for (File xmlFile : xmlFiles) {
                //2.处理文件
                File file = this.procOnce(xmlFile, acctMonth, BizCode.BIZ_4_6);
                if (file == null) {
                    continue;
                }
                saveEsp(acctMonth, xmlFile, file);
            }
        } catch (Exception e) {
            log.error("4.6接口文件处理失败", e);
        } finally {
            lock.unlock();
        }
    }

    private void saveEsp(String acctMonth, File xmlFile, File file) {
        List<XmlResp.ErrorRecord> errorRecords = null;
        ResultDate resultDate = ResultDate.success();
        List<SyncInterfaceEsp> espList = new ArrayList<>();
        List<SyncInterfaceEspP2c> espP2cList = new ArrayList<>();
        List<SyncInterfaceEspP2p> espP2pList = new ArrayList<>();
        List<SyncInterfaceEspPart> espPartList = new ArrayList<>();
        long count = 0;
        try {
            List<String> lines = FileUtil.readUtf8Lines(file);
            lines.removeAll(Arrays.asList("", null));
            if (lines.isEmpty()) {
                log.warn("ESP input file is empty");
                resultDate = ResultDate.fail("ESP input file is empty");
                return;
            }
            log.info("ESP input file details path:{}, row:{}", file.getAbsolutePath(), lines.size());
            for (String line : lines) {
                if (line.contains("!!!!P2P!!!!")) {
                    syncEspService.optP2p(acctMonth, xmlFile.getName(), espP2pList, line);
                } else if (line.contains("!!!!P2C!!!!")) {
                    syncEspService.optP2c(acctMonth, xmlFile.getName(), espP2cList, line);
                } else if (line.contains("!!!!PARTNER!!!!")) {
                    syncEspService.optP2Partner(acctMonth, xmlFile.getName(), espPartList, line);
                } else {
                    count++;
                    syncEspService.optEsp(acctMonth, xmlFile.getName(), espList, line);
                }
            }
            //保存数据
            syncEspService.saveEsp(acctMonth, espList);
            syncEspService.saveP2p(acctMonth, espP2pList);
            syncEspService.saveP2c(acctMonth, espP2cList);
            syncEspService.saveP2Partner(acctMonth, espPartList);
            //更新状态为23
            syncEspService.updateStatus23(acctMonth, xmlFile.getName());

            List<SyncInterfaceEsp> errDataList = syncEspService.queryErrData(acctMonth, xmlFile.getName());
            if (CollectionUtils.isNotEmpty(errDataList)) {
                errorRecords = getErrorEspRecords(errDataList);
            }
            if (CollectionUtils.isNotEmpty(errorRecords)) {
                resultDate = ResultDate.fail(RespCode.ERR_Line, "ESP数据校验失败");
            }
            log.info("ESP_ACC 执行成功 fileName:{} acctMonth:{}", xmlFile.getName(), acctMonth);
        } catch (Exception e) {
            log.error("ESP 执行失败 fileName:{} acctMonth:{} error：{}", xmlFile.getName(), acctMonth, e.getMessage(), e);
            resultDate = ResultDate.fail(RespCode.ERR_F099_F, "文件处理失败");

        } finally {
            saveLog(xmlFile, BizCode.BIZ_4_6, resultDate, count);
            genRespXml(xmlFile, resultDate, BizCode.BIZ_4_6, errorRecords);
            moveFile(xmlFile, acctMonth, BizCode.BIZ_4_6, resultDate);
            //删除txt文件
            FileUtil.del(file);
        }
    }

    private List<XmlResp.ErrorRecord> getErrorEspRecords(List<SyncInterfaceEsp> errDataList) {
        List<XmlResp.ErrorRecord> errorRecords = Lists.newArrayList();
        for (SyncInterfaceEsp esp : errDataList) {
            XmlResp.ErrorRecord errorRecord = new XmlResp.ErrorRecord();
            String errorNodeStr = esp.getGroupCustomerName()+","+ esp.getProvCode() + "," +
                    esp.getEbossCustomerName()+","+esp.getAddressProvCode();
            errorRecord.setErrorCode(esp.getStatus());
            errorRecord.setErrorNode(errorNodeStr);
            errorRecords.add(errorRecord);
        }
        return errorRecords;
    }

    /**
     * 移动云 4.16 文件接口
     * @param acctMonth
     * @throws Exception
     */
    @Override
    @Async
    public void processCloud_4_16(String acctMonth) {
        ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_16);
        if (!lock.tryLock()) {
            log.info("4.16接口文件正在处理中，本次不处理");
            return;
        }
        try {
            //1.扫描4.16接口文件
            List<File> xmlFiles = this.scanXmlFile(acctMonth, BizCode.BIZ_4_16);
            if (CollectionUtils.isEmpty(xmlFiles)) {
                return;
            }
            //清理表数据
            clearData(acctMonth,null, BizCode.BIZ_4_16);
            for (File xmlFile : xmlFiles) {
                //2.处理文件
                File file = this.procOnce(xmlFile, acctMonth, BizCode.BIZ_4_16);
                if (file == null) {
                    continue;
                }
                this.saveMc(acctMonth, xmlFile, file);
            }
        } catch (Exception e) {
            log.error("4.16接口文件处理失败", e);
        } finally {
            lock.unlock();
        }
    }

    private void saveMc(String acctMonth, File xmlFile, File file) {
        String fileName = file.getName();
        List<SyncInterfaceMc> syncInterfaceMcs = new ArrayList<>();
        List<SyncInterfaceMcP2C> syncInterfaceMcP2CLst = new ArrayList<>();
        List<SyncInterfaceMcP2P> syncInterfaceMcP2PLst = new ArrayList<>();
        List<SyncInterfaceMcNMG> syncInterfaceMcNMGLst = new ArrayList<>();
        List<XmlResp.ErrorRecord> errorRecords = null;
        ResultDate resultDate = ResultDate.success();
        try {
            List<String> mcLines = FileUtil.readUtf8Lines(file);
            mcLines.removeAll(Arrays.asList("", null));
            if (mcLines.isEmpty()) {
                log.warn("MC input file is empty");
                resultDate = ResultDate.fail("MC input file is empty");
                return;
            }
            log.info("mc input file details path:{}, row:{}", file.getAbsolutePath(), mcLines.size());

            for (String mcLine : mcLines) {
                if (mcLine.contains("!!!!P2P!!!!")) {
                    syncInterfaceMcService.optP2P(acctMonth, fileName, syncInterfaceMcP2PLst, mcLine);
                } else if (mcLine.contains("!!!!P2C!!!!")) {
                    syncInterfaceMcService.optP2C(acctMonth, fileName, syncInterfaceMcP2CLst, mcLine);
                }else if (mcLine.contains("!!!!NMG_TAG!!!!")) {
                    syncInterfaceMcService.optNMG(acctMonth, fileName, syncInterfaceMcNMGLst, mcLine);
                }
                else {
                    syncInterfaceMcService.optMc(acctMonth, fileName, syncInterfaceMcs, mcLine);
                }
            }
            syncInterfaceMcService.saveSyncInterfaceMc(acctMonth, syncInterfaceMcs);
            syncInterfaceMcService.saveSyncInterfaceMcP2P(acctMonth, syncInterfaceMcP2PLst);
            syncInterfaceMcService.saveSyncInterfaceMcP2C(acctMonth, syncInterfaceMcP2CLst);
            syncInterfaceMcService.saveSyncInterfaceMcNMG(acctMonth, syncInterfaceMcNMGLst);

            //更新状态为23
            syncInterfaceMcService.updateStatus23(acctMonth, xmlFile.getName());
            //更新状态为24
            syncInterfaceMcService.updateStatus24(acctMonth, xmlFile.getName());
            //更新受理模型
            syncInterfaceMcService.updateOrderMode(acctMonth, xmlFile.getName());
            //更新状态为25
            syncInterfaceMcService.updateStatus25(acctMonth, xmlFile.getName());
            //更新状态为99
            syncInterfaceMcService.updateStatus99(acctMonth, xmlFile.getName());
            //更新状态为正常状态
            syncInterfaceMcService.updateStatus0(acctMonth, xmlFile.getName());

            log.info("mc update data finish fileName:{} acctMonth:{}", fileName, acctMonth);
            List<SyncInterfaceMc> errDatas = syncInterfaceMcService.queryErrorByFileName(acctMonth, fileName);
            if (CollectionUtils.isNotEmpty(errDatas)) {
                errorRecords = getErrorRecords(errDatas);
                resultDate = ResultDate.fail(RespCode.ERR_Line, "MC数据校验失败");
            }
            log.info("MC 执行成功 fileName:{} acctMonth:{}", xmlFile.getName(), acctMonth);
        } catch (Exception e) {
            log.error("mc 执行失败 fileName:{} acctMonth:{} error：{}", fileName, acctMonth, e.getMessage(), e);
            resultDate = ResultDate.fail(RespCode.ERR_99_F, "文件处理失败");
            syncInterfaceMcService.deleteByFileNameMonth(acctMonth, fileName);
        } finally {
            saveLog(xmlFile, BizCode.BIZ_4_16, resultDate, (long) syncInterfaceMcs.size());
            genRespXml(xmlFile, resultDate, BizCode.BIZ_4_16, errorRecords);
            moveFile(xmlFile, acctMonth, BizCode.BIZ_4_16, resultDate);
            FileUtil.del(file);
        }
    }

    private List<XmlResp.ErrorRecord> getErrorRecords(List<SyncInterfaceMc> errDatas) {
        List<XmlResp.ErrorRecord> errorRecords = Lists.newArrayList();
        for (SyncInterfaceMc syncInterfaceMc : errDatas) {

            XmlResp.ErrorRecord errorRecord = new XmlResp.ErrorRecord();
            String errorNodeStr = syncInterfaceMc.getProvCode() + "," + syncInterfaceMc.getPayTag() + "," + syncInterfaceMc.getCustomerName() + "," +
                    syncInterfaceMc.getCityCode() + "," + syncInterfaceMc.getCreatorName() + "," + syncInterfaceMc.getEcId() + "," + syncInterfaceMc.getOrderMode() + "," +
                    syncInterfaceMc.getPoSubsId() + "," + syncInterfaceMc.getPoId() + "," + syncInterfaceMc.getPoName() + "," + syncInterfaceMc.getProductSubsId() + "," +
                    syncInterfaceMc.getProductId() + "," + syncInterfaceMc.getProductName() + "," + syncInterfaceMc.getDbProductId() + "," + syncInterfaceMc.getDbProductName() + "," +
                    syncInterfaceMc.getCoProductId() + "," + syncInterfaceMc.getBlProductId() + "," + syncInterfaceMc.getOneProductId() + "," + syncInterfaceMc.getBillingTerm();

            errorRecord.setErrorCode(syncInterfaceMc.getStatus());
            errorRecord.setErrorNode(errorNodeStr);
            errorRecords.add(errorRecord);
        }
        return errorRecords;
    }

    private void clearData(String acctMonth,String filename, BizCode bizCode) {
        switch (bizCode) {
            case BIZ_4_16:
                syncInterfaceMcService.deleteMcByMonth(acctMonth,filename);
                break;
            case BIZ_4_30:
                pvSettleService.truncate(acctMonth);
                break;
            case BIZ_4_29:
                pvBillService.deleteByAcctMonth(acctMonth);
                break;
            case BIZ_4_6:
                syncEspService.truncate(acctMonth);
                break;
            default:
                return;

        }
    }

    private List<File> scanXmlFile(String acctMonth, BizCode bizCode) {
        String filePath = "";
        String regFileName = "";
        switch (bizCode) {
            case BIZ_4_16:
                filePath = mcConfig.getDataPath();
                regFileName = mcConfig.getValidName();
                break;
            case BIZ_4_29:
                filePath = pvBillConfig.getDataPath();
                regFileName = pvBillConfig.getValidName();
                break;
            case BIZ_4_30:
                filePath = pvSettleConfig.getDataPath();
                regFileName = pvSettleConfig.getValidName();
                break;
            case BIZ_4_6:
                filePath = espAccConfig.getDataPath();
                regFileName = espAccConfig.getValidName();
                break;
            default:
                log.error("未知的业务类型:{}", bizCode.getInterfaceNum());
                return null;
        }
        Path directory = Paths.get(filePath);
        if (!Files.exists(directory)) {
            log.warn("directory:{} not exists,mkdir", directory);
            FileUtil.mkdir(directory.toFile());
        }

        Collection<File> listFiles = FileUtils.listFiles(new File(filePath), new CustomerFileFilter(regFileName), null);
        if (CollectionUtils.isEmpty(listFiles)) {
            log.info("未扫描到{}接口目录文件:{},{}", bizCode.getInterfaceNum(), filePath, regFileName);
            return null;
        }
        List<File> collect = Lists.newArrayList();
        for (File file : listFiles) {
            if (file.getName().contains(acctMonth)) {
                log.info("扫描到账期{},{}接口文件:{},{}", acctMonth, bizCode.getInterfaceName(), file.getName(), regFileName);
                collect.add(file);
            }
        }
        if (CollectionUtils.isEmpty(collect)) {
            log.info("未扫描到账期{},{}接口文件:{},{}", acctMonth, bizCode.getInterfaceName(), filePath, regFileName);
            return null;
        }
        return collect;
    }

    private File procOnce(File xmlFile, String acctMonth, BizCode bizCode) {
        //2.校验文件名是否符合规范
        ResultDate validate = xmlValidate(xmlFile, bizCode);
        if (!validate.isSuccess()) {
            log.error("文件格式不符合规范:{}", xmlFile.getName());
        } else {
            //3.解析文件
            validate = xmlParser(xmlFile, bizCode);
            if (!validate.isSuccess()) {
                log.error("文件解析失败:{}", xmlFile.getName());
            }
        }
        if (!validate.isSuccess()) {
            this.saveLog(xmlFile, bizCode, validate, 0L);
            this.genRespXml(xmlFile, validate, bizCode, null);
            this.moveFile(xmlFile, acctMonth, bizCode, validate);
        }
        return validate.getTxtFile();

    }

    private void moveFile(File xmlFile, String acctMonth, BizCode bizCode, ResultDate validate) {
        String errorPath = "";
        String archivePath = "";
        switch (bizCode) {
            case BIZ_4_16:
                errorPath = mcConfig.getErrorPath();
                archivePath = mcConfig.getBackupPath();
                break;
            case BIZ_4_6:
                errorPath = espAccConfig.getErrorPath();
                archivePath = espAccConfig.getBackupPath();
                break;
            case BIZ_4_29:
                errorPath = pvBillConfig.getErrorPath();
                archivePath = pvBillConfig.getBackupPath();
                break;
            case BIZ_4_30:
                errorPath = pvSettleConfig.getErrorPath();
                archivePath = pvSettleConfig.getBackupPath();
                break;
            default:
                return;
        }
        Path dest = Paths.get(archivePath, acctMonth);
        if (!validate.isSuccess()) {
            dest = Paths.get(errorPath, acctMonth);
        }
        FileUtil.mkdir(dest.toFile());
        FileUtil.move(xmlFile, dest.toFile(), true);

    }

    private void saveLog(File xmlFile, BizCode bizCode, ResultDate validate, Long rowCount) {
        LogXml logXml = new LogXml();
        logXml.setDirection(1);
        logXml.setFileName(xmlFile.getName());
        logXml.setFileSize(xmlFile.length());
        logXml.setBizType(bizCode.getBizType());
        logXml.setRowCount(rowCount);
        logXml.setErrCode(validate.getCode());
        logXml.setErrMessage(validate.getMessage());
        logXmlService.saveLogXml(logXml);
    }

    private void genRespXml(File xmlFile, ResultDate validate, BizCode bizCode, List<XmlResp.ErrorRecord> errorRecords) {
        String respPath = "";
        switch (bizCode) {
            case BIZ_4_16:
                respPath = mcConfig.getRespPath();
                break;
            case BIZ_4_6:
                respPath = espAccConfig.getRespPath();
                break;
            case BIZ_4_29:
                respPath = pvBillConfig.getRespPath();
                break;
            case BIZ_4_30:
                respPath = pvSettleConfig.getRespPath();
                break;
            default:
                return;
        }
        XmlResp xmlResp = new XmlResp();
        xmlResp.setOrgFileName(xmlFile.getName());
        xmlResp.setRespDate(new Date());
        if (validate.isSuccess()) {
            xmlResp.setFileStatus("0");
        } else {
            xmlResp.setFileStatus(validate.getFileStatus());
            if ("2".equals(validate.getFileStatus()) && CollectionUtils.isNotEmpty(errorRecords)) {
                //记录级错误
                xmlResp.setErrorRecords(errorRecords);
            } else {
                //文件级错误
                xmlResp.setFileStatus("1");
                xmlResp.setErrorCode(validate.getCode());
                xmlResp.setErrorNode(validate.getMessage());
            }
        }
        try {
            String respFileName = "Resp_" + xmlFile.getName();
            File respFile = new File(respPath, respFileName);
            FileUtil.mkdir(respFile.getParentFile());
            JAXBUtil.objectToXML(xmlResp, respFile.getAbsolutePath(), false);
        } catch (IOException e) {
            log.error("生成响应文件失败:{}", xmlFile.getName(), e);
        }
    }

    private ResultDate xmlValidate(File xmlFile, BizCode bizCode) {
        try {
            Validator validator = this.getValidator(bizCode);
            if (validator == null) {
                return ResultDate.fail("未知的业务类型");
            }
            DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder parser = documentBuilderFactory.newDocumentBuilder();
            org.w3c.dom.Document document = parser.parse(xmlFile);
            //校验xml格式
            validator.validate(new DOMSource(document));
        } catch (SAXException | IOException | ParserConfigurationException e) {
            log.error("xmlValidate error,file:{}", xmlFile.getAbsolutePath(), e);
            if (bizCode == BizCode.BIZ_4_6) {
                return ResultDate.fail(RespCode.ERR_F904_2, e.getMessage());
            }
            return ResultDate.fail(RespCode.ERR_22, e.getMessage());
        }
        return ResultDate.success();
    }

    private Validator getValidator(BizCode bizCode) throws SAXException {
        Validator validator = EspBizCache.getValidator2(bizCode);
        if (validator != null) {
            return validator;
        }
        String xsdPath = "";
        switch (bizCode) {
            case BIZ_4_16:
                xsdPath = mcConfig.getXsd();
                break;
            case BIZ_4_6:
                xsdPath = espAccConfig.getXsd();
                break;
            case BIZ_4_30:
                xsdPath = pvSettleConfig.getXsd();
                break;
            case BIZ_4_29:
                xsdPath = pvBillConfig.getXsd();
                break;
            default:
                log.error("未知的业务类型:{}", bizCode.getInterfaceNum());
                return null;
        }
        InputStream stream = ResourceUtil.getStream(xsdPath);
        try {
            StreamSource schemaFile = new StreamSource(stream);
            SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            Schema schema = schemaFactory.newSchema(schemaFile);
            validator = schema.newValidator();
            EspBizCache.putValidator2(bizCode, validator);
        } finally {
            IoUtil.close(stream);
        }
        return validator;
    }

    private ResultDate xmlParser(File xmlFile, BizCode bizCode) {
        String xslt = "";
        String txtPath = "";
        switch (bizCode) {
            case BIZ_4_16:
                xslt = mcConfig.getXslt();
                txtPath = mcConfig.getTxtPath();
                break;
            case BIZ_4_6:
                xslt = espAccConfig.getXslt();
                txtPath = espAccConfig.getTxtPath();
                break;
            case BIZ_4_29:
                xslt = pvBillConfig.getXslt();
                txtPath = pvBillConfig.getTxtPath();
                break;
            case BIZ_4_30:
                xslt = pvSettleConfig.getXslt();
                txtPath = pvSettleConfig.getTxtPath();
                break;
            default:
                log.error("未知的业务类型:{}", bizCode.getInterfaceNum());
                return ResultDate.fail("未知的业务类型");
        }
        BufferedWriter writer = null;
        try {
            Transformer transformer = this.getTransformer(bizCode, xslt);
            Source xmlSource = new StreamSource(xmlFile);
            File file = new File(txtPath, xmlFile.getName());
            //buffer流
            writer = FileUtil.getWriter(file, StandardCharsets.UTF_8, false);
            transformer.transform(xmlSource, new StreamResult(writer));
            return ResultDate.success(file);
        } catch (TransformerException e) {
            log.error("xmlParser error,file:{}", xmlFile.getAbsolutePath(), e);
            if (bizCode == BizCode.BIZ_4_6) {
                return ResultDate.fail(RespCode.ERR_F904, e.getMessage());
            } else {
                return ResultDate.fail(RespCode.ERR_21, e.getMessage());
            }
        } finally {
            IoUtil.close(writer);
        }
    }

    private Transformer getTransformer(BizCode bizCode,String xslt) throws TransformerConfigurationException {
        Transformer transformer = EspBizCache.getTransformer2(bizCode);
        if (transformer != null) {
            return transformer;
        }
        InputStream stream = ResourceUtil.getStream(xslt);
        try {
            StreamSource xsltFile = new StreamSource(stream);
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            transformer = transformerFactory.newTransformer(xsltFile);
            EspBizCache.putTransformer2(bizCode, transformer);
        } finally {
            IoUtil.close(stream);
        }
        return transformer;
    }


    private boolean validateXMLAgainstXSD(String xmlFilePath, StringBuffer errDesc) {
        InputStream xsdInputStream = null;
        try {
            // 创建 SchemaFactory
            SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
//            Resource xsdResource = resourceLoader.getResource("classpath:conf/xmlcfg/xsd/BC2C.xsd");
            xsdInputStream  = ResourceUtil.getStream("classpath:conf/xmlcfg/xsd/BC2C.xsd");
            // 创建 Schema 对象
//            InputStream xsdInputStream = xsdResource.getInputStream();
            StreamSource xsdStreamSource = new StreamSource(xsdInputStream);
            Schema schema = schemaFactory.newSchema(xsdStreamSource);

            // 创建 Validator 对象
            Validator validator = schema.newValidator();

            // 校验 XML 文件
            Source source = new StreamSource(new File(xmlFilePath));
            validator.validate(source);
            return true; // 如果校验通过，返回 true
        } catch (SAXException | IOException e) {
            // 校验失败，打印错误信息
            errDesc.append(e.getMessage().length() > 255 ? e.getMessage().substring(0, 255) : e.getMessage());
            log.error(e.getMessage());
            return false;
        }finally {
            IoUtil.close(xsdInputStream);
        }
    }

    private BillList parseXML(File bcFile) throws DocumentException, Exception {
        BillList billList = new BillList();
        try {
            SAXReader reader = new SAXReader();
            Document document = reader.read(bcFile);
            Element rootElement = document.getRootElement();
            billList.setProvCode(rootElement.elementText("ProvCode"));
            List<ToCBizInfo> toCBizInfoList = new ArrayList<>();
            for (Element toCBizInfoElement : rootElement.element("ToCBizList").elements("ToCBizInfo")) {
                ToCBizInfo toCBizInfo = new ToCBizInfo();
                List<ProductSubsInfo> productSubsInfoList = new ArrayList<>();
                if (toCBizInfoElement.element("ProductSubsList") == null) {
                    log.info("ProductSubsList为空");
                    return billList;
                }
                for (Element subProductInfoElement : toCBizInfoElement.element("ProductSubsList").elements("SubProductInfo")) {
                    // 设置 productSubsInfo 的属性...
                    ProductSubsInfo productSubsInfo = new ProductSubsInfo();
                    productSubsInfo.setProductSubsID(subProductInfoElement.elementText("ProductSubsID"));
                    productSubsInfo.setCloudInstanceID(subProductInfoElement.elementText("CloudInstanceID"));
                    productSubsInfo.setProductID(subProductInfoElement.elementText("ProductID"));
                    productSubsInfo.setProductName(subProductInfoElement.elementText("ProductName"));
                    productSubsInfo.setDbProductID(subProductInfoElement.elementText("DbProductID"));
                    productSubsInfo.setDbProductName(subProductInfoElement.elementText("DbProductName"));
                    productSubsInfo.setGhCode(subProductInfoElement.elementText("GhCode"));
                    productSubsInfo.setPVProductClass(subProductInfoElement.elementText("PVProductClass"));
                    productSubsInfo.setIssueTime(subProductInfoElement.elementText("IssueTime"));
                    productSubsInfo.setExpireTime(subProductInfoElement.elementText("ExpireTime"));
                    productSubsInfo.setBillingTerm(subProductInfoElement.elementText("BillingTerm"));

                    List<FeeInfo> feeInfoList = new ArrayList<>();
                    for (Element feeInfoElement : subProductInfoElement.element("FeeList").elements("FeeInfo")) {
                        // 设置 feeInfo 的属性...
                        FeeInfo feeInfo = new FeeInfo();
                        feeInfo.setFeeVal(feeInfoElement.elementText("FeeVal"));
                        feeInfo.setTaxRate(feeInfoElement.elementText("TaxRate"));
                        feeInfo.setTax(feeInfoElement.elementText("Tax"));
                        feeInfo.setFeeNoTax(feeInfoElement.elementText("FeeNoTax"));

                        // 设置 Province2SpecialSettleInfo
                        List<Province2SpecialSettleInfo> province2SpecialSettle = new ArrayList<>();
                        Element province2SpecialSettleElement = feeInfoElement.element("Province2SpecialSettle");
                        if (province2SpecialSettleElement != null) {
                            for (Element specialSettleInfoElement : province2SpecialSettleElement.elements("Province2SpecialSettleinfo")) {
                                if (specialSettleInfoElement != null) {
                                    Province2SpecialSettleInfo province2SpecialSettleInfo = new Province2SpecialSettleInfo();
                                    province2SpecialSettleInfo.setSettlementPartyIn(specialSettleInfoElement.elementText("SettlementPartyIn"));
                                    province2SpecialSettleInfo.setSettlementPartyOut(specialSettleInfoElement.elementText("SettlementPartyOut"));
                                    province2SpecialSettleInfo.setSettlementRate(specialSettleInfoElement.elementText("SettlementRate"));
                                    province2SpecialSettleInfo.setSettlementType(specialSettleInfoElement.elementText("SettlementType"));
                                    province2SpecialSettleInfo.setSettlementAmount(specialSettleInfoElement.elementText("SettlementAmount"));
                                    province2SpecialSettleInfo.setSettlementClass(specialSettleInfoElement.elementText("SettlementClass"));
                                    province2SpecialSettleInfo.setReportCode(specialSettleInfoElement.elementText("ReportCode"));
                                    province2SpecialSettle.add(province2SpecialSettleInfo);
                                }
                            }
                            feeInfo.setProvince2SpecialSettle(province2SpecialSettle);

                        }

                        // 设置 Province2ProvinceSettleInfo
                        List<Province2ProvinceSettleInfo> province2ProvinceSettle = new ArrayList<>();
                        Element province2ProvinceSettleElement = feeInfoElement.element("Province2ProvinceSettle");
                        if (province2ProvinceSettleElement != null) {
                            for (Element provinceSettleInfoElement : province2ProvinceSettleElement.elements("Province2Provinceinfo")) {
                                if (provinceSettleInfoElement != null) {
                                    Province2ProvinceSettleInfo province2ProvinceSettleInfo = new Province2ProvinceSettleInfo();
                                    province2ProvinceSettleInfo.setSettlementPartyIn(provinceSettleInfoElement.elementText("SettlementPartyIn"));
                                    province2ProvinceSettleInfo.setSettlementPartyOut(provinceSettleInfoElement.elementText("SettlementPartyOut"));
                                    province2ProvinceSettleInfo.setSettlementRate(provinceSettleInfoElement.elementText("SettlementRate"));
                                    province2ProvinceSettleInfo.setSettlementType(provinceSettleInfoElement.elementText("SettlementType"));
                                    province2ProvinceSettleInfo.setSettlementAmount(provinceSettleInfoElement.elementText("SettlementAmount"));
                                    province2ProvinceSettle.add(province2ProvinceSettleInfo);
                                }
                            }
                        }
                        feeInfo.setProvince2ProvinceSettle(province2ProvinceSettle);

                        // 设置 ParSettleInfo
                        List<ParSettleInfo> parSettle = new ArrayList<>();
                        Element parSettleElement = feeInfoElement.element("ParSettle");
                        if (parSettleElement != null) {
                            for (Element parSettleInfoElement : parSettleElement.elements("ParSettleInfo")) {
                                if (parSettleInfoElement != null) {
                                    ParSettleInfo parSettleInfo = new ParSettleInfo();
                                    parSettleInfo.setPartnerCode(parSettleInfoElement.elementText("PartnerCode"));
                                    parSettleInfo.setPartnerName(parSettleInfoElement.elementText("PartnerName"));
                                    parSettleInfo.setParSettleRate(parSettleInfoElement.elementText("ParSettleRate"));
                                    parSettleInfo.setSettlementType(parSettleInfoElement.elementText("SettlementType"));
                                    parSettleInfo.setParSettlAmount(parSettleInfoElement.elementText("ParSettlAmount"));
                                    parSettleInfo.setParResSettlRate(parSettleInfoElement.elementText("ParResSettlRate"));
                                    parSettle.add(parSettleInfo);
                                }
                            }
                        }
                        feeInfo.setParSettle(parSettle);
                        feeInfoList.add(feeInfo);
                    }
                    productSubsInfo.setFeeList(feeInfoList);
                    productSubsInfoList.add(productSubsInfo);
                }

                toCBizInfo.setProductSubsList(productSubsInfoList);
                toCBizInfoList.add(toCBizInfo);
            }
            billList.setToCBizList(toCBizInfoList);

            return billList;
        } catch (DocumentException de) {
            log.error("{}文件解析失败：{}", bcFile.getName(), de.getMessage());
            throw de;
        } catch (Exception e) {
            log.error("{}文件解析失败：{}", bcFile.getName(), e.getMessage());
            throw e;
        }
    }

    private void optPART(String acctMonth, String
            fileName, List<SyncInterfaceBc2cPart> syncInterfaceBc2cPartList, BillList billList) {
        for (ToCBizInfo toCBizInfo : billList.getToCBizList()) {
            if (CollUtil.isEmpty(toCBizInfo.getProductSubsList())) {
                log.error("ProductSubsList 数据为空，");
            } else {
                for (ProductSubsInfo psi : toCBizInfo.getProductSubsList()) {
                    for (FeeInfo feeInfo : psi.getFeeList()) {
                        for (ParSettleInfo p2si : feeInfo.getParSettle()) {
                            SyncInterfaceBc2cPart cPS = new SyncInterfaceBc2cPart();
                            cPS.setId(UUID.randomUUID().toString());
                            cPS.setAcctMonth(acctMonth);
                            cPS.setProvCode(billList.getProvCode());
                            cPS.setProductSubsId(psi.getProductSubsID());
                            cPS.setCloudInstanceId(psi.getCloudInstanceID());
                            cPS.setProductId(psi.getProductID());
                            cPS.setProductName(psi.getProductName());
                            cPS.setDbProductId(psi.getDbProductID());
                            cPS.setDbProductName(psi.getDbProductName());
                            cPS.setGhCode(psi.getGhCode());
                            cPS.setPvProductClass(psi.getPVProductClass());
                            cPS.setIssueTime(psi.getIssueTime());
                            cPS.setExpireTime(psi.getExpireTime());
                            cPS.setBillingTerm(psi.getBillingTerm());
                            cPS.setFeeVal(Double.valueOf(StringUtils.isNotBlank(feeInfo.getFeeVal()) ? feeInfo.getFeeVal() : "0"));
                            cPS.setTaxRate(Double.valueOf(StringUtils.isNotBlank(feeInfo.getTaxRate()) ? feeInfo.getTaxRate() : "0"));
                            cPS.setTax(Double.valueOf(StringUtils.isNotBlank(feeInfo.getTax()) ? feeInfo.getTax() : "0"));
                            cPS.setFeeNoTax(Double.valueOf(StringUtils.isNotBlank(feeInfo.getFeeNoTax()) ? feeInfo.getFeeNoTax() : "0"));
                            cPS.setPartnerCode(p2si.getPartnerCode());
                            cPS.setPartnerName(p2si.getPartnerName());
                            cPS.setParSettleRate(p2si.getParSettleRate());
                            cPS.setSettlementType(p2si.getSettlementType());
                            cPS.setParSettlAmount(Double.valueOf(StringUtils.isNotBlank(p2si.getParSettlAmount()) ? p2si.getParSettlAmount() : "0"));
                            cPS.setParResSettlRate(p2si.getParResSettlRate());
                            cPS.setFileName(fileName);
                            syncInterfaceBc2cPartList.add(cPS);
                        }
                    }
                }
            }
        }
    }

    private void optPS(String acctMonth, String
            fileName, List<SyncInterfaceBc2cPS> syncInterfaceMcs, BillList billList) {
        for (ToCBizInfo toCBizInfo : billList.getToCBizList()) {
            if (CollUtil.isEmpty(toCBizInfo.getProductSubsList())) {
                log.error("ProductSubsList 数据为空，");
            } else {
                for (ProductSubsInfo psi : toCBizInfo.getProductSubsList()) {
                    for (FeeInfo feeInfo : psi.getFeeList()) {
                        for (Province2SpecialSettleInfo p2si : feeInfo.getProvince2SpecialSettle()) {
                            SyncInterfaceBc2cPS cPS = new SyncInterfaceBc2cPS();
                            cPS.setId(UUID.randomUUID().toString());
                            cPS.setAcctMonth(acctMonth);
                            cPS.setProvCode(billList.getProvCode());
                            cPS.setProductSubsId(psi.getProductSubsID());
                            cPS.setCloudInstanceId(psi.getCloudInstanceID());
                            cPS.setProductId(psi.getProductID());
                            cPS.setProductName(psi.getProductName());
                            cPS.setDbProductId(psi.getDbProductID());
                            cPS.setDbProductName(psi.getDbProductName());
                            cPS.setGhCode(psi.getGhCode());
                            cPS.setPvProductClass(psi.getPVProductClass());
                            cPS.setIssueTime(psi.getIssueTime());
                            cPS.setExpireTime(psi.getExpireTime());
                            cPS.setBillingTerm(psi.getBillingTerm());
                            cPS.setFeeVal(Double.valueOf(StringUtils.isNotBlank(feeInfo.getFeeVal()) ? feeInfo.getFeeVal() : "0"));
                            cPS.setTaxRate(Double.valueOf(StringUtils.isNotBlank(feeInfo.getTaxRate()) ? feeInfo.getTaxRate() : "0"));
                            cPS.setTax(Double.valueOf(StringUtils.isNotBlank(feeInfo.getTax()) ? feeInfo.getTax() : "0"));
                            cPS.setFeeNoTax(Double.valueOf(StringUtils.isNotBlank(feeInfo.getFeeNoTax()) ? feeInfo.getFeeNoTax() : "0"));
                            cPS.setSettlementPartyIn(p2si.getSettlementPartyIn());
                            cPS.setSettlementPartyOut(p2si.getSettlementPartyOut());
                            cPS.setSettlementRate(p2si.getSettlementRate());
                            cPS.setSettlementType(p2si.getSettlementType());
                            cPS.setSettlementAmount(Double.valueOf(StringUtils.isNotBlank(p2si.getSettlementAmount()) ? p2si.getSettlementAmount() : "0"));
                            cPS.setSettlementClass(p2si.getSettlementClass());
                            cPS.setReportCode(p2si.getReportCode());
                            cPS.setFileName(fileName);
                            syncInterfaceMcs.add(cPS);
                        }
                    }
                }
            }
        }
    }
}
