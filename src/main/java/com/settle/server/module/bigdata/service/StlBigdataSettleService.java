package com.settle.server.module.bigdata.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.bigdata.StlBigdataSettleMapper;
import com.settle.server.entity.bigdata.StlBigdataSettle;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Service
public class StlBigdataSettleService extends ServiceImpl<StlBigdataSettleMapper, StlBigdataSettle> {

    @Transactional(rollbackFor = RuntimeException.class)
    public void insertBatch(List<StlBigdataSettle> list) {

        List<List<StlBigdataSettle>> partition = Lists.partition(list, 500);
        for (List<StlBigdataSettle> stlBigdataSettles : partition) {
            getBaseMapper().insertBatch(stlBigdataSettles);
        }
    }

    public void deleteByAcctMonth(String acctMonth) {
        //先删除
        getBaseMapper().delete(new LambdaQueryWrapper<>(StlBigdataSettle.class)
                .eq(StlBigdataSettle::getOpTime, acctMonth));
    }
}
