package com.settle.server.module.cdn.record;

/**
 * 通用记录实现
 *
 * <AUTHOR>
 */
public class GenericRecord<P> implements Record<P> {

    protected Header header;
    protected P payload;

    /**
     * 创建一个新的 {@link GenericRecord}
     *
     * @param header  记录头
     * @param payload 记录有效载荷
     */
    public GenericRecord(final Header header, final P payload) {
        this.header = header;
        this.payload = payload;
    }

    @Override
    public Header getHeader() {
        return header;
    }

    @Override
    public P getPayload() {
        return payload;
    }

    @Override
    public String toString() {
        return "Record: {" +
                "header=[" + header +
                "], payload=[" + payload +
                "]}";
    }
}
