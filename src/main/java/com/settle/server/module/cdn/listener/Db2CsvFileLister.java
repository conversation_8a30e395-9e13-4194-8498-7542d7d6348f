package com.settle.server.module.cdn.listener;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.setting.Setting;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.constant.RuleConst;
import com.settle.server.module.cdn.job.JobMetrics;
import com.settle.server.module.cdn.job.JobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.File;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 缺省文件监听器
 *
 * <AUTHOR>
 */
public class Db2CsvFileLister implements FileListener {
    private static final Logger log = LoggerFactory.getLogger(Db2CsvFileLister.class);
    /**
     * 变量Map
     */
    private Map<String, String> variableMap;
    /**
     * 日志ID
     */
    private String logId;

    /**
     * 数据文件
     */
    private File dataFile;

    /**
     * work目录文件
     */
    private File workFile;

    /**
     * 规则设置
     */
    private Setting ruleSetting;

    /**
     * 规则分组
     */
    private String ruleGroup;

    private DataSource dataSource;


    public Db2CsvFileLister(final String logId, final File dataFile, final String ruleGroup, final Setting ruleSetting,DataSource dataSource) {
        this.logId = logId;
        this.dataFile = dataFile;
        this.ruleSetting = ruleSetting;
        this.ruleGroup = ruleGroup;
        this.variableMap = buildVariableMap();
        this.dataSource = dataSource;
    }

    @Override
    public void beforeFileProcessing() {
        try {
            DateTime dateTimeNow = DateTime.now();
            Db.use(dataSource).tx(db -> {
                db.insert(Entity.create(NormalConst.DEFAULT_LOG_TABLE).set("id", this.logId)
                        .set("file_name", this.dataFile.getName())
                        .set("action", "db2csv")
                        .set("create_date", dateTimeNow)
                        .set("status", JobStatus.STARTING.name())
                        .set("error_code", 0)
                        .set("error_desc", "")
                        .set("read_count", -1)
                        .set("write_count", -1)
                        .set("filter_count", -1)
                        .set("error_count", -1));
            });
        } catch (SQLException ex) {
            ex.printStackTrace();
        }

    }

    @Override
    public void afterFileProcessing(final JobMetrics jobMetrics) {
        String finalFileName = this.dataFile.getName();
        log.info("临时文件名：{}",finalFileName);
        if (FileUtil.size(this.dataFile) == 0) {
            finalFileName = this.ruleSetting.getStr(RuleConst.G_K_EMPTY_FILE_NAME_REG, this.ruleGroup, this.dataFile.getName());
            finalFileName = StrUtil.format(finalFileName, variableMap);

            FileUtil.rename(this.dataFile, finalFileName, true);
        }else {
            finalFileName=this.ruleSetting.getStr(RuleConst.G_K_FILE_NAME_REG, this.ruleGroup, NormalConst.DEFAULT_OUT_FILE_NAME);
            finalFileName = StrUtil.format(finalFileName, variableMap);
            log.info("修改后的文件名:{}",finalFileName);
            FileUtil.rename(this.dataFile, finalFileName, true);
        }

        try {
            String finalFileName1 = finalFileName;
            Db.use(dataSource).tx(db ->
                    db.update(Entity.create(NormalConst.DEFAULT_LOG_TABLE)
                            .set("status", JobStatus.COMPLETED.name())
                            .set("file_name", finalFileName1)
                            .set("error_code", "000")
                            .set("error_desc", "")
                            .set("read_count", jobMetrics.getReadCount())
                            .set("write_count", jobMetrics.getWriteCount())
                            .set("filter_count", jobMetrics.getFilterCount())
                            .set("error_count", jobMetrics.getErrorCount()), Entity.create().set("id", this.logId)));

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onFileProcessingException(final Throwable throwable) {
        try {
            Db.use(dataSource).update(Entity.create(NormalConst.DEFAULT_LOG_TABLE)
                            .set("status", JobStatus.FAILED.name())
                            .set("error_code", "E110")
                            .set("error_desc", StrUtil.sub(throwable.getMessage(), 0, 255))
                    , Entity.create().set("id", this.logId));

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private Map<String, String> buildVariableMap() {
        Map<String, String> variableMap = new HashMap<>(16);
        String acctMonth = this.ruleSetting.get(null, NormalConst.ARG_ACCT_MONTH);
        if (CharSequenceUtil.isNotEmpty(acctMonth)) {
            if (CharSequenceUtil.equalsIgnoreCase(acctMonth, DatePattern.SIMPLE_MONTH_PATTERN)) {
                acctMonth = DateUtil.format(DateUtil.lastMonth(), DatePattern.SIMPLE_MONTH_PATTERN);
            }
            variableMap.put(DatePattern.SIMPLE_MONTH_PATTERN, acctMonth);
            variableMap.put("mm", String.valueOf(Integer.parseInt(acctMonth.substring(4, 6))));
        } else {
            DateTime dateTimeNow = DateTime.now();
            //yyyyMM
            variableMap.putIfAbsent(DatePattern.SIMPLE_MONTH_PATTERN, dateTimeNow.toString(DatePattern.SIMPLE_MONTH_PATTERN));
            variableMap.putIfAbsent("mm", String.valueOf(Integer.parseInt(dateTimeNow.toString("mm"))));
        }

        //入参
        String acctMonthDay = this.ruleSetting.get(null, NormalConst.ARG_ACCT_MONTH_DAY);
        if (CharSequenceUtil.isNotEmpty(acctMonthDay)) {
            if (CharSequenceUtil.equalsIgnoreCase(acctMonthDay, DatePattern.PURE_DATE_PATTERN)) {
                acctMonthDay = DateUtil.format(DateUtil.yesterday(), DatePattern.PURE_DATE_PATTERN);
            }
            variableMap.put(DatePattern.PURE_DATE_PATTERN, acctMonthDay);
            variableMap.put(DatePattern.SIMPLE_MONTH_PATTERN, acctMonthDay.substring(0, 6));
            variableMap.put("mm", String.valueOf(Integer.parseInt(acctMonthDay.substring(4, 6))));
            variableMap.put("dd", String.valueOf(Integer.parseInt(acctMonthDay.substring(6, 8))));
        } else {
            DateTime dateTimeNow = DateTime.now();
            //yyyyMMdd
            variableMap.putIfAbsent(DatePattern.PURE_DATE_PATTERN, dateTimeNow.toString(DatePattern.PURE_DATE_PATTERN));
            variableMap.putIfAbsent(DatePattern.SIMPLE_MONTH_PATTERN, dateTimeNow.toString(DatePattern.SIMPLE_MONTH_PATTERN));
            variableMap.putIfAbsent("mm", String.valueOf(Integer.parseInt(dateTimeNow.toString("mm"))));
            variableMap.putIfAbsent("dd", String.valueOf(Integer.parseInt(dateTimeNow.toString("dd"))));
        }

        return variableMap;
    }
}
