<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="SettleRuleList">
		<xs:annotation>
			<xs:documentation>Comment describing your root element</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="BillMonth">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="6"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="ECList">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ECInfo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="CustomerNumber">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="ProductSubsList">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="ProductInfo" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="ProductID">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductSpecNumber">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="OrderMode">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="FeeList">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="FeeInfo" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="ProdChargeCode">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettleOutPorv">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="3"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettleFlag">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="1"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettleInList" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="SettleInPorv">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:length value="3"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="SettleRate" type="xs:float"/>
																									<xs:element name="Description" minOccurs="0">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="ContractMain" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
