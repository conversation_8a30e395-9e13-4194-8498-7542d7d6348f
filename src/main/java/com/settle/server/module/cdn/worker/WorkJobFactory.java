package com.settle.server.module.cdn.worker;

import cn.hutool.core.io.FileUtil;
import cn.hutool.setting.Setting;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.constant.RuleConst;
import com.settle.server.module.cdn.enums.ActionType;
import com.settle.server.module.cdn.job.Job;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.hutool.core.io.FileUtil.getAbsolutePath;

/**
 * <AUTHOR>
 */
@Service
public class WorkJobFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(WorkJobFactory.class.getSimpleName());


    public List<Job> buildJobList(Setting ruleSetting, String ruleGroup, Map<String, Class<?>> classMap, DataSource stludrDataSource) {
        List<Job> jobs = new ArrayList<>();
        String action = ruleSetting.getByGroup(RuleConst.G_K_ACTION_TYPE, ruleGroup);
        ActionType actionType = Enum.valueOf(ActionType.class, action.toUpperCase());
        switch (actionType) {
            case CSV2DB:
                jobs = buildCsv2DbWorkJobs(ruleSetting, ruleGroup, classMap,stludrDataSource);
                break;
            case DB2CSV:
                jobs = buildDb2CsvWorkJobs(ruleSetting, ruleGroup,stludrDataSource);
                break;
            default:
                break;
        }

        return jobs;
    }

    /**
     * CSV文件 -> 数据库
     *
     * @param ruleSetting      规则配置
     * @param ruleGroup        规则组
     * @param classMap         类映射
     * @param stludrDataSource
     * @return 作业列表
     */
    private List<Job> buildCsv2DbWorkJobs(Setting ruleSetting, String ruleGroup, Map<String, Class<?>> classMap, DataSource stludrDataSource) {
        List<Job> jobList = new ArrayList<>();
        //文件名匹配正则（缺省为：.*）
        String fileNameReg = ruleSetting.getStr(RuleConst.G_K_FILE_NAME_REG, ruleGroup, NormalConst.DEFAULT_REG_MATCH_ANY);
        Pattern pattern = Pattern.compile(fileNameReg);
        //文件入口路径（缺省为：../incoming/）
        String fileInPath = ruleSetting.getStr(RuleConst.G_K_FILE_IN_PATH, ruleGroup, NormalConst.DEFAULT_FILE_IN_PATH);
        String absolutePath = getAbsolutePath(fileInPath);
        LOGGER.info("absolutePath:{}",absolutePath);
        List<File> dataFileList = FileUtil.loopFiles(fileInPath, pathname -> {
            Matcher matcher = pattern.matcher(pathname.getName());
            return matcher.matches();
        });

        for (File dataFile : dataFileList) {
            WorkerJob workerJob = new CsvToDbWorkerJob<>(dataFile, ruleSetting.getSetting(ruleGroup), classMap,stludrDataSource);
            workerJob.beforeBuildWorkerJob();
            Job job = workerJob.buildWorkerJob();
            workerJob.afterBuildWorkerJob();
            jobList.add(job);
        }

        return jobList;
    }

    /**
     * 数据库 -> CSV文件
     *
     * @param ruleSetting      规则配置
     * @param ruleGroup        规则组
     * @param stludrDataSource
     * @return 作业列表
     */
    private List<Job> buildDb2CsvWorkJobs(Setting ruleSetting, String ruleGroup, DataSource stludrDataSource) {
        List<Job> jobList = new ArrayList<>();
        WorkerJob workerJob = new DbToCsvWorkerJob<>(ruleGroup, ruleSetting,stludrDataSource);
        workerJob.beforeBuildWorkerJob();
        Job job = workerJob.buildWorkerJob();
        workerJob.afterBuildWorkerJob();
        jobList.add(job);

        return jobList;
    }

}
