package com.settle.server.module.clearup.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.RunLog;
import com.settle.server.utils.ExceptionCast;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/26
 * @since 1.0.0
 */
@Service
@Slf4j
public abstract class AbstractClearTableService implements ClearTableService {

    @Autowired
    private Map<String, DataSource> dataSourceMap;

    private static final String TRUNCATE_TABLE = "alter table {table} truncate partition {partition}";

    private static final String INSERT_RUNLOG = "insert into run_log (LOG_MMDD, LOG_TS, LOG_SID, LOG_AUDSID, LOG_LEVEL,LOG_SRC,LOG_LINE,LOG_TAG,LOG_TEXT) values (?,?,?,?,?,?,?,?,?);";

    @Override
    public void doProcess(ClearLogDTO dto) {
        RunLog runLog = initRunLog(dto.getTableName());
        Map<String, String> params = Maps.newHashMap();
        DbNameEnum nameEnum = DbNameEnum.getDbName(dto.getDbName().toUpperCase());
        try {
            if (CollectionUtil.isEmpty(dto.getPartition())) {
                beforeProcess(runLog, nameEnum);
            }
            if (!runLog.isExec()) {
                return;
            }
            params.put("table", dto.getTableName());
            DataSource dataSource = dataSourceMap.get(nameEnum.getDataSourceBeanName());
            List<String> partitions = this.getPartition(dto);
            for (String partition : partitions) {
                params.put("partition", partition);
                String sql = StrUtil.format(TRUNCATE_TABLE, params);
                traceLog(runLog, sql, nameEnum);
                execUpdateSql(sql, dataSource);
                infoLog(runLog, sql, nameEnum);
            }
        } catch (Exception e) {
            log.error("清理表:{} 失败", dto.getTableName(), e);
            runLog.setLogLevel("ERR");
            String errMsg = ExceptionUtil.stacktraceToString(e, 2000);
            runLog.setLogText(errMsg);
        } finally {
            if ("ERR".equals(runLog.getLogLevel())) {
                saveRunLog(runLog, nameEnum);
            }
        }
    }

    protected abstract void beforeProcess(RunLog runLog, DbNameEnum nameEnum);

    private void infoLog(RunLog runLog, String sql, DbNameEnum nameEnum) {
        runLog.setLogLevel("INF");
        runLog.setLogText("sql [" + sql + "] done");
        saveRunLog(runLog, nameEnum);
    }

    private void traceLog(RunLog runLog, String sql, DbNameEnum nameEnum) {
        runLog.setLogLevel("TRC");
        runLog.setLogText("try [" + sql + "]");
        saveRunLog(runLog, nameEnum);
    }

    protected void saveRunLog(RunLog runLog, DbNameEnum nameEnum) {
        DataSource dataSource = dataSourceMap.get(nameEnum.getDataSourceBeanName());
        Connection connection = getConnection(dataSource);
        PreparedStatement statement = null;
        try {
            statement = connection.prepareStatement(INSERT_RUNLOG);
            statement.setInt(1, runLog.getLogMmdd());
            statement.setTimestamp(2, new java.sql.Timestamp(runLog.getLogTs().getTime()));
            statement.setInt(3, runLog.getLogSid());
            statement.setInt(4, runLog.getLogAusid());
            statement.setString(5, runLog.getLogLevel());
            statement.setString(6, runLog.getLogSrc());
            statement.setInt(7, runLog.getLogLine());
            statement.setString(8, runLog.getLogTag());
            statement.setString(9, runLog.getLogText());
            statement.execute();
        } catch (SQLException e) {
            log.error("保存日志失败", e);
            ExceptionCast.cast("保存日志失败", "");
        } finally {
            close(statement, connection);
        }
    }

    private RunLog initRunLog(String tag) {
        int mmdd = Integer.parseInt(DateUtil.format(new Date(), "MMdd"));
        return RunLog.builder()
                .logMmdd(mmdd)
                .logTs(new Date())
                .logSid(1)
                .logAusid(1)
                .logSrc("tools")
                .logLine(1)
                .logTag(tag)
                .isExec(true)
                .build();
    }

    private List<String> getPartition(ClearLogDTO dto) {
        if (CollectionUtil.isNotEmpty(dto.getPartition())) {
            return dto.getPartition();
        }
        return this.partitionsBySelf(dto);
    }

    protected abstract List<String> partitionsBySelf(ClearLogDTO dto);


    /**
     * 执行SQL更新操作，包括 INSERT, UPDATE, DELETE, LOAD DATA 等
     *
     * @param sql        SQL语句
     * @param dataSource 数据源
     * @return 影响的行数
     */
    public int execUpdateSql(String sql, DataSource dataSource) {
        Connection connection = getConnection(dataSource);
        PreparedStatement statement = null;
        int result = 0;
        try {
            statement = connection.prepareStatement(sql);
            result = statement.executeUpdate();
        } catch (SQLException e) {
            log.error("执行SQL更新操作异常：{}", sql, e);
            ExceptionCast.cast("执行SQL更新操作异常: [%s]", sql);
        } finally {
            close(connection, statement);
        }
        return result;
    }

    private Connection getConnection(DataSource dataSource) {
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            //手动控制事务
            connection.setAutoCommit(true);
        } catch (SQLException e) {
            log.error("获取连接失败", e);
            ExceptionCast.cast("获取连接信息失败", "");
        }
        return connection;
    }

    private void close(AutoCloseable... cloneables) {
        for (AutoCloseable closeable : cloneables) {
            try {
                if (closeable != null) {
                    closeable.close();
                }
            } catch (Exception e) {
                log.error("关闭资源异常", e);
            }
        }
    }
}