<?xml version="1.0" encoding="GBK"?>
<!--
- author:   <PERSON>t
- date:     2020/05/22
- verson:   v1.0
- descprition:
    this file is used to transform the incoming inventory XML file to txt file.
-->
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:fn="http://www.w3.org/2005/xpath-functions" exclude-result-prefixes="fn xs xsi xsl">
	<xsl:output method="text" encoding="UTF-8" media-type="text/plain"/>
	<xsl:variable name="sep">
		<xsl:text>,</xsl:text>
	</xsl:variable>
	<xsl:variable name="zero">
		<xsl:text>0</xsl:text>
	</xsl:variable>
	<xsl:variable name="crlf">
		<xsl:text>&#xd;&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="carr">
		<xsl:text>&#xa;</xsl:text>
	</xsl:variable>
      
    <xsl:template match="/SettleRuleList">
		<xsl:variable name="v_BillMonth" select="BillMonth"/>
		<xsl:for-each select="ECList">
			<xsl:for-each select="ECInfo">
				<xsl:variable name="v_CustomerNumber" select="CustomerNumber"/>
				<xsl:for-each select="ProductSubsList">
					<xsl:for-each select="ProductInfo">
						<xsl:variable name="v_ProductID" select="ProductID"/>
						<xsl:variable name="v_ProductSpecNumber" select="ProductSpecNumber"/>
						<xsl:variable name="v_OrderMode" select="OrderMode"/>
						<xsl:variable name="v_ContractMain" select="ContractMain"/>
						<xsl:for-each select="FeeList">
							<xsl:for-each select="FeeInfo">
								<xsl:variable name="v_ProdChargeCode" select="ProdChargeCode"/>
								<xsl:variable name="v_SettleOutPorv" select="SettleOutPorv"/>
								<xsl:variable name="v_SettleFlag" select="SettleFlag"/>
								<xsl:for-each select="SettleInList">
									<xsl:variable name="v_SettleInPorv" select="SettleInPorv"/>
									<xsl:variable name="v_SettleRate" select="SettleRate"/>
									<xsl:variable name="v_Description" select="Description"/>
									
									<xsl:value-of select="$v_BillMonth"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_CustomerNumber"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_ProductID"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_ProductSpecNumber"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_OrderMode"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_ContractMain"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_ProdChargeCode"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_SettleOutPorv"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_SettleFlag"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_SettleInPorv"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_SettleRate"/>
									<xsl:value-of select="$sep"/>
									<xsl:value-of select="$v_Description"/>
									<xsl:value-of select="$crlf"/>
								</xsl:for-each>
							</xsl:for-each>
						</xsl:for-each>
					</xsl:for-each>
				</xsl:for-each>
			</xsl:for-each>
		</xsl:for-each>
    </xsl:template>
</xsl:stylesheet>
