<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.SyncInterfaceMcP2CDao">



    <resultMap type="com.settle.server.entity.SyncInterfaceMcP2C" id="SyncInterfaceMcP2CMap">
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="settlementPartyIn" column="SETTLEMENT_PARTY_IN" jdbcType="VARCHAR"/>
        <result property="settlementPartyOut" column="SETTLEMENT_PARTY_OUT" jdbcType="VARCHAR"/>
        <result property="settlementRate" column="SETTLEMENT_RATE" jdbcType="VARCHAR"/>
        <result property="settlementType" column="SETTLEMENT_TYPE" jdbcType="VARCHAR"/>
        <result property="settlementAmount" column="SETTLEMENT_AMOUNT" jdbcType="VARCHAR"/>
        <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="proSettleDisvalue" column="PRO_SETTLE_DISVALUE" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="queryAllByCondition" resultMap="SyncInterfaceMcP2CMap">
        select ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,FILE_NAME,STATUS,PRO_SETTLE_DISVALUE
            from SYNC_INTERFACE_MC_P2C_${acctMonth}
        <where>
            <if test="id != null and id != ''">
                and ID = #{id}
            </if>
            <if test="provCode != null and provCode != ''">
                and PROV_CODE = #{provCode}
            </if>
        </where>
    </select>

    <insert id="insert"  useGeneratedKeys="false" parameterType="com.settle.server.entity.SyncInterfaceMcP2C">
        insert into SYNC_INTERFACE_MC_P2C_${acctMonth}(ID, SETTLEMENT_PARTY_IN, SETTLEMENT_PARTY_OUT, SETTLEMENT_RATE,
                                                   SETTLEMENT_TYPE, SETTLEMENT_AMOUNT, FILE_NAME, STATUS,
                                                   PRO_SETTLE_DISVALUE)
        values (
                #{id}, #{settlementPartyIn}, #{settlementPartyOut}, #{settlementRate}, #{settlementType},
                #{settlementAmount}, #{fileName}, #{status}, #{proSettleDisvalue}
                )
    </insert>

    <insert id="insertBatch"  useGeneratedKeys="false" parameterType="com.settle.server.entity.SyncInterfaceMcP2C">
        insert into SYNC_INTERFACE_MC_P2C_${acctMonth}
        (
        ID, SETTLEMENT_PARTY_IN, SETTLEMENT_PARTY_OUT, SETTLEMENT_RATE,
        SETTLEMENT_TYPE, SETTLEMENT_AMOUNT, FILE_NAME, STATUS,
        PRD_SETTLE_DISVALUE)
        values
        <foreach collection="syncInterfaceMcP2CLst" item="item" index="index" separator=",">
            (#{item.id}, #{item.settlementPartyIn}, #{item.settlementPartyOut}, #{item.settlementRate},
            #{item.settlementType}, #{item.settlementAmount}, #{item.fileName}, #{item.status}, #{item.proSettleDisvalue})
        </foreach>
    </insert>

    <update id="updateStausByAcctMonth">
        update stludr.SYNC_INTERFACE_MC_P2C_${acctMonth}
        set status='23'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '23')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStatusByFileNameAndId">
        update stludr.SYNC_INTERFACE_MC_P2C_${acctMonth}
        set status='24'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '24')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStatusByFileName">
        update stludr.SYNC_INTERFACE_MC_P2C_${acctMonth}
        set status='25'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '25')
          and status is null
          and file_name = #{fileName}
    </update>
    <update id="updateStatusByMcStatus">
        update stludr.SYNC_INTERFACE_MC_P2C_${acctMonth}
        set status='99'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_MC_${acctMonth} where status = '99')
          and status is null
          and file_name = #{fileName}
    </update>

    <update id="updateStausByStatus">
        update stludr.SYNC_INTERFACE_MC_P2C_${acctMonth} set status='0'
        where status is null and file_name = #{fileName}
    </update>

    <delete id="deleteByFileNameMonth" parameterType="java.lang.String">
        delete from stludr.SYNC_INTERFACE_MC_P2C_${acctMonth}
        <where>
            <if test="fileName != null and fileName != ''">
                AND file_name = #{fileName, jdbcType = VARCHAR}
            </if>
        </where>
    </delete>
</mapper>