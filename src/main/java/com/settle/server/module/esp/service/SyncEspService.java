package com.settle.server.module.esp.service;

import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.esp.SyncInterfaceEspMapper;
import com.settle.server.dao.stludr.esp.SyncInterfaceEspP2cMapper;
import com.settle.server.dao.stludr.esp.SyncInterfaceEspP2pMapper;
import com.settle.server.dao.stludr.esp.SyncInterfaceEspPartMapper;
import com.settle.server.entity.esp.SyncInterfaceEsp;
import com.settle.server.entity.esp.SyncInterfaceEspP2c;
import com.settle.server.entity.esp.SyncInterfaceEspP2p;
import com.settle.server.entity.esp.SyncInterfaceEspPart;
import com.settle.server.service.impl.MobileCloudServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/17
 * @since 1.0.0
 */
@Service
@Slf4j
public class SyncEspService {

    @Autowired
    private SyncInterfaceEspMapper espMapper;
    @Autowired
    private SyncInterfaceEspP2cMapper p2cMapper;
    @Autowired
    private SyncInterfaceEspP2pMapper p2pMapper;
    @Autowired
    private SyncInterfaceEspPartMapper partMapper;

    public void truncate(String acctMonth) {
        log.info("truncate esp data for acctMonth: {}", acctMonth);
        espMapper.truncate(acctMonth);
        p2cMapper.truncate(acctMonth);
        p2pMapper.truncate(acctMonth);
        partMapper.truncate(acctMonth);
        log.info("truncate esp data for acctMonth: {} done", acctMonth);
    }

    //ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,REMARK,FILE_NAME
    public void optP2p(String acctMonth, String fileName, List<SyncInterfaceEspP2p> espP2pList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 8) {
            log.error("EspP2P line format error: {}", line);
            return;
        }
        SyncInterfaceEspP2p espP2p = new SyncInterfaceEspP2p();
        espP2p.setId(split[0]);
        espP2p.setSettlementPartyIn(split[2]);
        espP2p.setSettlementPartyOut(split[3]);
        espP2p.setSettlementAmount(Long.valueOf(StringUtils.isNotBlank(split[4])?split[4]:"0"));
        espP2p.setSettlementType(split[5]);
        espP2p.setSettlementRate(split[6]);
        espP2p.setRemark(split[7]);
        espP2p.setFileName(fileName);
        espP2p.setStatus("0");
        espP2pList.add(espP2p);
    }

    //ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,SETTLE_CLASS,FILE_NAME
    public void optP2c(String acctMonth, String filename, List<SyncInterfaceEspP2c> espP2cList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 8) {
            log.error("EspP2C line format error: {}", line);
            return;
        }
        SyncInterfaceEspP2c espP2c = new SyncInterfaceEspP2c();
        espP2c.setId(split[0]);
        espP2c.setSettlementPartyIn(split[2]);
        espP2c.setSettlementPartyOut(split[3]);
        espP2c.setSettlementRate(split[4]);
        espP2c.setSettlementType(split[5]);
        espP2c.setSettlementAmount(StringUtils.isNotBlank(split[6])?Long.valueOf(split[6]):null);
        espP2c.setSettleClass(split[7]);
        espP2c.setFileName(filename);
        espP2c.setStatus("0");
        espP2cList.add(espP2c);
    }

    //ID,PARTNER_CODE,PARTNER_NAME,PAR_SETTLE_RATE,PAR_RES_SETTL_RATE,SETTLEMENT_TYPE,PAR_SETTLE_PAY_TYPE,PAR_SETTL_AMOUNT,FILE_NAME
    public void optP2Partner(String acctMonth, String fileName, List<SyncInterfaceEspPart> espPartList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 9) {
            log.error("EspPart line format error: {}", line);
            return;
        }
        SyncInterfaceEspPart espPart = new SyncInterfaceEspPart();
        espPart.setId(split[0]);
        espPart.setPartnerCode(split[2]);
        espPart.setPartnerName(split[3]);
        espPart.setParSettleRate(split[4]);
        espPart.setSettlementType(split[5]);
        espPart.setParSettlAmount(StringUtils.isNotBlank(split[6])?Long.parseLong(split[6]):null);
        espPart.setParResSettlRate(split[7]);
        espPart.setParSettlePayType(split[8]);
        espPart.setFileName(fileName);
        espPart.setStatus("0");
        espPartList.add(espPart);
    }

    /**
     * 						<xsl:value-of select="$pos_ECInfo"/>
     * 						<xsl:value-of select="$hyphen"/>
     * 						<xsl:value-of select="$pos_FeeInfo"/>
     *
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ProvCode"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_PayTag"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_GroupCustomerNumber"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_GroupCustomerName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_EBOSSCustomerNumber"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_EBOSSCustomerName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_AddressProvCode"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_InnerECFlag"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_EcDepartmentName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ECCreatorName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ECCreatorTel"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_AccountID"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_AccountName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_SubsID"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ProductClassName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ProductDetailName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ProductID"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ProductName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_MainContract"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_RunDepartmentName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ICTFLAG"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_RateplanID"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_RateplanName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_FeeVal"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_TaxRate"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_Tax"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_FeeNoTax"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_FeeFlag"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_OriginalBillMonth"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_DiscountAmount"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_StandardFee"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_SettleFee"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_BillingTerm"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_PayTerm"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_SettleItem"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_BusiMode"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_Chargecode"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_ChargeCodeName"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_CityCode"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_IniPrice"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_SettlePrice"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_DbProductCode"/>
     * 						<xsl:value-of select="$sep"/>
     * 						<xsl:value-of select="$v_DbProdChargeCode"/>
     * 						<xsl:value-of select="$crlf"/>
     */
    public void optEsp(String acctMonth, String fileName, List<SyncInterfaceEsp> espList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 44) {
            log.error("Esp line format error: {}", line);
            return;
        }
        SyncInterfaceEsp esp = new SyncInterfaceEsp();
        esp.setId(split[0]);
        esp.setProvCode(split[1]);
        esp.setPayTag(split[2]);
        esp.setGroupCustomerNumber(split[3]);
        esp.setGroupCustomerName(split[4]);
        esp.setEbossCustomerNumber(split[5]);
        esp.setEbossCustomerName(split[6]);
        esp.setAddressProvCode(split[7]);
        esp.setInnerEcFlag(split[8]);
        esp.setEcDepartmentName(split[9]);
        esp.setEcCreatorName(split[10]);
        esp.setEcCreatorTel(split[11]);
        esp.setAccountId(split[12]);
        esp.setAccountName(split[13]);
        esp.setSubsId(split[14]);
        esp.setProductClassName(split[15]);
        esp.setProductDetailName(split[16]);
        esp.setProductId(split[17]);
        esp.setProductName(split[18]);
        esp.setMainContract(split[19]);
        esp.setRunDepartmentName(split[20]);
        esp.setIctFlag(split[21]);
        esp.setRateplanId(split[22]);
        esp.setRateplanName(split[23]);
        esp.setFeeVal(StringUtils.isNotBlank(split[24])?Long.parseLong(split[24]):null);
        esp.setTaxRate(split[25]);
        esp.setTax(StringUtils.isNotBlank(split[26])?Long.parseLong(split[26]):null);
        esp.setFeeNoTax(StringUtils.isNotBlank(split[27])?Long.parseLong(split[27]):null);
        esp.setFeeFlag(StringUtils.isNotBlank(split[28])? Integer.parseInt(split[28]) :null);
        esp.setOriginalBillMonth(split[29]);
        esp.setDiscountAmount(StringUtils.isNotBlank(split[30])?Long.parseLong(split[30]):null);
        esp.setStandardFee(StringUtils.isNotBlank(split[31])?Long.parseLong(split[31]):null);
        esp.setSettleFee(StringUtils.isNotBlank(split[32])?Long.parseLong(split[32]):null);
        esp.setBillingTerm(split[33]);
        esp.setPayTerm(split[34]);
        esp.setSettleItem(split[35]);
        esp.setBusiMode(split[36]);
        esp.setChargeCode(split[37]);
        esp.setChargeCodeName(split[38]);
        esp.setCityCode(split[39]);
        esp.setIniPrice(split[40]);
        esp.setSettlePrice(split[41]);
        esp.setDbproductcode(split[42]);
        esp.setDbprodchargecode(split[43]);
        esp.setFileName(fileName);
        esp.setStatus("0");
        espList.add(esp);

    }

    public void saveEsp(String acctMonth, List<SyncInterfaceEsp> espList) {
        if (CollectionUtils.isEmpty(espList)) {
            return;
        }
        Lists.partition(espList, 1000).forEach(espList1 -> {
            espMapper.saveEsp(acctMonth, espList1);
        });
    }

    public void saveP2p(String acctMonth, List<SyncInterfaceEspP2p> espP2pList) {
        if (CollectionUtils.isEmpty(espP2pList)) {
            return;
        }
        Lists.partition(espP2pList, 1000).forEach(espP2pList1 -> {
            p2pMapper.saveP2p(acctMonth, espP2pList1);
        });
    }

    public void saveP2c(String acctMonth, List<SyncInterfaceEspP2c> espP2cList) {
        if (CollectionUtils.isEmpty(espP2cList)) {
            return;
        }
        Lists.partition(espP2cList, 1000).forEach(espP2cList1 -> {
            p2cMapper.saveP2c(acctMonth, espP2cList1);
        });
    }

    public void saveP2Partner(String acctMonth, List<SyncInterfaceEspPart> espPartList) {
        if (CollectionUtils.isEmpty(espPartList)) {
            return;
        }
        Lists.partition(espPartList, 1000).forEach(espPartList1 -> {
            partMapper.savePart(acctMonth, espPartList1);
        });
    }

    //    echo "update stludr.SYNC_INTERFACE_ESP_${acct_month} set status='F023'  where (prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) or address_prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1'))) and status is null; " >>  ${sql_file}
    //    echo "update stludr.SYNC_INTERFACE_ESP_P2P_${acct_month} set status='F023' where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acct_month} where status='F023') and status is null; " >>  ${sql_file}
    //    echo "update stludr.SYNC_INTERFACE_ESP_P2C_${acct_month} set status='F023' where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acct_month} where status='F023') and status is null; " >> ${sql_file}
    //    echo "update stludr.SYNC_INTERFACE_ESP_PART_${acct_month} set status='F023' where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acct_month} where status='F023') and status is null; " >> ${sql_file}
    public void updateStatus23(String acctMonth, String fileName) {
        log.info("update status 23 for acctMonth: {}, fileName: {}", acctMonth, fileName);
        espMapper.updateStatus23(acctMonth, fileName);
        p2cMapper.updateStatus23(acctMonth, fileName);
        p2pMapper.updateStatus23(acctMonth, fileName);
        partMapper.updateStatus23(acctMonth, fileName);
        log.info("update status 23 for acctMonth: {}, fileName: {} done", acctMonth, fileName);
    }

    public void updateStatus0(String acctMonth, String fileName) {

    }

    public List<SyncInterfaceEsp> queryErrData(String acctMonth, String fileName) {

        return espMapper.queryErrData(acctMonth, fileName);
    }
}