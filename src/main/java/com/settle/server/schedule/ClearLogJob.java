package com.settle.server.schedule;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.settle.server.module.clearup.ClearTableHandler;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.PartitionTypeEnum;
import com.settle.server.utils.ExceptionCast;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/22
 * @since 1.0.0
 */
@Component
@Slf4j
public class ClearLogJob {
    @Autowired
    private ClearTableHandler clearTableHandler;

    @XxlJob("truncateHisPartition")
    //清理历史数据
    public ReturnT<String> truncateHisPartition() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("清理业务日志分区数据处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            ClearLogDTO clearLogDTO = this.getCleatLogDTO(entries);
            this.checkParam(clearLogDTO);
            clearTableHandler.clearTable(clearLogDTO);
            XxlJobHelper.log("清理业务日志分区数据处理结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("清理业务日志分区数据处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    private void checkParam(ClearLogDTO clearLogDTO) {
        String dbName = clearLogDTO.getDbName();
        if (StringUtils.isNotBlank(dbName)) {
            DbNameEnum dbNameEnum = DbNameEnum.getDbName(dbName.toUpperCase());
            if (dbNameEnum == null) {
                ExceptionCast.cast("dbName参数不合法,只支持以下参数: %s", Arrays.toString(DbNameEnum.values()));
            }
        }
        String partitionType = clearLogDTO.getPartitionType();
        if (StringUtils.isNotBlank(partitionType)) {
            PartitionTypeEnum partitionTypeEnum = PartitionTypeEnum.getByType(partitionType.toLowerCase());
            if (partitionTypeEnum == null) {
                ExceptionCast.cast("partitionType参数不合法,只支持以下参数: %s", Arrays.toString(PartitionTypeEnum.values()));
            }
        }
    }

    private ClearLogDTO getCleatLogDTO(JSONObject entries) {
        String dbName = entries.getStr("dbName");
        String tableName = entries.getStr("tableName");
        String partition = entries.getStr("partition");
        List<String> partitions = Lists.newArrayList();
        if (StringUtils.isNotBlank(partition)) {
            partitions = Arrays.stream(partition.split(",")).collect(Collectors.toList());
        }
        String range = entries.getStr("range");
        if (StringUtils.isNotBlank(range)) {
            partitions.clear();
            String[] split = range.split("-");
            String start = split[0].substring(1);
            String end = split[1].substring(1);
            DateTime startDate = DateUtil.parse(start, "MMdd");
            DateTime endDate = DateUtil.parse(end, "MMdd");
            List<String> finalPartitions = partitions;
            DateUtil.rangeConsume(startDate, endDate, DateField.DAY_OF_YEAR, date -> {
                String mmdd = DateUtil.format(date, "MMdd");
                finalPartitions.add("p" + mmdd);
            });
        }
        return new ClearLogDTO(dbName, tableName, partitions);
    }
}