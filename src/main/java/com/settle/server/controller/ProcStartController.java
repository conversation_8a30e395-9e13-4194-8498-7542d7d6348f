package com.settle.server.controller;

import com.settle.server.proc.Model;
import com.settle.server.proc.StlProcedures;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
public class ProcStartController {
    @PostMapping("/testProc")
    public  void procStart(HttpServletRequest req, @RequestParam Map<String,Object> request){
        log.info("*************************************************************************");
        log.info("*该程序执行的存储过程只包括以下相关的存储过程!");
        log.info("*创建表存取的存储 : CREATE_PARTITION(?,?)");
        log.info("*数据库中 RVL_TABLE_ALL 表中有效的相关存储过程!");
        log.info("*其他的存储请不要使用该程序执行!... ...");
        log.info("*************************************************************************");
        String arg= (String) request.get("request");
        List<String> args= Arrays.asList(arg.split(","));
        Calendar c = Calendar.getInstance(); // 获取当前时间
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 设置日期格式
        SimpleDateFormat month_df = new SimpleDateFormat("yyyyMM");// 设置日期格式
        StlProcedures sp = new StlProcedures();
        Map<String, String> map = new HashMap<String, String>();
        if (args.size() == 1) {
            if (args.get(0).equals("one")) {
                log.info("开始执行一批存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = sp.getProc("1");
                c.add(Calendar.MONTH, -1); // 获取上月当前时间
                map.put("settlemonth", month_df.format(c.getTime()));
                map.put("flag_version", "N");
                sp.startProc(list, map);
            } else if (args.get(0).equals("two")) {
                log.info("开始执行二批存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = sp.getProc("");
                c.add(Calendar.MONTH, -1); // 获取上月当前时间
                map.put("settlemonth", month_df.format(c.getTime()));
                map.put("flag_version", "N");
                sp.startProc(list, map);
            } else if (args.get(0).equals("CREATE_PARTITION")) {
                log.info("开始执行创建表空间存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = new ArrayList<Model>();
                c.add(Calendar.MONTH, -1); // 获取上月当前时间
                map.put("settlemonth", month_df.format(c.getTime()));
                map.put("flag_version", "N");
                Model m = new Model();
                m.setProcName("CREATE_PARTITION(#RPT_SETTLEMONTH#,?)");
                m.setProcRemrk("创建中间表的表空间... ...");
                list.add(m);
                sp.startProc(list, map);
            } else {
                log.error("程序不支持" + args.get(0) + "存储过程... ...");
            }
        } else if (args.size() == 2) {
            if (args.get(0).equals("one")) {
                log.info("开始执行一批存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = sp.getProc("1");
                map.put("settlemonth", args.get(1));
                map.put("flag_version", "N");
                sp.startProc(list, map);
            } else if (args.get(0).equals("two")) {
                log.info("开始执行二批存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = sp.getProc("");
                map.put("settlemonth", args.get(1));
                map.put("flag_version", "N");
                sp.startProc(list, map);
            } else if (args.get(0).equals("CREATE_PARTITION")) {
                log.info("开始执行创建表空间存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = new ArrayList<Model>();
                map.put("settlemonth", args.get(1));
                map.put("flag_version", "N");
                Model m = new Model();
                m.setProcName("CREATE_PARTITION(#RPT_SETTLEMONTH#,?)");
                m.setProcRemrk("创建中间表的表空间... ...");
                list.add(m);
                sp.startProc(list, map);
            } else {
                log.error("程序不支持" + args.get(0) + "存储过程... ...");
            }
        } else if (args.size() == 3) {
            if (args.get(0).equals("one")) {
                log.info("开始执行一批存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = sp.getProc("1");
                map.put("settlemonth", args.get(1));
                map.put("flag_version", args.get(2));
                sp.startProc(list, map);
            } else if (args.get(0).equals("two")) {
                log.info("开始执行二批存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = sp.getProc("");
                map.put("settlemonth", args.get(1));
                map.put("flag_version", args.get(2));
                sp.startProc(list, map);
            } else if (args.get(0).equals("CREATE_PARTITION")) {
                log.info("开始执行创建表空间存储过程, 启动时间："	+ df.format(c.getTime()) + "... ...");
                List<Model> list = new ArrayList<Model>();
                map.put("settlemonth", args.get(1));
                map.put("flag_version", args.get(2));
                Model m = new Model();
                m.setProcName("CREATE_PARTITION(#RPT_SETTLEMONTH#,?)");
                m.setProcRemrk("创建中间表的表空间... ...");
                list.add(m);
                sp.startProc(list, map);
            } else {
                log.error("程序不支持" + args.get(0) + "存储过程... ...");
            }
        } else {
            log.error("请确认执行任务... ...");
        }

        Calendar c1 = Calendar.getInstance(); // 获取当前时间
        log.info("程序执行完成, 结束时间：" + df.format(c1.getTime()) + "... ...");
    }
}
