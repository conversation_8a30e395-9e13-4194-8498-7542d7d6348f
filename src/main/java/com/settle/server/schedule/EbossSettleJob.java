package com.settle.server.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.module.esp.cache.EspBizCache;
import com.settle.server.module.esp.enums.BizCode;
import com.settle.server.service.EbossFileService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/7
 * @since 1.0.0
 */
@Component
@Slf4j
public class EbossSettleJob {

    @Autowired
    private EbossFileService ebossFileService;

    /**
     * EBOSS平台4.16接口,[EBOSS_BILL_BBOSS_M_DETAIL]文件处理
     * EBOSS每月4号0点之前将月计费费用明细文件同步给BBOSS；BBOSS系统接收到文件后，需要将文件解析入库。
     * @return
     */
    @XxlJob("ebossMCSettleHandler")
    public ReturnT<String> ebossMCSettleHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("EBOSS平台4.16接口,[EBOSS_BILL_BBOSS_M_DETAIL]文件处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_16);
            if (lock.isLocked()) {
                XxlJobHelper.log("EBOSS平台4.16接口,[EBOSS_BILL_BBOSS_M_DETAIL]文件处理已有线程在处理,无需重复操作");
                return ReturnT.SUCCESS;
            }
            ebossFileService.processCloud_4_16(acctMonth);
            XxlJobHelper.log("EBOSS平台4.16接口,[EBOSS_BILL_BBOSS_M_DETAIL]文件处理开始");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("EBOSS平台4.16接口,[EBOSS_BILL_BBOSS_M_DETAIL]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    /**
     * EBOSS平台,4.31 接口文件[BC2C]文件处理
     * @return
     */
    @XxlJob("handBC2CFile")
    public ReturnT<String> handBC2CFile() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        XxlJobHelper.log("EBOSS平台4.31接口，[EBOSS_BC2C_BBOSS_M_DETAIL]文件处理开始");
        try {
            String param = XxlJobHelper.getJobParam();
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            XxlJobHelper.log("EBOSS平台,[BC2C]文件处理开始，接受参数：{}", param);
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if(StringUtils.isBlank(acctMonth)){
                LocalDateTime now = LocalDateTime.now(); // 获取当前日期和时间
                LocalDateTime localDateTime = now.minusMonths(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth =  localDateTime.format(formatter);
            }
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            ebossFileService.saveBCTOC(acctMonth);
            XxlJobHelper.log("EBOSS平台4.31接口，[EBOSS_BC2C_BBOSS_M_DETAIL]文件处理结束");
        } catch (Exception e) {
            XxlJobHelper.log("handBC2CFile ---> save error:[{}]", e.getMessage(), e);
            String stackTrace = ExceptionUtils.getStackTrace(e);
            XxlJobHelper.log("EBOSS平台4.31接口，[EBOSS_BC2C_BBOSS_M_DETAIL]文件处理失败,原因:{}", stackTrace);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }





    /**
     * EBOSS平台4.29接口,[EBOSS_PVBILL_BBOSS_M_DETAIL]文件处理
     * EBOSS每月4号0点之前将月计费费用明细文件同步给BBOSS；BBOSS系统接收到文件后，需要将文件解析入库。
     * @return
     */
    @XxlJob("ebossPVBILLSettleHandler")
    public ReturnT<String> ebossPVBILLSettleHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("EBOSS平台4.29接口,[EBOSS_PVBILL_BBOSS_M_DETAIL]文件处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_29);
            if (lock.isLocked()) {
                XxlJobHelper.log("EBOSS平台4.29接口,[EBOSS_PVBILL_BBOSS_M_DETAIL]文件处理已有线程在处理,无需重复操作");
                return ReturnT.SUCCESS;
            }
            ebossFileService.processCloud_4_29(acctMonth);
            XxlJobHelper.log("EBOSS平台4.29接口,[EBOSS_PVBILL_BBOSS_M_DETAIL]文件处理开始");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("EBOSS平台4.29接口,[EBOSS_PVBILL_BBOSS_M_DETAIL]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }



    /**
     * EBOSS平台4.30接口,[EBOSS_PVSETTLE_BBOSS_M_DETAIL]文件处理
     * EBOSS每月4号0点之前将月计费费用明细文件同步给BBOSS；BBOSS系统接收到文件后，需要将文件解析入库。
     * @return
     */
    @XxlJob("ebossPVSETTLESettleHandler")
    public ReturnT<String> ebossPVSETTLESettleHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("EBOSS平台4.30接口,[EBOSS_PVSETTLE_BBOSS_M_DETAIL]文件处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_30);
            if (lock.isLocked()) {
                XxlJobHelper.log("EBOSS平台4.30接口,[EBOSS_PVSETTLE_BBOSS_M_DETAIL]文件处理已有线程在处理,无需重复操作");
                return ReturnT.SUCCESS;
            }
            ebossFileService.processCloud_4_30(acctMonth);
            XxlJobHelper.log("EBOSS平台4.30接口,[EBOSS_PVSETTLE_BBOSS_M_DETAIL]文件处理开始");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("EBOSS平台4.30接口,[EBOSS_PVSETTLE_BBOSS_M_DETAIL]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }


    /**
     * EBOSS平台4.6接口,[ESP_ACC_E_BILL]文件处理
     * 1．每月3号24点前政企ESP给一级BBOSS上传结算应收账单文件；
     * 2．省系统于T+2月生成未经省系统二次批价的结算应收账单文件于3号24点上传给一级BBOSS。
     *
     * 	省系统发送给一级BBOSS的结算账单上传文件名ESP_ACC_E_BILL_3_YYYYMM_ZZZ.NNNN
     * 	政企ESP发送给一级BBOSS的结算账单上传文件名ESP_ACC_E_BILL_2_YYYYMM_ZQYW.NNNN
     * @return
     */
    @XxlJob("ebossESPSettleHandler")
    public ReturnT<String> ebossESPSettleHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("EBOSS平台4.6接口,[ESP_ACC_E_BILL]文件处理开始，接受参数：{}", param);
        try {
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            ReentrantLock lock = EspBizCache.getLock(BizCode.BIZ_4_6);
            if (lock.isLocked()) {
                XxlJobHelper.log("EBOSS平台4.6接口,[ESP_ACC_E_BILL]文件处理已有线程在处理,无需重复操作");
                return ReturnT.SUCCESS;
            }
            ebossFileService.process4_6(acctMonth);
            XxlJobHelper.log("EBOSS平台4.6接口,[ESP_ACC_E_BILL]文件处理开始");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("EBOSS平台4.6接口,[ESP_ACC_E_BILL]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }
}