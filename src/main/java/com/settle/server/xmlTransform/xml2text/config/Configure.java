/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 0.9.6</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.xml2text.config;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import java.io.IOException;
import java.io.Reader;
import java.io.Serializable;
import java.io.Writer;
import org.exolab.castor.xml.MarshalException;
import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;
import org.exolab.castor.xml.ValidationException;
import org.xml.sax.ContentHandler;

/**
 * Comment describing your root element
 * 
 * @version $Revision$ $Date$
 */
public class Configure implements Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _paths
     */
    private Paths _paths;

    /**
     * Field _category
     */
    private Category _category;


      //----------------/
     //- Constructors -/
    //----------------/

    public Configure() {
        super();
    } //-- com.hp.xmlusage.xml2text.config.Configure()


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * Returns the value of field 'category'.
     * 
     * @return Category
     * @return the value of field 'category'.
     */
    public Category getCategory()
    {
        return this._category;
    } //-- com.hp.xmlusage.xml2text.config.Category getCategory() 

    /**
     * Returns the value of field 'paths'.
     * 
     * @return Paths
     * @return the value of field 'paths'.
     */
    public Paths getPaths()
    {
        return this._paths;
    } //-- com.hp.xmlusage.xml2text.config.Paths getPaths() 

    /**
     * Method isValid
     * 
     * 
     * 
     * @return boolean
     */
    public boolean isValid()
    {
        try {
            validate();
        }
        catch (ValidationException vex) {
            return false;
        }
        return true;
    } //-- boolean isValid() 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param out
     */
    public void marshal(Writer out)
        throws MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, out);
    } //-- void marshal(java.io.Writer) 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param handler
     */
    public void marshal(ContentHandler handler)
        throws IOException, MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, handler);
    } //-- void marshal(org.xml.sax.ContentHandler) 

    /**
     * Sets the value of field 'category'.
     * 
     * @param category the value of field 'category'.
     */
    public void setCategory(Category category)
    {
        this._category = category;
    } //-- void setCategory(com.hp.xmlusage.xml2text.config.Category) 

    /**
     * Sets the value of field 'paths'.
     * 
     * @param paths the value of field 'paths'.
     */
    public void setPaths(Paths paths)
    {
        this._paths = paths;
    } //-- void setPaths(com.hp.xmlusage.xml2text.config.Paths) 

    /**
     * Method unmarshal
     * 
     * 
     * 
     * @param reader
     * @return Object
     */
    public static Object unmarshal(Reader reader)
        throws MarshalException, ValidationException
    {
        return (Configure) Unmarshaller.unmarshal(Configure.class, reader);
    } //-- java.lang.Object unmarshal(java.io.Reader) 

    /**
     * Method validate
     * 
     */
    public void validate()
        throws ValidationException
    {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    } //-- void validate() 

}
