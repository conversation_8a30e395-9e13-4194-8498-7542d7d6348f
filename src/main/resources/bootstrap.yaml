spring:
  application:
    name: settle-service-tools
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: ${NACOS_SERVER}
        namespace: ${NACOS_NAMESPACE}
        group: ${spring.profiles.active}
        file-extension: properties
        username: ${NACOS_USERNAME}
        password: ${NACOS_PASSWORD}
        shared-configs:
          - data-id: datasource.properties
            group: ${spring.profiles.active}
        extension-configs:
          - data-id: common.properties
            group: ${spring.profiles.active}
            refresh: true







