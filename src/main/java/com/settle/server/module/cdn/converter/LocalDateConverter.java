package com.settle.server.module.cdn.converter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * {@link LocalDate} 类型转换器
 * 将 {@link DateTimeFormatter#ISO_LOCAL_DATE} 格式的字符串日期转换为 {@link LocalDate} 类型
 *
 * <AUTHOR>
 */
public class LocalDateConverter implements TypeConverter<String, LocalDate> {

    @Override
    public LocalDate convert(String value) {
        return LocalDate.parse(value);
    }
}
