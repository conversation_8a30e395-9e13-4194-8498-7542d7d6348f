package com.settle.server.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.settle.server.module.clearup.ClearTableHandler;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.PartitionTypeEnum;
import com.settle.server.utils.ExceptionCast;
import com.xxl.job.core.biz.model.ReturnT;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/27
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/clearup")
@Api(tags = "清理历史分区")
public class TruncateHisPartitionController {

    @Autowired
    private ClearTableHandler clearTableHandler;

    @PostMapping("truncateHisPartition")
    @ApiOperation("清理历史分区")
    public ReturnT<String> truncateHisPartition(@RequestBody ClearLogDTO dto) {
        try {
            checkParam(dto);
            String range = dto.getRange();
            if (StringUtils.isNotBlank(range)) {
                List<String> partitions = Lists.newArrayList();
                String[] split = range.split("-");
                String start = split[0].substring(1);
                String end = split[1].substring(1);
                DateTime startDate = DateUtil.parse(start, "MMdd");
                DateTime endDate = DateUtil.parse(end, "MMdd");
                DateUtil.rangeConsume(startDate, endDate, DateField.DAY_OF_YEAR, date -> {
                    String mmdd = DateUtil.format(date, "MMdd");
                    partitions.add("p" + mmdd);
                });
                dto.setPartition(partitions);
            }
            clearTableHandler.clearTable(dto);
        } catch (Exception e) {
            log.error("清理历史分区异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    private void checkParam(ClearLogDTO clearLogDTO) {
        String dbName = clearLogDTO.getDbName();
        if (StringUtils.isNotBlank(dbName)) {
            DbNameEnum dbNameEnum = DbNameEnum.getDbName(dbName.toUpperCase());
            if (dbNameEnum == null) {
                ExceptionCast.cast("dbName参数不合法,只支持以下参数: %s", Arrays.toString(DbNameEnum.values()));
            }
        }
        String partitionType = clearLogDTO.getPartitionType();
        if (StringUtils.isNotBlank(partitionType)) {
            PartitionTypeEnum partitionTypeEnum = PartitionTypeEnum.getByType(partitionType.toLowerCase());
            if (partitionTypeEnum == null) {
                ExceptionCast.cast("partitionType参数不合法,只支持以下参数: %s", Arrays.toString(PartitionTypeEnum.values()));
            }
        }
    }
}