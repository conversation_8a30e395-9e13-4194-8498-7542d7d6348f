# SQL语法错误分析和解决方案

## 错误信息分析

```
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''10010001',
            '智慧园区专线',
            '20020001',
          ' at line 5
```

## 问题原因

根据错误信息，问题出现在SQL语句的第5行附近，具体是在处理中文字符 `'智慧园区专线'` 时出现了语法错误。

## 可能的原因

### 1. 字符编码问题
- 数据库连接字符集不正确
- XML文件编码问题
- 中文字符在SQL中的处理问题

### 2. SQL语句格式问题
- 字段数量与VALUES数量不匹配
- 特殊字符转义问题

## 解决方案

### 1. 检查数据库连接配置
确保数据库连接URL包含正确的字符编码：
```properties
spring.datasource.url=***********************************************************************************************************
```

### 2. 验证字段映射
根据实体类定义，确保XML中的字段顺序正确：

**实体类字段顺序（共29个字段）：**
1. id (自增，不需要插入)
2. pospecnumber
3. pospecname
4. sospecnumber
5. sospecname
6. soid
7. customernumber
8. customername
9. startProvNm (start_prov_nm)
10. startProv (start_prov)
11. startCity (start_city)
12. managerNm (manager_nm)
13. managerCon (manager_con)
14. endProvNm (end_prov_nm)
15. endProv (end_prov)
16. endCity (end_city)
17. endDistr (end_distr)
18. cpNm (cp_nm)
19. cpCon (cp_con)
20. bandwidth
21. address
22. startRentRate (start_rent_rate)
23. endRentRate (end_rent_rate)
24. startSetupRate (start_setup_rate)
25. endSetupRate (end_setup_rate)
26. startServRentRate (start_serv_rent_rate)
27. endServRentRate (end_serv_rent_rate)
28. startServOnceRate (start_serv_once_rate)
29. endServOnceRate (end_serv_once_rate)
30. settlemonth

### 3. 数据验证
确保传入的数据：
- 不包含SQL注入字符
- 中文字符正确编码
- 字段数量正确（28个字段，不包括自增ID）

### 4. 调试建议

#### 4.1 添加SQL日志
在application.yml中添加：
```yaml
logging:
  level:
    com.settle.server.dao.stludr.mnp.StlBaseinfoSrv6Mapper: DEBUG
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

#### 4.2 单条记录测试
先测试插入单条记录，确保SQL语句正确：
```java
@Test
public void testSingleInsert() {
    StlBaseinfoSrv6 entity = new StlBaseinfoSrv6();
    entity.setPospecnumber("10010001");
    entity.setPospecname("测试专线"); // 使用简单中文测试
    // ... 设置其他字段
    
    List<StlBaseinfoSrv6> list = Arrays.asList(entity);
    mapper.insertBatch(list);
}
```

#### 4.3 检查数据源
确保数据中没有特殊字符或格式问题：
```java
// 在createStlBaseinfoSrv6方法中添加数据清理
private StlBaseinfoSrv6 createStlBaseinfoSrv6(String acctMonth, String[] lines) {
    try {
        // 数据清理和验证
        for (int i = 0; i < lines.length; i++) {
            if (lines[i] != null) {
                lines[i] = lines[i].trim(); // 去除前后空格
                // 可以添加更多数据清理逻辑
            }
        }
        
        StlBaseinfoSrv6 entity = getStlBaseinfoSrv6(acctMonth, lines);
        ObjectUtils.replaceEmptyStringWithNull(entity);
        return entity;
    } catch (Exception e) {
        log.error("Failed to create StlBaseinfoSrv6 entity for acctMonth: {}, error: {}", 
                acctMonth, e.getMessage(), e);
        return null;
    }
}
```

## 立即行动项

1. **检查数据库连接字符集配置**
2. **启用SQL日志查看实际执行的SQL**
3. **使用简单数据测试单条插入**
4. **检查输入数据的字符编码**

## 预防措施

1. 在生产环境中使用PreparedStatement（MyBatis已经处理）
2. 对输入数据进行严格验证
3. 使用统一的字符编码配置
4. 添加完善的错误处理和日志记录
