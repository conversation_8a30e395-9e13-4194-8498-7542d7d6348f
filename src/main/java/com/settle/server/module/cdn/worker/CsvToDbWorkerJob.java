package com.settle.server.module.cdn.worker;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.sql.SqlBuilder;
import cn.hutool.setting.Setting;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.constant.RuleConst;
import com.settle.server.module.cdn.flatfile.DelimitedRecordMapper;
import com.settle.server.module.cdn.flatfile.FlatFileRecordReader;
import com.settle.server.module.cdn.jdbc.BeanPropertiesPreparedStatementProvider;
import com.settle.server.module.cdn.jdbc.JdbcRecordWriter;
import com.settle.server.module.cdn.job.Job;
import com.settle.server.module.cdn.job.JobBuilder;
import com.settle.server.module.cdn.listener.*;
import com.settle.server.module.cdn.mapper.RecordMapper;
import com.settle.server.module.cdn.writer.RecordWriter;

import javax.sql.DataSource;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * CSV文件 -> 数据库
 *
 * <AUTHOR>
 */
public final class CsvToDbWorkerJob<I, O> implements WorkerJob {

    private DataSource dataSource;
    private Setting setting;
    private String jobName;
    private int batchSize;
    private Map<String, Class<?>> classMap;
    private FileListener fileListener;
    private PipelineListener pipelineListener;
    private RecordMapper<?, ?> recordMapper;
    private RecordWriter<O> recordWriter;
    private RecordWriterListener<O> recordWriterListener;

    private File dataFile;

    public CsvToDbWorkerJob() {
        //no op
    }

    public CsvToDbWorkerJob(final File dataFile, final Setting setting, final Map<String, Class<?>> classMap, DataSource stludrDataSource) {
        this.dataFile = dataFile;
        this.setting = setting;
        this.classMap = classMap;
        this.dataSource=stludrDataSource;
    }

    @Override
    public void beforeBuildWorkerJob() {
        this.jobName = this.dataFile.getName();
        List<String> tableFields = StrUtil.split(setting.getStr(RuleConst.G_K_TABLE_FIELDS), ',');
        Entity entity = new Entity(setting.getStr(RuleConst.G_K_TABLE_NAME));

        List<String> camelFields = new ArrayList<>();
        tableFields.forEach(field -> {
            entity.set(field, field);
            camelFields.add(StrUtil.toCamelCase(StrUtil.trim(field)));
        });

        entity.set(NormalConst.FIELD_LOG_ID, NormalConst.FIELD_LOG_ID);
        entity.set(NormalConst.FIELD_LINE_NUM, NormalConst.FIELD_LINE_NUM);

        //拼接SQL
        String execSql = SqlBuilder.create().insert(entity).build();
        camelFields.add(NormalConst.CAMEL_LOG_ID);
        camelFields.add(NormalConst.CAMEL_LINE_NUM);

        String logId = IdUtil.createSnowflake(1L, 1L).nextIdStr();
        this.fileListener = new DefaultFileLister(logId, dataFile, setting,dataSource);
        this.pipelineListener = new DefaultPipelineListener(logId,dataSource);

        //依据表名转类名，如：cust_prod_info -> CustProdInfo
        String className = StrUtil.upperFirst(StrUtil.toCamelCase(setting.getStr(RuleConst.G_K_TABLE_NAME)));
        Class<?> recordClass = classMap.get(className);
        DelimitedRecordMapper delimitedRecordMapper = new DelimitedRecordMapper(recordClass, camelFields.toArray(new String[camelFields.size()]), tableFields.size(), logId);
        //设置记录分隔符（如未设置，缺省为：英文逗号）
        delimitedRecordMapper.setDelimiter(setting.getStr(RuleConst.G_K_DELIMITER, ","));
        this.recordMapper = delimitedRecordMapper;

//        DataSource dataSource = DSFactory.get(setting.getStr(RuleConst.G_K_DB_GROUP));
        this.recordWriter = new JdbcRecordWriter<>(dataSource, execSql, new BeanPropertiesPreparedStatementProvider(recordClass, camelFields.toArray(new String[camelFields.size()])));

        this.recordWriterListener = new DefaultRecordWriterListener<>(logId,dataSource);

        //作业批量大小（如未设置，缺省为100）
        this.batchSize = setting.getInt(RuleConst.G_K_BATCH_SIZE, 100);
    }


    @Override
    public Job buildWorkerJob() {
        String workPath = setting.getStr(RuleConst.G_K_WORK_PATH, NormalConst.DEFAULT_WORK_PATH);
        Path path = Paths.get(FileUtil.file(workPath + FileUtil.FILE_SEPARATOR + this.dataFile.getName()).getAbsolutePath());
        return new JobBuilder<String, O>()
                .named(this.jobName)
                .batchSize(this.batchSize)
                .fileListener(this.fileListener)
                .pipelineListener(this.pipelineListener)
//                .writerListener(this.recordWriterListener)
                .reader(new FlatFileRecordReader(path))
//                .filter(new HeaderRecordFilter<>())
                .mapper(this.recordMapper)
                .writer(this.recordWriter)
                .build();

    }
}
