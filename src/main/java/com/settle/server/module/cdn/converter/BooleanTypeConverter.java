package com.settle.server.module.cdn.converter;

/**
 * Boolean 类型转换器：将“true”、“1”、“yes”和“on”（忽略大小写）转换为布尔真值
 * 任何其他值都将转换为 false
 *
 * <AUTHOR>
 */
public class BooleanTypeConverter implements TypeConverter<String, Boolean> {

    /**
     * {@inheritDoc}
     */
    @Override
    public Boolean convert(final String value) {
        return Boolean.parseBoolean(value) || "1".equals(value) || "on".equalsIgnoreCase(value) || "yes".equalsIgnoreCase(value);
    }

}
