package com.settle.server.module.clearup.dto;

import lombok.Getter;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/26
 * @since 1.0.0
 */
@Getter
public enum PartitionTypeEnum {

    DAILY("day"),

    MONTHLY("month");

    private final String type;

    PartitionTypeEnum(String type) {
        this.type = type;
    }

    public static PartitionTypeEnum getByType(String type) {
        for (PartitionTypeEnum value : PartitionTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}