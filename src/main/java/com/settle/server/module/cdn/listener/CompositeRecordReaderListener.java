package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Record;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * 将处理委托给其他侦听器的复合监听器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class CompositeRecordReaderListener<P> implements RecordReaderListener<P> {

    private List<RecordReaderListener<P>> listeners;

    /**
     * 创建一个新的 {@link CompositeRecordReaderListener}
     */
    public CompositeRecordReaderListener() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositeRecordReaderListener}
     *
     * @param listeners 委托
     */
    public CompositeRecordReaderListener(List<RecordReaderListener<P>> listeners) {
        this.listeners = listeners;
    }

    @Override
    public void beforeRecordReading() {
        for (RecordReaderListener<P> listener : listeners) {
            listener.beforeRecordReading();
        }
    }

    @Override
    public void afterRecordReading(Record<P> record) {
        for (ListIterator<RecordReaderListener<P>> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().afterRecordReading(record);
        }
    }

    @Override
    public void onRecordReadingException(Throwable throwable) {
        for (ListIterator<RecordReaderListener<P>> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().onRecordReadingException(throwable);
        }
    }

    /**
     * 添加委托监听器
     *
     * @param recordReaderListener 添加
     */
    public void addRecordReaderListener(RecordReaderListener<P> recordReaderListener) {
        listeners.add(recordReaderListener);
    }
}
