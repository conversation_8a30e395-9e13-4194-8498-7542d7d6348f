package com.hp.cmcc.bboss.settle.app.validation.biz;

import com.google.common.collect.Lists;
import com.hp.cmcc.bboss.settle.app.validation.cache.AppContext;
import com.hp.cmcc.bboss.settle.app.validation.cache.CacheHelper;
import com.hp.cmcc.bboss.settle.app.validation.concurrent.FileMoveProcess;
import com.hp.cmcc.bboss.settle.app.validation.concurrent.FutureTaskScheduler;
import com.hp.cmcc.bboss.settle.app.validation.concurrent.LogCollectTask;
import com.hp.cmcc.bboss.settle.app.validation.concurrent.LogFileTask;
import com.hp.cmcc.bboss.settle.app.validation.dto.CiddRowDTO;
import com.hp.cmcc.bboss.settle.app.validation.dto.FileFormat;
import com.hp.cmcc.bboss.settle.app.validation.dto.IddFileCxtDTO;
import com.hp.cmcc.bboss.settle.app.validation.dto.ProcessFileDTO;
import com.hp.cmcc.bboss.settle.app.validation.entity.*;
import com.hp.cmcc.bboss.settle.app.validation.enums.CacheEnum;
import com.hp.cmcc.bboss.settle.app.validation.enums.LogStatusEnum;
import com.hp.cmcc.bboss.settle.app.validation.service.FileValidationDupchkService;
import com.hp.cmcc.bboss.settle.app.validation.util.DateUtil;
import com.hp.cmcc.bboss.settle.app.validation.util.SeqGenTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/1/23
 * @since 1.0.0
 */
@Service
@Slf4j
public class FileProcessConsumer {
    @Autowired
    private FileValidationDupchkService dupchkService;
    @Autowired
    private CacheHelper cacheHelper;
    private static final Long INTERVAL = 1000L;

    public void consumer() {
        while (FileProcessManager.RUNNING.get()) {
            try {
                ProcessFileDTO processFileDTO = AppContext.takeAndRegister(Thread.currentThread().getName());
                if (processFileDTO == null) {
                    sleep(INTERVAL);
                    continue;
                }
                this.doProcess(processFileDTO);
            } catch (Exception e) {
                log.error("消费者处理文件异常：", e);
            }
        }
    }

    private void doProcess(ProcessFileDTO processFileDTO) {
        StopWatch stopWatch = new StopWatch();
        StringBuilder biz = new StringBuilder("开始处理文件：").append(processFileDTO.getFileName());
        stopWatch.start();
        String procDate = DateUtil.dateFormat(new Date(), DateUtil.YYYYMMDDHHMMSS);
        LogFileT srcLogFile = null;
        LogCollectT logCollect = null;
        LogFileT destLogFile = null;
        LogStatusEnum logStatusEnum = LogStatusEnum.SUCCESS;
        try {
            FileFormat fileFormat = processFileDTO.getFileFormat();
            List<String> lines = FileUtils.readLines(processFileDTO.getFile(), "UTF-8");
            logCollect = this.getClollectLog(processFileDTO, lines.size(), procDate);
            //1.校验文件重复
            if (!this.checkFile(processFileDTO)) {
                biz.append("-文件重复，不处理");
                logStatusEnum = LogStatusEnum.DUPCHK;
                logCollect.setState(logStatusEnum.getStatus());
                logCollect.setRemark(logStatusEnum.getDesc());
                logCollect.setUsageCount(0);
                return;
            }
            srcLogFile = this.getFileLog(processFileDTO, fileFormat.getInFileType(), procDate);
            srcLogFile.setUsageCount((long) lines.size());
            //2.处理每行数据
            IddFileCxtDTO iddFileCxtDTO = new IddFileCxtDTO();
            int lineNum = 0;
            for (String line : lines) {
                String[] ciddArr = line.split(fileFormat.getSeparator(),-1);
                CiddRowDTO ciddRowDTO = this.getCiddRow(ciddArr, fileFormat, ++lineNum);
                String iddRow = this.processIdd(ciddRowDTO, processFileDTO);
                iddFileCxtDTO.getIddRowList().add(iddRow);
            }
            //3.生成目标文件名
            String destFileName = this.genDestFileName(processFileDTO);
            iddFileCxtDTO.setIddFileName(destFileName);
            //4.生成idd文件
            this.writeIddFile(iddFileCxtDTO, fileFormat);
            biz.append("-生成idd文件：").append(destFileName);
            //5.记录目标文件日志
            destLogFile = this.getFileLog(processFileDTO, fileFormat.getOutFileType(), procDate);
            destLogFile.setFileName(destFileName);
            destLogFile.setUsageCount((long) iddFileCxtDTO.getIddRowList().size());
            biz.append("-处理成功");
        } catch (Exception e) {
            biz.append("-处理异常");
            log.error("文件:{},处理异常", processFileDTO.getFileName(), e);
            logStatusEnum = LogStatusEnum.FAIL;
            if (logCollect == null) {
                logCollect = this.getClollectLog(processFileDTO, 0, procDate);
            }
            logCollect.setState(logStatusEnum.getStatus());
            String stackTrace = ExceptionUtils.getStackTrace(e);
            stackTrace = stackTrace.length() > 240 ? stackTrace.substring(0, 240) : stackTrace;
            logCollect.setRemark(logStatusEnum.getDesc() + "." + stackTrace);
        } finally {
            AppContext.removeWorkMap(processFileDTO.getFileName());
            saveLogs(srcLogFile, logCollect, destLogFile);
            moveSrcFile(processFileDTO, logStatusEnum);
            stopWatch.stop();
            biz.append(",耗时：").append(stopWatch.getTotalTimeMillis()).append("ms");
            log.info(biz.toString());
        }

    }

    private void writeIddFile(IddFileCxtDTO iddFileCxtDTO, FileFormat fileFormat) throws IOException {
        //写入到本地磁盘
        String localPath = fileFormat.getLocalPath();
        String iddFileName = iddFileCxtDTO.getIddFileName();
        File iddFile = new File(localPath, iddFileName);

        FileUtils.createParentDirectories(iddFile);

        Files.write(iddFile.toPath(), iddFileCxtDTO.getIddRowList(), StandardCharsets.UTF_8);
        log.info("生成idd文件：{}", iddFileName);
        //移动到目标路径
        String outPath = fileFormat.getOutPath();
        FutureTaskScheduler.add(new FileMoveProcess(localPath, outPath, iddFileName));
    }

    private String genDestFileName(ProcessFileDTO processFileDTO) {
        String fileName = processFileDTO.getFileName();
        FileFormat fileFormat = processFileDTO.getFileFormat();
        String outFileName = fileFormat.getOutFileNameFormat();
        // %(BIZ_TYPE)-%(ORI_FILENAME)
        if (outFileName.contains("%(BIZ_TYPE)")) {
            outFileName = outFileName.replace("%(BIZ_TYPE)", fileFormat.getBizType());
        }
        if (outFileName.contains("%(ORI_FILENAME)")) {
            outFileName = outFileName.replace("%(ORI_FILENAME)", fileName);
        }
        return outFileName;
    }

    private String processIdd(CiddRowDTO ciddRowDTO, ProcessFileDTO processFileDTO) {
        FileFormat fileFormat = processFileDTO.getFileFormat();
        StringJoiner joiner = new StringJoiner(fileFormat.getSeparator());
        Map<String, String> fieldValMap = ciddRowDTO.getCiddValList()
                .stream()
                .collect(Collectors.toMap(CiddRowDTO.CiddValDTO::getFieldName, CiddRowDTO.CiddValDTO::getFieldValue, (key1, key2) -> key2));
        String offerOrderId = fieldValMap.getOrDefault("OFFER_ORDER_ID", StringUtils.EMPTY).trim();
        String orgMonth = fieldValMap.getOrDefault("ORG_MONTH", StringUtils.EMPTY).trim();
        String memNumber = fieldValMap.getOrDefault("MEM_NUMBER", StringUtils.EMPTY).trim();
        //获取订购信息
        StlServBizCode servBiz = this.getServBiz(offerOrderId, orgMonth);
        //获取客户信息
        StlCustomer customer = null;
        if (servBiz != null) {
            customer = this.getCustomer(servBiz.getEcCode(), orgMonth);
        }
        //获取成员省编码
        String memProv = this.getMemberProv(memNumber, orgMonth);

        List<FileFormat.IDD> iddList = fileFormat.getIddList();
        for (FileFormat.IDD idd : iddList) {
            String iddFieldName = StringUtils.trim(idd.getFieldName());
            switch (iddFieldName) {
                case "ERROR_CODE":
                    joiner.add("V000");
                    break;
                case "ERROR_LINE":
                    joiner.add(String.valueOf(ciddRowDTO.getRowNumber()));
                    break;
                case "BIZ_TYPE":
                    joiner.add(fileFormat.getBizType());
                    break;
                case "ATTACHMENT":
                    joiner.add(ciddRowDTO.getRowContent());
                    break;
                case "FILE_ID":
                    joiner.add(String.valueOf(processFileDTO.getFileId()));
                    break;
                case "TICKET_ID":
                    joiner.add(SeqGenTool.seqString());
                    break;
                case "EC_CODE":
                    if (servBiz == null) {
                        joiner.add(StringUtils.EMPTY);
                    } else {
                        joiner.add(StringUtils.trim(servBiz.getEcCode() == null ? "" : servBiz.getEcCode()));
                    }
                    break;
                case "SIGN_ENTITY":
                    if (servBiz == null) {
                        joiner.add(StringUtils.EMPTY);
                    } else {
                        joiner.add(StringUtils.trim(servBiz.getSignEntity()==null ? "" : servBiz.getSignEntity()));
                    }
                    break;
                case "ORDER_PROV":
                    if (servBiz == null) {
                        joiner.add(StringUtils.EMPTY);
                    } else {
                        joiner.add(StringUtils.trim(servBiz.getProvCode() == null ? "" : servBiz.getProvCode()));
                    }
                    break;
                case "EC_PROV_CODE":
                    if (customer == null) {
                        joiner.add(StringUtils.EMPTY);
                    } else {
                        joiner.add(StringUtils.trim(customer.getProvinceCode() == null ? "" : customer.getProvinceCode()));
                    }
                    break;
                case "MEM_PROV":
                    if (StringUtils.isBlank(memProv)) {
                        joiner.add(StringUtils.EMPTY);
                    } else {
                        joiner.add(StringUtils.trim(memProv));
                    }
                case "COUNTY_CODE":
                    if (customer == null) {
                        joiner.add(StringUtils.EMPTY);
                    } else {
                        joiner.add(StringUtils.trim(customer.getCountyCode() == null ? "" : customer.getCountyCode()));
                    }
                    break;
                default:
                    String fieldValue = fieldValMap.getOrDefault(iddFieldName, StringUtils.EMPTY);
                    joiner.add(StringUtils.trim(fieldValue));
                    break;
            }

        }
        return joiner.toString();
    }

    private String getMemberProv(String memNumber, String orgMonth) {
        if (StringUtils.isBlank(memNumber)) {
            return "";
        }
        String _number = memNumber;
        if (StringUtils.startsWith(memNumber, "86")) {
            _number = memNumber.substring(2);
        }
        if (StringUtils.startsWith(memNumber, "0086")) {
            _number = memNumber.substring(4);
        }
        List<StlImsiLdCd> imsiLdCdList = null;
        if (_number.length() >= 9) {
            for (int i = 9; i > 6; i--) {
                imsiLdCdList = cacheHelper.getDateByKey(CacheEnum.IMSI_LD_CD, _number.substring(0, i));
                if (CollectionUtils.isNotEmpty(imsiLdCdList)) {
                    break;
                }
            }
        }
        if (CollectionUtils.isEmpty(imsiLdCdList)) {
            if (!memNumber.startsWith("0")) {
                _number = "0" + memNumber;
            }
            if (_number.length() > 3) {
                //座机号码
                for (int i = 4; i > 2; i--) {
                    List<StlDomLdAreaCdProv> areaCdProvList = cacheHelper.getDateByKey(CacheEnum.AREA_CD_PROV, _number.substring(0, i));
                    if (CollectionUtils.isNotEmpty(areaCdProvList)) {
                        return String.valueOf(areaCdProvList.get(0).getProvCd());
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(imsiLdCdList)) {
            if (orgMonth.length() > 6) {
                orgMonth  = StringUtils.substring(orgMonth, 0, 6);
            }
            final int finalOrgMonth = Integer.parseInt(orgMonth);
            Predicate<StlImsiLdCd> predicate = imsiLdCd -> {
                int effectiveDate = Integer.parseInt(DateUtil.dateFormat(imsiLdCd.getEffTm(), DateUtil.YYYYMM));
                int expiryDate = Integer.parseInt(DateUtil.dateFormat(imsiLdCd.getExpTm(), DateUtil.YYYYMM));
                return effectiveDate <= finalOrgMonth && finalOrgMonth <= expiryDate;
            };
            Optional<StlImsiLdCd> optional = imsiLdCdList.stream().filter(predicate).findFirst();
            if (optional.isPresent()) {
                List<StlDomLdAreaCdProv> areaCdProvList = cacheHelper.getDateByKey(CacheEnum.AREA_CD_PROV, optional.get().getLdAreaCd());
                if (CollectionUtils.isNotEmpty(areaCdProvList)) {
                    return String.valueOf(areaCdProvList.get(0).getProvCd());
                }
            }else {
                log.warn("未找到号码:{},账期:{},对应的省编码", memNumber, orgMonth);
            }
        }
        log.warn("未找到号码:{},对应的省编码", memNumber);
        return "";
    }

    private StlCustomer getCustomer(String ecCode, String orgMonth) {
        if (StringUtils.isBlank(ecCode)) {
            log.warn("ecCode为空");
            return null;
        }
        List<StlCustomer> customerList = cacheHelper.getDateByKey(CacheEnum.CUSTOMER, ecCode);
        if (CollectionUtils.isEmpty(customerList)) {
            log.warn("未找到ecCode:{}对应的客户信息", ecCode);
            return null;
        }
        if (orgMonth.length() > 6) {
            orgMonth  = StringUtils.substring(orgMonth, 0, 6);
        }
        final int finalOrgMonth = Integer.parseInt(orgMonth);
        Predicate<StlCustomer> predicate = customer -> {
            int effectiveDate = Integer.parseInt(DateUtil.dateFormat(customer.getEffectiveDate(), DateUtil.YYYYMM));
            int expiryDate = Integer.parseInt(DateUtil.dateFormat(customer.getExpiryDate(), DateUtil.YYYYMM));
            return effectiveDate <= finalOrgMonth && finalOrgMonth <= expiryDate;
        };
        Optional<StlCustomer> optional = customerList.stream().filter(predicate).findFirst();
        if (!optional.isPresent()) {
            log.warn("未找到ecCode:{},账期:{},对应的客户信息", ecCode, orgMonth);
        }
        return optional.orElse(null);
    }

    private StlServBizCode getServBiz(String offerOrderId, String orgMonth) {
        if (StringUtils.isBlank(offerOrderId)) {
            log.warn("offerOrderId为空");
            return null;
        }
        List<StlServBizCode> servBizList = cacheHelper.getDateByKey(CacheEnum.SERV_BIZ, offerOrderId);
        if (CollectionUtils.isEmpty(servBizList)) {
            log.warn("未找到offerOrderId:{}对应的订购信息", offerOrderId);
            return null;
        }
        if (orgMonth.length() > 6) {
            orgMonth  = StringUtils.substring(orgMonth, 0, 6);
        }
        final int finalOrgMonth = Integer.parseInt(orgMonth);
        Predicate<StlServBizCode> predicate = servBiz -> {
            int effectiveDate = Integer.parseInt(DateUtil.dateFormat(servBiz.getEffectiveDate(), DateUtil.YYYYMM));
            int expiryDate = Integer.parseInt(DateUtil.dateFormat(servBiz.getExpiryDate(), DateUtil.YYYYMM));
            return effectiveDate <= finalOrgMonth && finalOrgMonth <= expiryDate;
        };
        Optional<StlServBizCode> optional = servBizList.stream().filter(predicate).findFirst();
        if (!optional.isPresent()) {
            log.warn("未找到offerOrderId:{},账期:{},对应的订购信息", offerOrderId, orgMonth);
        }
        return optional.orElse(null);
    }

    private void moveSrcFile(ProcessFileDTO processFileDTO, LogStatusEnum logStatusEnum) {
        FileFormat fileFormat = processFileDTO.getFileFormat();
        if (logStatusEnum == LogStatusEnum.SUCCESS) {
            String backPath = this.getBackupPath(fileFormat.getBakPath());
            FutureTaskScheduler.add(new FileMoveProcess(fileFormat.getInPath(), backPath, processFileDTO.getFileName()));
        } else {
            String errFileName = logStatusEnum.getCode() + processFileDTO.getFileName();
            FutureTaskScheduler.add(new FileMoveProcess(fileFormat.getInPath(), fileFormat.getErrPath(), processFileDTO.getFileName(),errFileName));
        }
    }

    private String getBackupPath(String bakPath) {
        int i = bakPath.lastIndexOf("$");
        if (i > 0) {
            String placeHolder = bakPath.substring(i);
            String replaceStr = DateUtil.dateFormat(new Date(), DateUtil.YYYYMMDD);
            return bakPath.replace(placeHolder, replaceStr);
        }
        return bakPath;
    }

    private void saveLogs(LogFileT srcLogFile, LogCollectT logCollect, LogFileT destLogFile) {
        if (srcLogFile != null) {
            //保存源文件日志
            FutureTaskScheduler.add(new LogFileTask(srcLogFile));
        }
        if (logCollect != null) {
            //保存汇总日志
            FutureTaskScheduler.add(new LogCollectTask(logCollect));
        }
        if (destLogFile != null) {
            //保存目标文件日志
            FutureTaskScheduler.add(new LogFileTask(destLogFile));
        }
    }

    private LogCollectT getClollectLog(ProcessFileDTO processFileDTO, int size, String procDate) {
        LogCollectT logCollectT = new LogCollectT();
        logCollectT.setIoidId0(SeqGenTool.seqLong());
        logCollectT.setFileId(processFileDTO.getFileId());
        logCollectT.setFileName(processFileDTO.getFileName());
        logCollectT.setBizType(processFileDTO.getFileFormat().getBizType());
        logCollectT.setUsageCount(size);
        logCollectT.setRawCount(size);
        logCollectT.setState(LogStatusEnum.SUCCESS.getStatus());
        logCollectT.setRemark(LogStatusEnum.SUCCESS.getDesc());
        logCollectT.setProcessDate(procDate);
        logCollectT.setAcctMonth(procDate.substring(0, 6));
        logCollectT.setPartitionIdMonth(Integer.parseInt(procDate.substring(4, 6)));
        logCollectT.setCreatedTime(LocalDateTime.now());
        logCollectT.setModTime(LocalDateTime.now());
        return logCollectT;
    }

    private LogFileT getFileLog(ProcessFileDTO processFileDTO, String fileType, String procDate) {
        LogFileT logFileT = new LogFileT();
        logFileT.setIoidId0(SeqGenTool.seqLong());
        logFileT.setFileId(processFileDTO.getFileId());
        logFileT.setFileName(processFileDTO.getFileName());
        logFileT.setFileType(fileType);
        logFileT.setBizType(processFileDTO.getFileFormat().getBizType());
        logFileT.setUsageCount(0L);
        logFileT.setState(LogStatusEnum.SUCCESS.getStatus());
        logFileT.setUseFlag(0);
        logFileT.setModual("VALID");
        logFileT.setCreatedTime(LocalDateTime.now());
        logFileT.setModTime(LocalDateTime.now());
        logFileT.setProcessDate(procDate);
        logFileT.setAcctMonth(procDate.substring(0, 6));
        logFileT.setPartitionIdMonth(Integer.parseInt(procDate.substring(4, 6)));
        return logFileT;
    }

    private boolean checkFile(ProcessFileDTO processFileDTO) {
        File file = processFileDTO.getFile();
        String fileName = file.getName();
        String bizType = processFileDTO.getFileFormat().getBizType();
        return dupchkService.saveOnNotExist(fileName, bizType);
    }

    private CiddRowDTO getCiddRow(String[] ciddArr, FileFormat fileFormat, int lineNum) {
        CiddRowDTO ciddRowDTO = new CiddRowDTO();
        List<CiddRowDTO.CiddValDTO> list = Lists.newArrayList();
        List<FileFormat.CIDD> ciddList = fileFormat.getCiddList();
        for (FileFormat.CIDD cidd : ciddList) {
            CiddRowDTO.CiddValDTO ciddValDTO = new CiddRowDTO.CiddValDTO();
            ciddValDTO.setFieldName(cidd.getFieldName());
            if (cidd.getFieldIndex() > ciddArr.length) {
                ciddValDTO.setFieldValue(StringUtils.EMPTY);
                list.add(ciddValDTO);
                continue;
            }
            ciddValDTO.setFieldValue(ciddArr[(cidd.getFieldIndex() - 1)]);
            list.add(ciddValDTO);
        }
        ciddRowDTO.setCiddValList(list);
        ciddRowDTO.setRowContent(String.join("\\,", ciddArr));
        ciddRowDTO.setRowNumber(lineNum);
        return ciddRowDTO;
    }


    private void sleep(Long interval) {
        try {
            TimeUnit.MILLISECONDS.sleep(interval);
        } catch (Exception e) {
            log.error("生产者线程休眠异常：", e);
        }
    }
}