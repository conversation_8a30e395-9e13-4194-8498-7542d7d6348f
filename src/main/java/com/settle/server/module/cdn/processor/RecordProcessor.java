package com.settle.server.module.cdn.processor;


import com.settle.server.module.cdn.record.Record;

/**
 * 记录处理器对输入记录执行业务逻辑并生成输出记录
 * 输出记录的类型可能与输入记录的类型不同，并且将通过管道输出到下一个处理器（如果有）
 * 如果记录处理器在处理过程中抛出异常，则该记录将被报告为错误
 * 如果记录处理器返回 null，则记录将被过滤并跳过管道中的下一个处理器
 *
 * @param <I> 负载的输入记录类型
 * @param <O> 有效载荷的输出记录类型
 * <AUTHOR>
 */
public interface RecordProcessor<I, O> {

    /**
     * 处理记录
     *
     * @param record 处理
     * @return 处理的记录，可能与输入记录的类型不同，或者 {@code null} 跳过下一个处理器
     * @throws Exception 如果在记录处理过程中发生错误
     */
    Record<O> processRecord(Record<I> record) throws Exception;

}
