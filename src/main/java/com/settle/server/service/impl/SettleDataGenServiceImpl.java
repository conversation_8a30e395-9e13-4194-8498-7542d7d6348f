package com.settle.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.config.SpecialLineConfig;
import com.settle.server.dao.bossAcct.CommonInvoiceDataLogDao;
import com.settle.server.dto.SettleDetailReqDTO;
import com.settle.server.entity.CommonInvoiceDataLog;
import com.settle.server.service.SettleDetailDataGenService;
import com.settle.server.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SettleDataGenServiceImpl implements SettleDetailDataGenService {

    @Autowired
    private SpecialLineConfig specialLineConfig;
    @Autowired
    private MinioUtil minioUtil;
    @Resource
    private CommonInvoiceDataLogDao commonInvoiceDataLogDao;

    @Override
    public boolean handleSpecialLineData(String acctMonth, List<String> rkeys) {

        // 构建请求DTO
        SettleDetailReqDTO reqDTO = buildRequestDTO(acctMonth, rkeys);

        // 发送HTTP请求
        String result = sendHttpRequest(reqDTO);
        log.info("请求调用结果:{}", result);

        // 解析响应
        JSONObject response = JSONUtil.parseObj(result);
        if (isResponseSuccess(response)) {
            // 处理文件上传
            return processFileUpload(acctMonth, rkeys);
        } else {
            logErrorResponse(response);
            return false;
        }
    }

    private SettleDetailReqDTO buildRequestDTO(String acctMonth, List<String> rkeys) {
        SettleDetailReqDTO reqDTO = new SettleDetailReqDTO();
        reqDTO.setSettlemonth(acctMonth);
        reqDTO.setRaqList(rkeys);
        return reqDTO;
    }

    private String sendHttpRequest(SettleDetailReqDTO reqDTO) {
        return HttpRequest.post(specialLineConfig.getRemoteUrl())
                .body(JSONUtil.toJsonStr(reqDTO))
                .execute()
                .body();
    }

    private boolean isResponseSuccess(JSONObject response) {
        String code = String.valueOf(response.get("code"));
        return StrUtil.isNotBlank(code) && StrUtil.equals("200", code);
    }

    private boolean processFileUpload(String acctMonth, List<String> rkeys) {
        // 删除旧的日志记录
        commonInvoiceDataLogDao.deleteLog(acctMonth, rkeys);

        String remotePath = specialLineConfig.getRemotePath();
        List<CommonInvoiceDataLog> commonInvoiceDataLogs = new ArrayList<>();
        for (String rkey : rkeys) {
            String path = remotePath + acctMonth + File.separator + rkey ;
            File[] files = FileUtil.ls(path);
            log.info("账期：{},开始获取目录下文件：{}, 文件数量:{}", acctMonth,path, files.length);
            for (File file : files) {
                String fileName = minioUtil.upload(file,acctMonth);
                log.info("{}, 上传文件成功", fileName);
                CommonInvoiceDataLog logEntry = createLogEntry(rkey, acctMonth, file, fileName);
                commonInvoiceDataLogs.add(logEntry);
            }
        }
        commonInvoiceDataLogDao.saveBatch(commonInvoiceDataLogs);
        return true;
    }

    private CommonInvoiceDataLog createLogEntry(String rkey, String acctMonth, File file, String fileName) {
        CommonInvoiceDataLog logEntry = new CommonInvoiceDataLog();
        logEntry.setFileType(rkey);
        logEntry.setId(IdUtil.fastSimpleUUID());
        logEntry.setFileName(file.getName());
        logEntry.setFilePath(fileName);
        logEntry.setAcctMonth(acctMonth);
        logEntry.setCreateDate(new Date());
        return logEntry;
    }

    private void logErrorResponse(JSONObject response) {
        String errMsg = String.valueOf(response.get("msg"));
        log.error("远程调用出错：{}", errMsg);
    }

}
