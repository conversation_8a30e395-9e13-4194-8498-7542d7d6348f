package com.settle.server.module.esp.enums;

import lombok.Getter;

@Getter
public enum BizCode {
    BIZ_4_6("4.6", "结算账单上传接口","ESP"),
    BIZ_4_16("4.16", "移动云月计费费用结算清单接口","MC"),
    BIZ_4_29("4.29", "直管自有I+P月收入信息同步","PVBILL"),
    BIZ_4_30("4.30", "直管自有I+P量价结算清单接口","PVSETTLE"),
    BIZ_4_31("4.31", "BC融合2C销售渠道移动云结算清单接口","BC2C"),
    ;

    private String interfaceNum;

    private String interfaceName;
    private String bizType;

    BizCode(String interfaceNum, String interfaceName, String bizType) {
        this.interfaceNum = interfaceNum;
        this.interfaceName = interfaceName;
        this.bizType = bizType;
    }
}
