package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.job.JobParameters;
import com.settle.server.module.cdn.job.JobReport;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * 将处理委托给其他侦听器的复合监听器
 *
 * <AUTHOR>
 */
public class CompositeJobListener implements JobListener {

    private List<JobListener> listeners;

    /**
     * 创建一个新的 {@link CompositeJobListener}
     */
    public CompositeJobListener() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositeJobListener}
     *
     * @param listeners 委托
     */
    public CompositeJobListener(List<JobListener> listeners) {
        this.listeners = listeners;
    }

    @Override
    public void beforeJob(JobParameters jobParameters) {
        for (JobListener listener : listeners) {
            listener.beforeJob(jobParameters);
        }
    }

    @Override
    public void afterJob(JobReport jobReport) {
        for (ListIterator<JobListener> iterator
             = listeners.listIterator(listeners.size());
             iterator.hasPrevious(); ) {
            iterator.previous().afterJob(jobReport);
        }
    }

    /**
     * 添加委托监听器
     *
     * @param jobListener 要添加的作业监听器
     */
    public void addJobListener(JobListener jobListener) {
        listeners.add(jobListener);
    }
}
