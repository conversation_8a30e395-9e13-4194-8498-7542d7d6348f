package com.settle.server.module.cdn.reader;


import com.settle.server.module.cdn.record.Record;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 从 {@link BlockingQueue} 读取记录的 {@link RecordReader}
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class BlockingQueueRecordReader<P> implements RecordReader<P> {

    public static final long DEFAULT_TIMEOUT = 60000;

    private BlockingQueue<Record<P>> queue;
    private long timeout;

    /**
     * 创建一个新的 {@link BlockingQueueRecordReader}
     *
     * @param queue 从中读取记录的队列
     */
    public BlockingQueueRecordReader(final BlockingQueue<Record<P>> queue) {
        this(queue, DEFAULT_TIMEOUT);
    }

    /**
     * 创建一个新的 {@link BlockingQueueRecordReader}
     *
     * @param queue   从中读取记录的队列
     * @param timeout 以毫秒为单位，之后读取器将返回 null
     */
    public BlockingQueueRecordReader(final BlockingQueue<Record<P>> queue, final long timeout) {
        this.queue = queue;
        this.timeout = timeout;
    }

    @Override
    public Record<P> readRecord() throws Exception {
        //超时后返回 null
        return queue.poll(timeout, TimeUnit.MILLISECONDS);
    }

}
