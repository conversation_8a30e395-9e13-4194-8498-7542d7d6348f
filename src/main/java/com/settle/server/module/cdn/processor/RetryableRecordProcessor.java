package com.settle.server.module.cdn.processor;

import com.settle.server.module.cdn.record.Record;
import com.settle.server.module.cdn.retry.RetryPolicy;
import com.settle.server.module.cdn.retry.RetryTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

/**
 * 处理记录时抛出异常时，使 {@link RecordProcessor} 可重试的装饰器
 * 这个装饰器在重试可能成功的瞬时错误的情况下很有用
 *
 * <AUTHOR>
 */
public class RetryableRecordProcessor<I, O> implements RecordProcessor<I, O> {

    private RecordProcessor<I, O> delegate;
    private RecordProcessingTemplate recordProcessingTemplate;

    /**
     * 创建一个新的 {@link RetryableRecordProcessor}
     *
     * @param delegate    记录处理器
     * @param retryPolicy 重试策略
     */
    public RetryableRecordProcessor(RecordProcessor<I, O> delegate, RetryPolicy retryPolicy) {
        this.delegate = delegate;
        this.recordProcessingTemplate = new RecordProcessingTemplate(retryPolicy);
    }

    @Override
    public Record<O> processRecord(Record<I> record) throws Exception {
        return recordProcessingTemplate.execute(new RecordProcessingCallable<>(delegate, record));
    }

    private static class RecordProcessingCallable<I, O> implements Callable<Record<O>> {

        private RecordProcessor<I, O> recordProcessor;

        private Record<I> record;

        RecordProcessingCallable(RecordProcessor<I, O> recordProcessor, Record<I> record) {
            this.recordProcessor = recordProcessor;
            this.record = record;
        }

        @Override
        public Record<O> call() throws Exception {
            return recordProcessor.processRecord(record);
        }

    }

    private static class RecordProcessingTemplate extends RetryTemplate {

        private final Logger LOGGER = LoggerFactory.getLogger(RecordProcessingTemplate.class.getName());

        RecordProcessingTemplate(RetryPolicy retryPolicy) {
            super(retryPolicy);
        }

        @Override
        protected void beforeCall() {
            // no op
        }

        @Override
        protected void afterCall(Object result) {
            // no op
        }

        @Override
        protected void onException(Exception e) {
            LOGGER.error("Unable to process record", e);
        }

        @Override
        protected void onMaxAttempts(Exception e) {
            LOGGER.error("Unable to process record after {} attempt(s)", retryPolicy.getMaxAttempts());
        }

        @Override
        protected void beforeWait() {
            LOGGER.debug("Waiting for {} {} before retrying to process record", retryPolicy.getDelay(), retryPolicy.getTimeUnit());
        }

        @Override
        protected void afterWait() {
            // no op
        }

    }
}
