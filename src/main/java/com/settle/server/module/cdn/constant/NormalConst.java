package com.settle.server.module.cdn.constant;

import cn.hutool.core.util.StrUtil;

/**
 * 普通常量
 *
 * <AUTHOR>
 */
public class NormalConst {


    /**
     * 缺省工作目录
     */
    public static final String DEFAULT_WORK_PATH = "../work/";
    /**
     * 缺省文件入口路径
     */
    public static final String DEFAULT_FILE_IN_PATH = "../incoming/";

    /**
     * 缺省文件出口路径
     */
    public static final String DEFAULT_FILE_OUT_PATH = "../outgoing/";
    /**
     * 缺省文件备份路径
     */
    public static final String DEFAULT_FILE_BAK_PATH = "../backup/{yyyy-MM-dd}/";

    /**
     * 缺省错误文件路径
     */
    public static final String DEFAULT_FILE_ERR_PATH = "../error/";

    /**
     * 要扫描的包路径
     */
    public static final String DOMAIN_PACKAGE_NAME = "com.bboss.batch.domain";

    /**
     * 匹配任意字符正则
     */
    public static final String DEFAULT_REG_MATCH_ANY = ".*";

    /**
     * 缺省日志表名称
     */
    public static final String DEFAULT_LOG_TABLE = "batch_log";

    /**
     * 缺省记录错误日志表
     */
    public static final String DEFAULT_RECORD_ERR_LOG_TABLE = "batch_record_error_log";

    /**
     * 缺省输出文件名
     */
    public static final String DEFAULT_OUT_FILE_NAME = "SETTLE-${yyyyMMddHHmmssSSS}.txt";

    public static final String DEFAULT_EMPTY_OUT_FILE_NAME = "EMPTY-.txt";

    /**
     * 日志ID字段名称、驼峰式命名的字符串
     */
    public static final String FIELD_LOG_ID = "org_log_id";
    public static final String CAMEL_LOG_ID = StrUtil.toCamelCase(FIELD_LOG_ID);

    /**
     * 文件行号字段名称、驼峰式命名的字符串
     */
    public static final String FIELD_LINE_NUM = "line_num";
    public static final String CAMEL_LINE_NUM = StrUtil.toCamelCase(FIELD_LINE_NUM);

    /**
     * 入参常量:
     * acctMonth: 指定账期月
     * acctMonthDay: 指定账期日
     * ruleFile：指定规则配置文件
     */
    public static final String ARG_ACCT_MONTH = "acctMonth";
    public static final String ARG_ACCT_MONTH_DAY = "acctMonthDay";
    public static final String ARG_RULE_FILE = "ruleFile";
}
