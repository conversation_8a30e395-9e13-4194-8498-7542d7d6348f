package com.settle.server.module.cdn.flatfile;


import com.settle.server.module.cdn.field.BeanFieldExtractor;
import com.settle.server.module.cdn.field.FieldExtractor;
import com.settle.server.module.cdn.marshaller.RecordMarshaller;
import com.settle.server.module.cdn.record.Record;
import com.settle.server.module.cdn.record.StringRecord;
import com.settle.server.utils.Utils;

import java.util.Iterator;

/**
 * 将 POJO 编组为 CSV 格式
 *
 * <strong>此编组器不支持递归编组</strong>
 *
 * @param <P> 记录有效负载的类型
 * <AUTHOR>
 */
public class DelimitedRecordMarshaller<P> implements RecordMarshaller<P, String> {

    public static final String DEFAULT_DELIMITER = ",";
    public static final String DEFAULT_QUALIFIER = "\"";

    private String delimiter;
    private String qualifier;
    private FieldExtractor<P> fieldExtractor;

    /**
     * 创建一个新的 {@link DelimitedRecordMarshaller}.
     *
     * @param type   编组的对象
     * @param fields 按顺序编组
     */
    public DelimitedRecordMarshaller(final Class<P> type, final String... fields) {
        this(new BeanFieldExtractor<>(type, fields));
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMarshaller}.
     *
     * @param fieldExtractor 用于提取字段
     */
    public DelimitedRecordMarshaller(FieldExtractor<P> fieldExtractor) {
        Utils.checkNotNull(fieldExtractor, "field extractor");
        this.fieldExtractor = fieldExtractor;
        this.delimiter = DEFAULT_DELIMITER;
        this.qualifier = DEFAULT_QUALIFIER;
    }

    @Override
    public StringRecord processRecord(final Record<P> record) throws Exception {
        Iterable<Object> values = fieldExtractor.extractFields(record.getPayload());
        StringBuilder stringBuilder = new StringBuilder();
        Iterator<?> iterator = values.iterator();
        while (iterator.hasNext()) {
            Object value = iterator.next();
            stringBuilder.append(qualifier);
            stringBuilder.append(value);
            stringBuilder.append(qualifier);
            if (iterator.hasNext()) {
                stringBuilder.append(delimiter);
            }
        }
        return new StringRecord(record.getHeader(), stringBuilder.toString());
    }

    /**
     * 设置要使用的分隔符
     *
     * @param delimiter 要使用的分隔符
     */
    public void setDelimiter(final String delimiter) {
        this.delimiter = delimiter;
    }

    /**
     * 设置要使用的数据限定符
     *
     * @param qualifier 要使用的数据限定符
     */
    public void setQualifier(final String qualifier) {
        this.qualifier = qualifier;
    }

}
