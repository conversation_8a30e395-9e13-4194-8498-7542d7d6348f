package com.settle.server.module.bigdata.enums;

import lombok.Getter;

@Getter
@Deprecated
public enum BizSettleUnitEnum {
    BIZ_TYPE_01("01","IDC外部客户",6000),
    BIZ_TYPE_02("02","互联网专线",6000),
    BIZ_TYPE_03("03","家庭宽带",6000),
    BIZ_TYPE_04("04","企业宽带",6000),
    BIZ_TYPE_05("05","IDC专业公司云能转售（经核实）",6000),
    BIZ_TYPE_06("06","IDC专业公司非云能转售（经核实）",6000),
    BIZ_TYPE_07("07","IDC专业公司云能转售（系统判断）",3200),
    BIZ_TYPE_08("08","IDC专业公司非云能转售（系统判断）",4445),
    BIZ_TYPE_09("09","IDC专业公司云能",2880),
    BIZ_TYPE_10("10","IDC专业公司非云能",4445),
    BIZ_TYPE_11("11","IDC分发TOP客户内容",6000),
    BIZ_TYPE_12("12","IDC-BGP违规配置",9000),
    BIZ_TYPE_13("13","IDC带宽-拉流",9000),
    BIZ_TYPE_14("14","互联网专线-拉流",9000),
    BIZ_TYPE_15("15","家庭宽带-拉流",9000),
    BIZ_TYPE_16("16","企业宽带-拉流",9000),
    ;

    private String bizType;
    private String bizDesc;
    // 单位（元/G/月）
    private int unitPrice;

    BizSettleUnitEnum(String bizType, String bizDesc, int unitPrice) {
        this.bizType = bizType;
        this.bizDesc = bizDesc;
        this.unitPrice = unitPrice;
    }

    public static BizSettleUnitEnum getByBizType(String bizType) {
        for (BizSettleUnitEnum bizSettleUnitEnum : BizSettleUnitEnum.values()) {
            if (bizSettleUnitEnum.getBizType().equals(bizType)) {
                return bizSettleUnitEnum;
            }
        }
        return null;
    }
}
