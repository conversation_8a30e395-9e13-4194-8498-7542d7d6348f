package com.settle.server.module.mnp.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.settle.server.module.mnp.dto.DataFmDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/1
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "mnpserver")
@NacosConfigurationProperties(dataId = "settle-service-tools", autoRefreshed = true)
@Data
public class MNPConfig {

    private String dataPath;

    private String backupPath;

    private String errorPath;

    private String tablePath;

    private List<DataFmDTO> tables;
}