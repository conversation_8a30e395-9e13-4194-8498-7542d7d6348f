package com.settle.server.module.cdn.converter;

import com.settle.server.utils.Utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * java.util.Date 类型转换器
 * 将字符串日期（默认为“yyyy-MM-dd”格式）转换为 {@link Date} 类型
 * 不接受 {@code null} 或空字符串
 *
 * <AUTHOR>
 */
public class DateTypeConverter implements TypeConverter<String, Date> {

    /**
     * 默认日期格式
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 要使用的日期格式
     */
    private String dateFormat;

    /**
     * 创建一个默认格式的日期转换器 {@link DateTypeConverter#DEFAULT_DATE_FORMAT}
     */
    public DateTypeConverter() {
        this(DEFAULT_DATE_FORMAT);
    }

    /**
     * 创建具有指定日期格式的日期转换器
     *
     * @param dateFormat 要使用的日期格式
     */
    public DateTypeConverter(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Date convert(final String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        try {
            return new SimpleDateFormat(dateFormat).parse(value);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Unable to convert value '" + value + "' to a Date object with format "
                    + dateFormat, e);
        }
    }

}
