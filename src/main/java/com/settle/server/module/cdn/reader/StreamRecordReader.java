package com.settle.server.module.cdn.reader;

import com.settle.server.module.cdn.record.GenericRecord;
import com.settle.server.module.cdn.record.Header;
import com.settle.server.module.cdn.record.Record;

import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.stream.Stream;

/**
 * 从流读取数据的 {@link RecordReader}
 * 此读取器生成 {@link GenericRecord} 实例
 *
 * @param <T> 流中元素的类型
 * <AUTHOR>
 */
public class StreamRecordReader<T> implements RecordReader<T> {

    protected Stream<T> dataSource;
    protected Iterator<T> iterator;
    protected long currentRecordNumber;

    /**
     * 创建一个新的 {@link StreamRecordReader} 以从 {@link Stream} 读取记录
     *
     * @param dataSource 从中读取记录
     */
    public StreamRecordReader(final Stream<T> dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void open() {
        if (dataSource == null) {
            throw new IllegalArgumentException("The stream must not be null");
        }
        currentRecordNumber = 0;
        iterator = dataSource.iterator();
    }

    @Override
    public Record<T> readRecord() {
        if (iterator.hasNext()) {
            Header header = new Header(++currentRecordNumber, getDataSourceName(), LocalDateTime.now());
            return new GenericRecord<>(header, iterator.next());
        } else {
            return null;
        }
    }

    private String getDataSourceName() {
        return "In-Memory Stream";
    }

    @Override
    public void close() {
        dataSource.close();
    }
}
