log4j.rootLogger=INFO, A1 ,R ,DBERROR

log4j.appender.A1=org.apache.log4j.ConsoleAppender
log4j.appender.A1.layout=org.apache.log4j.SimpleLayout

log4j.appender.R=org.apache.log4j.DailyRollingFileAppender
log4j.appender.R.File=$BBOSS_HOME/$BBOSS_APPID/var/log/xml2text.log
log4j.appender.R.Encoding=GBK
log4j.appender.R.DatePattern='.'yyyy-MM-dd
log4j.appender.R.layout=org.apache.log4j.PatternLayout
log4j.appender.R.layout.ConversionPattern=%d{dd MMM yyyy HH:mm:ss.SSS} [%p] [%t] - %m%n

log4j.appender.DBERROR=org.apache.log4j.jdbc.JDBCAppender
log4j.appender.DBERROR.URL=jdbc:oracle:thin:@************:1521:acc
log4j.appender.DBERROR.driver=oracle.jdbc.driver.OracleDriver
log4j.appender.DBERROR.user=bbossmon
log4j.appender.DBERROR.password=bbossmon
log4j.appender.DBERROR.sql=INSERT INTO log4jmsg(logid,createDate,HOSTNAME,HOSTIP,SYSTEMNAME,MODULENAME,category,method,filename,message,location,line) values(SEQ_LOG4JMSG.nextval,sysdate,'billserver','************','bboss2','x2t','%-5p','%t','%c.%F','%m','%l','%L')
log4j.appender.DBERROR.layout=org.apache.log4j.PatternLayout
log4j.appender.DBERROR.layout.ConversionPattern=%-5p','%t','%c.%F','%m','%l','%L
log4j.appender.DBERROR.Threshold=ERROR