<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.StlIotSettDao">
  <resultMap id="BaseResultMap" type="com.settle.server.entity.StlIotSett">
    <result column="SETTLE_MONTH" jdbcType="VARCHAR" property="settleMonth" />
    <result column="PRODUCT_SPEC_NUM" jdbcType="VARCHAR" property="productSpecNum" />
    <result column="SETTLE_TYPE" jdbcType="VARCHAR" property="settleType" />
    <result column="SETTLE_OUT" jdbcType="VARCHAR" property="settleOut" />
    <result column="SETTLE_IN" jdbcType="VARCHAR" property="settleIn" />
    <result column="PARTNER_NAME" jdbcType="VARCHAR" property="partnerName" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="SETTLE_AMOUNT" jdbcType="VARCHAR" property="settleAmount" />
    <result column="TAX_RATE" jdbcType="VARCHAR" property="taxRate" />
    <result column="SOURCE_FILE_NAME" jdbcType="VARCHAR" property="sourceFileName" />
    <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType" />
  </resultMap>

  <sql id="Base_Column_List">
    SETTLE_MONTH, PRODUCT_SPEC_NUM, SETTLE_TYPE, SETTLE_OUT, SETTLE_IN, PARTNER_NAME,
    PRODUCT_NAME, SETTLE_AMOUNT, TAX_RATE, SOURCE_FILE_NAME, BIZ_TYPE
  </sql>

  <select id="selectSumAmount" parameterType="java.lang.String" resultType="java.math.BigDecimal">
    select CAST(SUM(SETTLE_AMOUNT) AS DECIMAL(12, 4)) from STLUDR.STL_IOT_SETT where SETTLE_MONTH = #{acctMonth, jdbcType = VARCHAR} and SOURCE_FILE_NAME = #{fileName, jdbcType = VARCHAR}
                                                         AND BIZ_TYPE = #{bizType, jdbcType = VARCHAR}
  </select>

  <select id="selectCount" parameterType="java.lang.String" resultType="java.lang.String">
    select count(*) from STLUDR.STL_IOT_SETT where SETTLE_MONTH = #{acctMonth, jdbcType = VARCHAR} and SOURCE_FILE_NAME = #{fileName, jdbcType = VARCHAR}
                                               AND BIZ_TYPE = #{bizType, jdbcType = VARCHAR}
  </select>

  <insert id="batchInsert" useGeneratedKeys="false" parameterType="java.util.List">
    insert into STLUDR.STL_IOT_SETT (SETTLE_MONTH, PRODUCT_SPEC_NUM, SETTLE_TYPE, SETTLE_OUT, SETTLE_IN,
    PARTNER_NAME, PRODUCT_NAME, SETTLE_AMOUNT, TAX_RATE, SOURCE_FILE_NAME, BIZ_TYPE,PARTNER_NUMBER,PARTNER_RULE_TYPE)
    <foreach collection="list" item="stlIotSett" index="index" separator="UNION ALL">
      ( select
      #{stlIotSett.settleMonth, jdbcType = VARCHAR},
      #{stlIotSett.productSpecNum, jdbcType = VARCHAR},
      #{stlIotSett.settleType, jdbcType = VARCHAR},
      #{stlIotSett.settleOut, jdbcType = VARCHAR},
      #{stlIotSett.settleIn, jdbcType = VARCHAR},
      #{stlIotSett.partnerName, jdbcType = VARCHAR},
      #{stlIotSett.productName, jdbcType = VARCHAR},
      #{stlIotSett.settleAmount, jdbcType = VARCHAR},
      #{stlIotSett.taxRate, jdbcType = VARCHAR},
      #{stlIotSett.sourceFileName, jdbcType = VARCHAR},
      #{stlIotSett.bizType, jdbcType = VARCHAR},
      #{stlIotSett.partnerNumber, jdbcType = VARCHAR},
      #{stlIotSett.partnerRuleType, jdbcType = VARCHAR} from dual)
    </foreach>
  </insert>

  <delete id="deleteBySettleMonth" parameterType="java.lang.String">
    delete from STLUDR.STL_IOT_SETT
    <where>
      <if test="acctMonth != null and acctMonth != ''">
        AND SETTLE_MONTH = #{acctMonth, jdbcType = VARCHAR}
      </if>
      <if test="bizType != null and bizType != ''">
        AND BIZ_TYPE = #{bizType, jdbcType = VARCHAR}
      </if>
      <if test="fileName != null and fileName != ''">
        AND SOURCE_FILE_NAME = #{fileName, jdbcType = VARCHAR}
      </if>
    </where>
  </delete>
</mapper>