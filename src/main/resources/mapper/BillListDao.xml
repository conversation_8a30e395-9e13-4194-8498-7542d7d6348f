<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.settle.server.dao.bossAcct.BillListDao">
    <select id="queryBillList" resultType="com.settle.server.entity.SettleDataStatistics">
        select b.acct_month as settleMonth,sum(b.accu_occurence) as settleCount from boss_acct.bill_list b
        where b.acct_month >=DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{acctMonth}, '01'), '%Y%m%d'), INTERVAL 12 MONTH), '%Y%m') and b.flag=0 and b.order_mode =1 group by b.acct_month
    </select>
</mapper>