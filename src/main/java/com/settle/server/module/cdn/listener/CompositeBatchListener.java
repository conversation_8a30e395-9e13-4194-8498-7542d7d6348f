package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Batch;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * 将处理委托给其他侦听器的复合监听器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class CompositeBatchListener<P> implements BatchListener<P> {

    private List<BatchListener<P>> listeners;

    /**
     * 创建一个新的 {@link CompositeBatchListener}
     */
    public CompositeBatchListener() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositeBatchListener}
     *
     * @param listeners 委托
     */
    public CompositeBatchListener(List<BatchListener<P>> listeners) {
        this.listeners = listeners;
    }

    @Override
    public void beforeBatchReading() {
        for (BatchListener<P> listener : listeners) {
            listener.beforeBatchReading();
        }
    }

    @Override
    public void afterBatchProcessing(Batch<P> batch) {
        for (ListIterator<BatchListener<P>> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().afterBatchProcessing(batch);
        }
    }

    @Override
    public void afterBatchWriting(Batch<P> batch) {
        for (ListIterator<BatchListener<P>> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().afterBatchWriting(batch);
        }
    }

    @Override
    public void onBatchWritingException(Batch<P> batch, Throwable throwable) {
        for (ListIterator<BatchListener<P>> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().onBatchWritingException(batch, throwable);
        }
    }

    /**
     * 添加委托监听器
     *
     * @param batchListener 要添加的批处理监听器
     */
    public void addBatchListener(final BatchListener<P> batchListener) {
        listeners.add(batchListener);
    }
}
