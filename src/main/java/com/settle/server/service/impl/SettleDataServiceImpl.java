package com.settle.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.*;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.policy.AttachmentRenderPolicy;
import com.google.common.base.Splitter;
import com.settle.server.Plugin.CustomChartRenderPolicy;
import com.settle.server.config.AsyncTaskConfig;
import com.settle.server.config.MailAccountConfig;
import com.settle.server.config.SftpConfig;
import com.settle.server.config.wordPathConfig;
import com.settle.server.dao.bboss.LogFileDao;
import com.settle.server.dao.bossAcct.BillListDao;
import com.settle.server.dao.bossAcct.StatErrorDao;
import com.settle.server.dao.stludr.SettleDataDao;
import com.settle.server.entity.LogFileEntity;
import com.settle.server.entity.SettleDataProvince;
import com.settle.server.entity.SettleDataStatistics;
import com.settle.server.entity.StatErrorInfo;
import com.settle.server.entity.mail.MailAttachFile;
import com.settle.server.entity.mail.MailInfoVo;
import com.settle.server.service.SettleDataService;
import com.settle.server.utils.DateTimeUtil;
import com.settle.server.utils.HttpClientUtils;
import com.settle.server.utils.SFTPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SettleDataServiceImpl implements SettleDataService {

    @Autowired
    private SettleDataDao settleDataDao;

    @Autowired
    private LogFileDao logFileDao;

    @Autowired
    private StatErrorDao statErrorDao;

    @Autowired
    private BillListDao billListDao;

    @Autowired
    private CustomChartRenderPolicy customChartRenderPolicy;

    @Autowired
    private wordPathConfig wordPathConfig;

    @Autowired
    private HttpClientUtils httpClientUtils;

    @Autowired
    private MailAccountConfig mailAccountConfig;

    @Autowired
    private SftpConfig sftpConfig;

    @Autowired
    private AsyncTaskConfig asyncTaskConfig;

    @Override
    public void genWord(String acctMonth) {
        StopWatch watch = new StopWatch();
        watch.start();
        this.runTask(acctMonth);
        this.zipFile(acctMonth);
        this.uploadFile(acctMonth);
        this.sendEmail(acctMonth);
        watch.stop();
        log.info("成功生成文件 in {} s", TimeUnit.NANOSECONDS.toSeconds(watch.getNanoTime()));
    }

    private void runTask(String acctMonth) {
        String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
        String dataPath = wordPathConfig.getDataPath().replace("$<YYYY>", year).replace("$<dateTime>", acctMonth);
        FileUtil.mkdir(dataPath);
        Executor asyncExecutor = asyncTaskConfig.getAsyncExecutor();
        CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
            try {
                this.genDataExcel(acctMonth,dataPath);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, asyncExecutor);
        CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
            try {
                this.genSettleDataDesc(acctMonth,dataPath);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, asyncExecutor);
        CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
            try {
                this.genCwD208wordAndV101(acctMonth);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, asyncExecutor);
        CompletableFuture<Void> task4 = CompletableFuture.runAsync(() -> {
            try {
                this.genCwD202WordAndZQWord(acctMonth);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, asyncExecutor);

        List<CompletableFuture> completableFutures = new ArrayList<>();
        completableFutures.add(task1);
        completableFutures.add(task2);
        completableFutures.add(task3);
        completableFutures.add(task4);
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
    }

    private void uploadFile(String acctMonth) {
        SFTPUtils sftpUtils = new SFTPUtils();
        try {
            sftpUtils.connectServer(sftpConfig.getIp(), Integer.parseInt(sftpConfig.getPort()), sftpConfig.getUsername(), sftpConfig.getPassword());
            String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
            String remotePath = sftpConfig.getRemotePath().replace("$<YYYY>",year);
            sftpUtils.mkdir(remotePath);
            String zipPath = wordPathConfig.getFileZipPath().replace("$<YYYY>",year).replace("$<dateTime>", acctMonth);
            sftpUtils.uploadFile(remotePath+mailAccountConfig.getFileName().replace("$<dateTime>", acctMonth),zipPath);
        } catch (Exception e) {
            log.error("上传异常！",e);
            throw new RuntimeException(e);
        } finally {
            sftpUtils.close();
        }

    }

    /**
     * 结算说明数据
     *
     * @param acctMonth
     * @param dataPath
     */
    private void genSettleDataDesc(String acctMonth, String dataPath) throws Exception {
        XSSFWorkbook wb = new XSSFWorkbook();
        FileOutputStream fileOut = null;
        try {
            List<LogFileEntity> logFileEntities = logFileDao.queryLogFile(acctMonth,  acctMonth.substring(4, 6));
            String sheetName = "短彩信";
            XSSFSheet sheet = wb.createSheet(sheetName);
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            cell.setCellValue(acctMonth);
            for (int i = 0; i < logFileEntities.size(); i++) {
                LogFileEntity logFileEntity = logFileEntities.get(i);
                row = sheet.createRow(i+1);
                cell = row.createCell(0);
                cell.setCellValue(logFileEntity.getBizType());
                cell = row.createCell(1);
                cell.setCellValue(logFileEntity.getCount());
            }
            String sheetName1 = "出账费用明细";
            XSSFSheet sheet1 = wb.createSheet(sheetName1);
//            sheet1.setColumnWidth(0,255*255);
            String zqCmccBLSettleTotalData = settleDataDao.getZqCmccBLSettleTotalData(acctMonth);
            Row row1 = sheet1.createRow(0);
            Cell cell1 = row1.createCell(0);
            cell1.setCellValue(acctMonth);
            row1 = sheet1.createRow(1);
            cell1 = row1.createCell(0);
            cell1.setCellValue(zqCmccBLSettleTotalData);

            String sheetName2 = "短彩信错单";
            XSSFSheet sheet2 = wb.createSheet(sheetName2);
            String tableName = "stat_error_" + acctMonth + "_t";
            List<StatErrorInfo> statErrorInfos = statErrorDao.queryStatErrorCodeInfo(tableName);
            Row row2 = sheet2.createRow(0);
            Cell cell2 = row2.createCell(0);
            cell2.setCellValue(acctMonth);
            for (int i = 0; i < statErrorInfos.size(); i++) {
                StatErrorInfo statErrorInfo = statErrorInfos.get(i);
                row2 = sheet2.createRow(i+1);
                cell2 = row2.createCell(0);
                cell2.setCellValue(statErrorInfo.getBizType());
                cell2 = row2.createCell(1);
                cell2.setCellValue(statErrorInfo.getErrCode());
                cell2 = row2.createCell(2);
                cell2.setCellValue(statErrorInfo.getDescription());
                cell2 = row2.createCell(3);
                cell2.setCellValue(statErrorInfo.getCount());
            }

            String sheetName3 = "省公司主办集团客户业务结算单";
            XSSFSheet sheet3 = wb.createSheet(sheetName3);
            List<SettleDataProvince> d208ProvinceSettleData = settleDataDao.getD208ProvinceSettleData(acctMonth);
            Row row3 = sheet3.createRow(0);
            Cell cell3 = row3.createCell(0);
            cell3.setCellValue(acctMonth);
            for (int i = 0; i < d208ProvinceSettleData.size(); i++) {
                SettleDataProvince settleDataProvince = d208ProvinceSettleData.get(i);
                row3 = sheet3.createRow(i+1);
                cell3 = row3.createCell(0);
                cell3.setCellValue(settleDataProvince.getBusName());
                cell3 = row3.createCell(1);
                cell3.setCellValue(settleDataProvince.getSettleType());
                cell3 = row3.createCell(2);
                cell3.setCellValue(settleDataProvince.getTaxRate());
                cell3 = row3.createCell(3);
                cell3.setCellValue(settleDataProvince.getSettleOutFee());
                cell3 = row3.createCell(4);
                cell3.setCellValue(settleDataProvince.getSettleInFee());
                cell3 = row3.createCell(5);
                cell3.setCellValue(settleDataProvince.getSettleFee());
            }

            String sheetName4 = "政企、财务公司报表";
            XSSFSheet sheet4 = wb.createSheet(sheetName4);
            List<SettleDataProvince> zqCmccArBLSettleData = settleDataDao.getZqCmccArBLSettleData(acctMonth);
            Row row4 = sheet4.createRow(0);
            Cell cell4 = row4.createCell(0);
            cell4.setCellValue(acctMonth);
            setSheet4CellValue(null,zqCmccArBLSettleData, sheet4);
            List<SettleDataProvince> cwCmccArBLSettleData = settleDataDao.getCwCmccArBLSettleData(acctMonth);
            setSheet4CellValue(zqCmccArBLSettleData,cwCmccArBLSettleData, sheet4);
            // 将输出写入excel文件
            String filename = dataPath+"结算说明数据"+acctMonth+".xlsx";
            fileOut = new FileOutputStream(filename);
            wb.write(fileOut);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            wb.close();
            if (fileOut != null) {
                fileOut.close();
            }
        }
    }

    private void setSheet4CellValue(List<SettleDataProvince> zqData,List<SettleDataProvince> cwData, XSSFSheet sheet4) {
        Cell cell4;
        Row row4;
        Integer size=zqData==null ? 0 : zqData.size();
        for (int i = 0; i < cwData.size(); i++) {
            SettleDataProvince settleDataProvince = cwData.get(i);
            row4 = sheet4.createRow(size+1+i);
            cell4 = row4.createCell(0);
            cell4.setCellValue(settleDataProvince.getBusType());
            cell4 = row4.createCell(1);
            cell4.setCellValue(settleDataProvince.getSettleType());
            cell4 = row4.createCell(2);
            cell4.setCellValue(settleDataProvince.getBusName());
            cell4 = row4.createCell(3);
            cell4.setCellValue(settleDataProvince.getTaxRate());
            cell4 = row4.createCell(4);
            cell4.setCellValue(settleDataProvince.getDescription());
        }
    }

    /**
     * 数据图表
     *
     * @param acctMonth
     * @param dataPath
     * @throws IOException
     */
    public void genDataExcel(String acctMonth, String dataPath) throws IOException {
        log.info("生成数据图表");
        XSSFWorkbook wb = new XSSFWorkbook();
        FileOutputStream fileOut = null;
        try {
            this.genSheet1(wb,acctMonth);
            this.genSheet2(wb,acctMonth);
            this.genSheet3(wb,acctMonth);
            // 将输出写入excel文件
            String filename = dataPath +"数据图表.xlsx";
            fileOut = new FileOutputStream(filename);
            wb.write(fileOut);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            wb.close();
            if (fileOut != null) {
                fileOut.close();
            }
        }
    }

    private void genSheet3(XSSFWorkbook wb, String acctMonth) {
        List<SettleDataStatistics> siarCarsvs = settleDataDao.getSiarCarsvs(acctMonth);
        packageYearData(siarCarsvs,acctMonth);
        siarCarsvs.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        String sheetName = "车务通";
        XSSFSheet sheet = wb.createSheet(sheetName);
        genChart6(sheet,wb,siarCarsvs);
    }

    private void genChart6(XSSFSheet sheet, XSSFWorkbook wb, List<SettleDataStatistics> siarCarsvs) {
        CellStyle numberStyle = wb.createCellStyle();
        numberStyle.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
//        sheet.setColumnWidth((short)   4,   (short)   10000);//   单位
        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("结算账期");
        cell = row.createCell(1);
        cell.setCellValue("结算费用");
        for (int i = 0; i < siarCarsvs.size(); i++) {
            SettleDataStatistics settleDataStatistics = siarCarsvs.get(i);
            row = sheet.createRow(i+1);
            cell = row.createCell(0);
            cell.setCellValue(settleDataStatistics.getSettleMonth());
            cell = row.createCell(1);
            cell.setCellValue(settleDataStatistics.getSettleAmount()==null ? 0 : settleDataStatistics.getSettleAmount());
//            cell.setCellStyle(numberStyle);
        }

        // 创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        // 前四个默认0，[0,5]：从0列5行开始;[7,26]:到7列26行结束
        // 默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 4, 1, 14, 15);
        // 创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        // 标题
        chart.setTitleText("车务通结算费用（万）");
        // 标题覆盖
        chart.setTitleOverlay(false);

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        // 左Y轴和X轴交叉点在X轴0点位置
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(Double.parseDouble(decimalFormat.format(min.get().getSettleAmount())));
//        leftAxis.setMaximum(Double.parseDouble(decimalFormat.format(max.get().getSettleAmount())));
        //poi设置显示单位
        // leftAxis.setVisible(false);// 隐藏Y轴
//        leftAxis.setTitle("元");
        leftAxis.setMajorTickMark(AxisTickMark.CROSS);
        leftAxis.setMinorTickMark(AxisTickMark.NONE);
        // 图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);
        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(1, siarCarsvs.size()+1, 0, 0));
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(1, siarCarsvs.size()+1, 1, 1));
        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line1.addSeries(xdatas, leftYdatas);
        // 折线图例标题
//        series2.setTitle("结算费用（亿元）", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);
    }

    private void genSheet2(XSSFWorkbook wb, String acctMonth) {
        List<SettleDataStatistics> settleBLData = settleDataDao.getSettleBLData(acctMonth);
        List<SettleDataStatistics> settleARData = settleDataDao.getSettleArData(acctMonth);
        packageYearData(settleBLData,acctMonth);
        settleBLData.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        packageYearData(settleARData,acctMonth);
        settleARData.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        String sheetName = "政企结算费用、业务量";
        XSSFSheet sheet = wb.createSheet(sheetName);
        genChart3(sheet,wb,settleBLData);
        int chart4Row = genChart4(sheet, wb, settleARData, settleBLData);
        List<SettleDataStatistics> billList = billListDao.queryBillList(acctMonth);
        packageYearData(billList,acctMonth);
        billList.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        genChart5(sheet,wb,billList,chart4Row);
    }

    private void genChart5(XSSFSheet sheet, XSSFWorkbook wb, List<SettleDataStatistics> dataStatistics, int chart4Row) {

        int rownum = chart4Row + 2;
        CellStyle numberStyle = wb.createCellStyle();
        numberStyle.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
//        sheet.setColumnWidth((short)   4,   (short)   10000);//   单位
        Row row = sheet.createRow(rownum);
        Cell cell = row.createCell(0);
        cell.setCellValue("计费账期");
        cell = row.createCell(1);
        cell.setCellValue("计费话单量");
        for (int i = 0; i < dataStatistics.size(); i++) {
            SettleDataStatistics settleDataStatistics = dataStatistics.get(i);
            row = sheet.createRow(i + 1 + rownum);
            cell = row.createCell(0);
            cell.setCellValue(settleDataStatistics.getSettleMonth());
            cell = row.createCell(1);
            cell.setCellValue(settleDataStatistics.getSettleCount()==null ? 0 : settleDataStatistics.getSettleCount());
//            cell.setCellStyle(numberStyle);
        }

        // 创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        // 前四个默认0，[0,5]：从0列5行开始;[7,26]:到7列26行结束
        // 默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 4, rownum, 14, rownum + 15);
        // 创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        // 标题
        chart.setTitleText("计费话单量（亿条）");
        // 标题覆盖
        chart.setTitleOverlay(false);

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        // 左Y轴和X轴交叉点在X轴0点位置
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(Double.parseDouble(decimalFormat.format(min.get().getSettleAmount())));
//        leftAxis.setMaximum(Double.parseDouble(decimalFormat.format(max.get().getSettleAmount())));
        //poi设置显示单位
        // leftAxis.setVisible(false);// 隐藏Y轴
        leftAxis.setTitle("元");
        leftAxis.setMajorTickMark(AxisTickMark.CROSS);
        leftAxis.setMinorTickMark(AxisTickMark.NONE);
        // 图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);
        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(rownum + 1, rownum + dataStatistics.size(), 0, 0));
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(rownum + 1, rownum + dataStatistics.size(), 1, 1));
        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line1.addSeries(xdatas, leftYdatas);
        // 折线图例标题
//        series2.setTitle("结算费用（亿元）", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);
    }

    private int genChart4(XSSFSheet sheet, XSSFWorkbook wb, List<SettleDataStatistics> settleARData, List<SettleDataStatistics> settleBLData) {
        int size = settleBLData.size();
        CellStyle numberStyle = wb.createCellStyle();
        numberStyle.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
//        sheet.setColumnWidth((short)   4,   (short)   10000);//   单位
        int rownum = size + 2;
        int countRow=0;

        Row row = sheet.createRow(rownum);
        Cell cell = row.createCell(0);
        cell.setCellValue("结算账期");
        cell = row.createCell(1);
        cell.setCellValue("结算费用");
        for (int i = 0; i < settleARData.size(); i++) {
            SettleDataStatistics settleDataStatistics = settleARData.get(i);
            countRow=i+rownum+1;
            row = sheet.createRow(i+rownum+1);
            cell = row.createCell(0);
            cell.setCellValue(settleDataStatistics.getSettleMonth());
            cell = row.createCell(1);
            cell.setCellValue(settleDataStatistics.getSettleAmount());
//            cell.setCellStyle(numberStyle);
        }

        // 创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        // 前四个默认0，[0,5]：从0列5行开始;[7,26]:到7列26行结束
        // 默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 4, rownum, 14, settleARData.size()+rownum+1);
        // 创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        // 标题
        chart.setTitleText("实收收结算费用（亿元）");
        // 标题覆盖
        chart.setTitleOverlay(false);

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        // 左Y轴和X轴交叉点在X轴0点位置
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(Double.parseDouble(decimalFormat.format(min.get().getSettleAmount())));
//        leftAxis.setMaximum(Double.parseDouble(decimalFormat.format(max.get().getSettleAmount())));
        //poi设置显示单位
        // leftAxis.setVisible(false);// 隐藏Y轴
        leftAxis.setTitle("元");
        leftAxis.setMajorTickMark(AxisTickMark.CROSS);
        leftAxis.setMinorTickMark(AxisTickMark.NONE);
        // 图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);
        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(rownum+1, rownum+settleARData.size(), 0, 0));
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(rownum+1, rownum+settleARData.size(), 1, 1));
        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line1.addSeries(xdatas, leftYdatas);
        // 折线图例标题
//        series2.setTitle("结算费用（亿元）", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);

        return countRow;
    }

    private void genChart3(XSSFSheet sheet,XSSFWorkbook wb, List<SettleDataStatistics> provinceDedicatedLineData) {
        CellStyle numberStyle = wb.createCellStyle();
        numberStyle.setDataFormat(wb.createDataFormat().getFormat("#,##0.00"));
//        sheet.setColumnWidth((short)   4,   (short)   10000);//   单位
        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("结算账期");
        cell = row.createCell(1);
        cell.setCellValue("结算费用");
        for (int i = 0; i < provinceDedicatedLineData.size(); i++) {
            SettleDataStatistics settleDataStatistics = provinceDedicatedLineData.get(i);
            row = sheet.createRow(i+1);
            cell = row.createCell(0);
            cell.setCellValue(settleDataStatistics.getSettleMonth());
            cell = row.createCell(1);
            cell.setCellValue(settleDataStatistics.getSettleAmount()==null ? 0 : settleDataStatistics.getSettleAmount());
//            cell.setCellStyle(numberStyle);
        }

        // 创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        // 前四个默认0，[0,5]：从0列5行开始;[7,26]:到7列26行结束
        // 默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 4, 1, 14, provinceDedicatedLineData.size());
        // 创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        // 标题
        chart.setTitleText("应收结算费用（亿元）");
        // 标题覆盖
        chart.setTitleOverlay(false);

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        // 左Y轴和X轴交叉点在X轴0点位置
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(Double.parseDouble(decimalFormat.format(min.get().getSettleAmount())));
//        leftAxis.setMaximum(Double.parseDouble(decimalFormat.format(max.get().getSettleAmount())));
        //poi设置显示单位
        // leftAxis.setVisible(false);// 隐藏Y轴
//        leftAxis.setTitle("元");
        leftAxis.setMajorTickMark(AxisTickMark.CROSS);
        leftAxis.setMinorTickMark(AxisTickMark.NONE);
        // 图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);
        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(1, provinceDedicatedLineData.size(), 0, 0));
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(1, provinceDedicatedLineData.size(), 1, 1));
        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line1.addSeries(xdatas, leftYdatas);
        // 折线图例标题
//        series2.setTitle("结算费用（亿元）", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);
    }

    private void genSheet1(XSSFWorkbook wb,String acctMonth) {
        List<SettleDataStatistics> provinceDedicatedLineData = settleDataDao.getProvinceDedicatedLineData(acctMonth);
        packageYearData(provinceDedicatedLineData,acctMonth);
        provinceDedicatedLineData.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        List<SettleDataStatistics> provincePayData = settleDataDao.getProvincePayData(acctMonth);
        packageYearData(provincePayData,acctMonth);
        provincePayData.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        String sheetName = "省公司主办专线、代付类";
        XSSFSheet sheet = wb.createSheet(sheetName);
        genChart1(sheet, provinceDedicatedLineData);
        genChart2(sheet, provinceDedicatedLineData,provincePayData);
    }

    private void packageYearData(List<SettleDataStatistics> provinceData,String acctMonth) {
        List<String> yearMonthList = getYearMonth(provinceData,acctMonth);
        if (CollectionUtil.isNotEmpty(yearMonthList)){
            for (String yearMonth : yearMonthList) {
                SettleDataStatistics settleDataStatistics = new SettleDataStatistics();
                settleDataStatistics.setSettleCount(0L);
                settleDataStatistics.setSettleMonth(yearMonth);
                settleDataStatistics.setSettleAmount(0d);
                provinceData.add(settleDataStatistics);
            }
        }
    }


    private void genChart2(XSSFSheet sheet, List<SettleDataStatistics> provinceDedicatedLineData, List<SettleDataStatistics> provincePayData) {
        int size = provinceDedicatedLineData.size();
        Row row = sheet.createRow(size +2);
        Cell cell = row.createCell(0);
        cell.setCellValue("结算账期");
        cell = row.createCell(1);
        cell.setCellValue("条数");
        cell = row.createCell(2);
        cell.setCellValue("结算金额");
        for (int i = 0; i < provincePayData.size(); i++) {
            SettleDataStatistics settleDataStatistics = provincePayData.get(i);
            row = sheet.createRow(size+3+i);
            cell = row.createCell(0);
            cell.setCellValue(settleDataStatistics.getSettleMonth());
            cell = row.createCell(1);
            cell.setCellValue(settleDataStatistics.getSettleCount()==null ? 0 : settleDataStatistics.getSettleCount() );
            cell = row.createCell(2);
            cell.setCellValue(settleDataStatistics.getSettleAmount()==null ? 0 : settleDataStatistics.getSettleAmount());
        }

        // 创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        // 前四个默认0，[0,5]：从0列5行开始;[7,26]:到7列26行结束
        // 默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 5, 19, 16, 27);
        // 创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        // 标题
        chart.setTitleText("省公司主办代付类结算金额");
        // 标题覆盖
        chart.setTitleOverlay(false);

        /*双Y轴*/

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        // 左Y轴和X轴交叉点在X轴0点位置
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provincePayData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provincePayData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(Double.parseDouble(decimalFormat.format(min.get().getSettleAmount())));
//        leftAxis.setMaximum(Double.parseDouble(decimalFormat.format(max.get().getSettleAmount())));
//                leftAxis.setMajorUnit(1);
        // leftAxis.setVisible(false);// 隐藏Y轴
        leftAxis.setTitle("元");

        // 右Y轴
        XDDFValueAxis rightAxis = chart.createValueAxis(AxisPosition.RIGHT);
        // 右Y轴和X轴交叉点在X轴最大值位置
        rightAxis.setCrosses(AxisCrosses.MAX);
        rightAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        rightAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(rightAxis);
//        Optional<SettleDataStatistics> rightmin = provincePayData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleCount));
//        Optional<SettleDataStatistics> rightmax = provincePayData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleCount));
//        rightAxis.setMinimum(rightmin.get().getSettleCount());
//        rightAxis.setMaximum(rightmax.get().getSettleCount());
        // rightAxis.setVisible(false);// 隐藏Y轴
//        rightAxis.setTitle("右Y轴标题");

        // 图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);

        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
        int size1 = provincePayData.size();
        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(size +3, size+size1+2, 0, 0));
        // XDDFCategoryDataSource xdatas = XDDFDataSourcesFactory.fromArray(new String[] {"俄罗斯","加拿大","美国","中国","巴西","澳大利亚","印度"});
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(size +3, size+size1+2, 2, 2));
//                 XDDFNumericalDataSource<Integer> leftYdatas = XDDFDataSourcesFactory.fromArray(new Integer[] {17098242,9984670});
        // 右Y轴数据，单元格范围位置[2, 0]到[2, 6]
        XDDFNumericalDataSource<Double> rightYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(size +3, size+size1+2, 1, 1));
        // XDDFNumericalDataSource<Integer> rightYdatas = XDDFDataSourcesFactory.fromArray(new Integer[] {17098242,9984670,9826675,9596961,8514877,7741220,3287263});

        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) line1.addSeries(xdatas, rightYdatas);
        // 折线图例标题
        series1.setTitle("条数", null);
        // 线条样式:true平滑曲线,false折线
        series1.setSmooth(false);
        // 设置标记大小
        series1.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series1.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // LINE：折线图，
        XDDFLineChartData line2 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line2.addSeries(xdatas, leftYdatas);
        // 折线图例标题
        series2.setTitle("结算金额", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line2);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);
    }

    private void genChart1(XSSFSheet sheet, List<SettleDataStatistics> provinceDedicatedLineData) {

        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("结算账期");
        cell = row.createCell(1);
        cell.setCellValue("条数");
        cell = row.createCell(2);
        cell.setCellValue("结算金额");
        for (int i = 0; i < provinceDedicatedLineData.size(); i++) {
            SettleDataStatistics settleDataStatistics = provinceDedicatedLineData.get(i);
            row = sheet.createRow(i+1);
            cell = row.createCell(0);
            cell.setCellValue(settleDataStatistics.getSettleMonth());
            cell = row.createCell(1);
            cell.setCellValue(settleDataStatistics.getSettleCount()==null ? 0 : settleDataStatistics.getSettleCount());
            cell = row.createCell(2);
            cell.setCellValue(settleDataStatistics.getSettleAmount()==null ? 0 : settleDataStatistics.getSettleAmount());
        }

        // 创建一个画布
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        // 前四个默认0，[0,5]：从0列5行开始;[7,26]:到7列26行结束
        // 默认宽度(14-8)*12
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 5, 1, 16, 17);
        // 创建一个chart对象
        XSSFChart chart = drawing.createChart(anchor);
        // 标题
        chart.setTitleText("省公司主办专线业务增长图");
        // 标题覆盖
        chart.setTitleOverlay(false);

        /*双Y轴*/

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
        // 左Y轴和X轴交叉点在X轴0点位置
        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(Double.parseDouble(decimalFormat.format(min.get().getSettleAmount())));
//        leftAxis.setMaximum(Double.parseDouble(decimalFormat.format(max.get().getSettleAmount())));
//                leftAxis.setMajorUnit(1);
        // leftAxis.setVisible(false);// 隐藏Y轴
        leftAxis.setTitle("元");

        // 右Y轴
        XDDFValueAxis rightAxis = chart.createValueAxis(AxisPosition.RIGHT);
        // 右Y轴和X轴交叉点在X轴最大值位置
        rightAxis.setCrosses(AxisCrosses.MAX);
        rightAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        rightAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(rightAxis);
//        Optional<SettleDataStatistics> rightmin = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleCount));
//        Optional<SettleDataStatistics> rightmax = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleCount));
//        rightAxis.setMinimum(rightmin.get().getSettleCount());
//        rightAxis.setMaximum(rightmax.get().getSettleCount());
        // rightAxis.setVisible(false);// 隐藏Y轴
        rightAxis.setMajorUnit(1000);
        rightAxis.setTitle("条数");

        // 图例位置
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.TOP);

        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(1, provinceDedicatedLineData.size(), 0, 0));
        // XDDFCategoryDataSource xdatas = XDDFDataSourcesFactory.fromArray(new String[] {"俄罗斯","加拿大","美国","中国","巴西","澳大利亚","印度"});
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(1, provinceDedicatedLineData.size(), 2, 2));
//                 XDDFNumericalDataSource<Integer> leftYdatas = XDDFDataSourcesFactory.fromArray(new Integer[] {17098242,9984670});
        // 右Y轴数据，单元格范围位置[2, 0]到[2, 6]
        XDDFNumericalDataSource<Double> rightYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(1, provinceDedicatedLineData.size(), 1, 1));
        // XDDFNumericalDataSource<Integer> rightYdatas = XDDFDataSourcesFactory.fromArray(new Integer[] {17098242,9984670,9826675,9596961,8514877,7741220,3287263});
        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) line1.addSeries(xdatas, rightYdatas);
        // 折线图例标题
        series1.setTitle("条数", null);
        // 线条样式:true平滑曲线,false折线
        series1.setSmooth(false);
        // 设置标记大小
        series1.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series1.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // LINE：折线图，
        XDDFLineChartData line2 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line2.addSeries(xdatas, leftYdatas);
        // 折线图例标题
        series2.setTitle("结算金额", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line2);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);
    }

    private static List<String> getYearMonth(List<SettleDataStatistics> provinceDedicatedLineData,String acctMonth) {
        List<String> monthList = provinceDedicatedLineData.stream().map(s -> s.getSettleMonth()).collect(Collectors.toList());
        if (monthList.size()>=12){
            return ListUtil.empty();
        }
        List<String> yearMonthList = DateTimeUtil.getYearMonthList(acctMonth);
        Iterator<String> iterator = yearMonthList.iterator();
        while(iterator.hasNext()){
            String next = iterator.next();
            if (monthList.contains(next)){
                iterator.remove();
            }
        }
        return yearMonthList;
    }


    private void zipFile(String acctMonth) {
        try {
            log.info("开始压缩文件！");
            String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
            String baseDataPath = wordPathConfig.getDataPath().replace("$<YYYY>",year).replace("$<dateTime>",acctMonth);
            String fileZipPath = wordPathConfig.getFileZipPath().replace("$<YYYY>",year).replace("$<dateTime>",acctMonth);
            ZipUtil.zip(baseDataPath, fileZipPath);
            log.info("压缩文件成功！压缩成功目录：{}",fileZipPath);
        } catch (UtilException e) {
            log.error("压缩文件异常：{}",e.getMessage(), e);
            throw new RuntimeException(e);
        }

    }

    private void sendEmail(String acctMonth) {
        log.info("开始发送邮件！");
        String subject = acctMonth+"结算数据说明"; // 邮件主题
        String content = acctMonth+"结算数据说明"; // 邮件正文
        //发送邮件
        MailInfoVo mailInfoVo=new MailInfoVo();
        mailInfoVo.setContent(content);
        //运维账号发送
        mailInfoVo.setClientId(Integer.parseInt(mailAccountConfig.getClientId()));
        String toEmail = mailAccountConfig.getToEmail();
        mailInfoVo.setRecipients(Splitter.on(",").splitToList(toEmail));
        mailInfoVo.setSubject(subject);
        mailInfoVo.setHaveNotTemple(true);
        MailAttachFile mailAttachFile = new MailAttachFile();
        String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
        String filePath = mailAccountConfig.getFilePath().replace("$<YYYY>", year);
        String fileName = mailAccountConfig.getFileName().replace("$<dateTime>",acctMonth);
        mailAttachFile.setFilePath(filePath);
        mailAttachFile.setFileName(fileName);
        mailInfoVo.setFiles(ListUtil.toList(mailAttachFile));
        httpClientUtils.sendJSONDataByPost(mailAccountConfig.getRemoteUrl(),JSONUtil.toJsonStr(mailInfoVo));
    }

    /**
     * 生成财务公司下D202报表和政企分公司下报表
     * @param acctMonth
     * @throws IOException
     */
    private void genCwD202WordAndZQWord(String acctMonth) throws IOException {
        log.info("create D202word report");
        //封装map数据
        Map<String, Object> dataMap = packDataMap(acctMonth);
        //绑定策略
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        ConfigureBuilder report = Configure.builder()
                .bind("smsErrorInfos", policy)
                .bind("mmsErrorInfos", policy)
                .bind("attachment", new AttachmentRenderPolicy());
        // Prepare charts
        ChartMultiSeriesRenderData blDataReport = prepareChart("应收结算费用（亿元）", settleDataDao.getSettleBLData(acctMonth));
        ChartMultiSeriesRenderData arDataReport = prepareChart("实收结算费用（亿元）", settleDataDao.getSettleArData(acctMonth));

        List<SettleDataProvince> cwCmccArBLSettleData = settleDataDao.getCwCmccArBLSettleData(acctMonth);
        //生成D202文档
        generateReports(cwCmccArBLSettleData, dataMap, report, blDataReport, arDataReport, "cw", "D202",acctMonth);

        List<SettleDataProvince> zqCmccArBLSettleData = settleDataDao.getZqCmccArBLSettleData(acctMonth);
        //生成政企分公司文档
        generateReports(zqCmccArBLSettleData, dataMap, report, blDataReport, arDataReport, "zq", "",acctMonth);
    }

    /**
     * 生成文档
     * @param settleData
     * @param dataMap
     * @param report
     * @param blDataReport
     * @param arDataReport
     * @param templatePrefix
     * @param wordNamePrefix
     * @param acctMonth
     * @throws IOException
     */
    private void generateReports(List<SettleDataProvince> settleData, Map<String, Object> dataMap, ConfigureBuilder report,
                                 ChartMultiSeriesRenderData blDataReport, ChartMultiSeriesRenderData arDataReport,
                                 String templatePrefix, String wordNamePrefix,String acctMonth) throws IOException {
        for (SettleDataProvince settleDatum : settleData) {
            dataMap.put("dataSource", settleDatum.getDescription());
            dataMap.put("busName", settleDatum.getBusName());
            String settleType = settleDatum.getSettleType();
            Resource resource;
            String newFilePath;

            if (StrUtil.contains(settleType, "调账")) {
                resource = new ClassPathResource("templates/" + templatePrefix +"/"+wordNamePrefix+ "AdjustSettleDataTemplate.docx");
                newFilePath = getNewFilePath(settleDatum, acctMonth, true, templatePrefix, wordNamePrefix);
            } else {
                dataMap.put("settleType", settleType.contains("应收") ? "应收" : "实收");
                dataMap.put("settleDataReport", settleType.contains("应收") ? blDataReport : arDataReport);
                resource = new ClassPathResource("templates/" + templatePrefix +"/"+wordNamePrefix+ "SettleDataTemplate.docx");
                newFilePath = getNewFilePath(settleDatum, acctMonth, false, templatePrefix, wordNamePrefix);
            }

            XWPFTemplate
                    .compile(resource.getInputStream(), report.build())
                    .render(dataMap).writeToFile(newFilePath);
        }
    }
    private String getNewFilePath(SettleDataProvince settleDatum, String acctMonth, boolean isAdjust, String templatePrefix, String wordNamePrefix) {
        if (templatePrefix.equals("cw")) {
            return this.getCWNewFilePath(settleDatum, acctMonth, isAdjust, wordNamePrefix);
        } else {
            return this.getZQNewFilePath(settleDatum, acctMonth, isAdjust, wordNamePrefix);
        }
    }
    private ChartMultiSeriesRenderData prepareChart(String title, List<SettleDataStatistics> data) {
        data.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        return Charts
                .ofMultiSeries(title, data.stream().map(SettleDataStatistics::getSettleMonth).toArray(String[]::new))
                .addSeries(title.replace("（亿元）", ""), data.stream().map(SettleDataStatistics::getSettleAmount).toArray(Double[]::new))
                .create();
    }
    private Map<String, Object> packDataMap(String acctMonth) {
        List<LogFileEntity> logFileEntities = logFileDao.queryLogFile(acctMonth,acctMonth.substring(4,6));
        HashMap<String, Object> map = new HashMap<>();
        map.put("smsCount", logFileEntities.stream().filter(s->s.getBizType().equalsIgnoreCase("SMS")).findFirst().orElse(new LogFileEntity()).getCount());
        map.put("mmsCount", logFileEntities.stream().filter(s->s.getBizType().equalsIgnoreCase("MMS")).findFirst().orElse(new LogFileEntity()).getCount());
        String zqCmccBLSettleTotalData = settleDataDao.getZqCmccBLSettleTotalData(acctMonth);
        map.put("totalData",zqCmccBLSettleTotalData);
        String tableName = "stat_error_" + acctMonth + "_t";
        List<StatErrorInfo> statErrorInfos = statErrorDao.queryStatErrorCodeInfo(tableName);
        map.put("smsErrorInfos",statErrorInfos.stream().filter(s->s.getBizType().equalsIgnoreCase("SMS")).collect(Collectors.toList()));
        map.put("mmsErrorInfos",statErrorInfos.stream().filter(s->s.getBizType().equalsIgnoreCase("MMS")).collect(Collectors.toList()));

        List<SettleDataStatistics> billList = billListDao.queryBillList(acctMonth);
        packageYearData(billList,acctMonth);
        billList.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        String[] billMonth = billList.stream().map(SettleDataStatistics::getSettleMonth).sorted().toArray(String[]::new);
        Number[] billCount = billList.stream().map(s-> s.getSettleCount()).toArray(Number[]::new);
        ChartMultiSeriesRenderData billDataReport = Charts
                .ofMultiSeries("计费话单量(亿条)", billMonth)
                .addSeries("计费话单量(亿条)", billCount)
                .setxAsixTitle("月")
                .create();
        map.put("billDataReport", billDataReport);
        String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
        File[] ls = FileUtil.ls(wordPathConfig.getUploadPath().replace("$<YYYY>",year).replace("$<dateTime>",acctMonth));
        AttachmentRenderData attach = Attachments.ofLocal(FileUtil.getAbsolutePath(ls[0]), AttachmentType.DOCX).create();
        map.put("attachment", attach);

        return map;
    }

    /**
     * 生成财务公司下D208报表和V101
     * @param acctMonth
     * @throws IOException
     */
    private void genCwD208wordAndV101(String acctMonth) throws IOException {
        log.info("create D208word report");
        List<SettleDataStatistics> provinceDedicatedLineData = settleDataDao.getProvinceDedicatedLineData(acctMonth);
        List<SettleDataProvince> d208ProvinceSettleData = settleDataDao.getD208ProvinceSettleData(acctMonth);
        ConfigureBuilder report = Configure.builder()
                .bind("report", customChartRenderPolicy);
        packageYearData(provinceDedicatedLineData,acctMonth);
        provinceDedicatedLineData.sort(Comparator.comparing(SettleDataStatistics::getSettleMonth));
        HashMap<Object, Object> map = new HashMap<>();
        map.put("report", provinceDedicatedLineData);
        for (SettleDataProvince dataProvince : d208ProvinceSettleData) {
            map.put("settleFee", dataProvince.getSettleFee());
            String settleType = dataProvince.getSettleType();
            Resource resource = new ClassPathResource("templates/cw/D208SettleDataTemplate.docx");
            String wordNamePrefix="D208";
            String cwNewFilePath = this.getCWNewFilePath(dataProvince, acctMonth, false,wordNamePrefix);
            if (StrUtil.contains(settleType, "调账")) {
                resource = new ClassPathResource("templates/cw/D208AdjSettleDataTemplate.docx");
                //todo 获取上传的文件
                String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
                File[] ls = FileUtil.ls(wordPathConfig.getUploadPath().replace("$<YYYY>",year).replace("$<dateTime>",acctMonth));
                AttachmentRenderData attach = Attachments.ofLocal(FileUtil.getAbsolutePath(ls[0]), AttachmentType.DOCX).create();
                map.put("attachment", attach);
                report.bind("attachment", new AttachmentRenderPolicy());
                cwNewFilePath = this.getCWNewFilePath(dataProvince, acctMonth, true,wordNamePrefix);
            }
            XWPFTemplate
                    .compile(resource.getInputStream(), report.build())
                    .render(map).writeToFile(cwNewFilePath);

        }
        log.info("create V101word report");
        List<SettleDataProvince> v101ProvinceSettleData = settleDataDao.getV101ProvinceSettleData(acctMonth);
        for (SettleDataProvince dataProvince : v101ProvinceSettleData){
            map.put("settleFee", dataProvince.getSettleFee());
            String settleType = dataProvince.getSettleType();
            Resource resource = new ClassPathResource("templates/cw/D208SettleDataTemplate.docx");
            //生成v101报表
            if (!StrUtil.contains(settleType, "调账")){
                String v101FilePath =this.getV101FilePath(acctMonth,dataProvince);
                XWPFTemplate
                        .compile(resource.getInputStream(), report.build())
                        .render(map).writeToFile(v101FilePath);
            }
        }

    }

    private String getV101FilePath(String acctMonth, SettleDataProvince dataProvince) {
        String taxRate = dataProvince.getTaxRate();
        String year = DateUtil.format(DateUtil.parse(acctMonth,"yyyyMM"), "yyyy");
        String baseDataPath = wordPathConfig.getBaseDataPath().replace("$<YYYY>",year).replace("$<dateTime>", acctMonth);
        String namePrefix="V101_省公司主办集团客户业务结算单（省间直接结算）";
        return baseDataPath+namePrefix+"("+taxRate+"%)"+"_"+acctMonth+".doc";

    }

    /**
     * 获取财务公司报表目录
     * @param dataProvince
     * @param acctMonth
     * @return
     */
    private String getCWNewFilePath(SettleDataProvince dataProvince, String acctMonth,Boolean isAdjust,String wordNamePrefix) {
        String busName = dataProvince.getBusName();
        String taxRate = dataProvince.getTaxRate();
        String settleType = dataProvince.getSettleType();

        String cwDataPath = wordPathConfig.getCwDataPath();
        String month;
        String year;
        if (StringUtils.isNotEmpty(acctMonth)){
            month=acctMonth;
            year = DateUtil.format(DateUtil.parse(month,"yyyyMM"), "yyyy");
            cwDataPath = cwDataPath.replace("$<YYYY>",year).replace("$<dateTime>",acctMonth);
        }else {
            month = DateTimeUtil.today();
            year = DateUtil.format(DateUtil.parse(month,"yyyyMM"), "yyyy");
            cwDataPath =  cwDataPath.replace("$<YYYY>",year).replace("$<dateTime>",month);
        }
        FileUtil.mkdir(cwDataPath);
        String newFilePath;
        if (isAdjust){
            //去掉settleType中的调账
            settleType = settleType.replace("调账","");
            newFilePath= cwDataPath+wordNamePrefix+"_"+busName+settleType+"结算单"+"_调账_"+"("+taxRate+"%)"+"_"+month+".doc";
        }else {
            newFilePath=cwDataPath+wordNamePrefix+"_"+busName+settleType+"结算单"+"("+taxRate+"%)"+"_"+month+".doc";
        }

        return newFilePath;
    }
    /**
     * 获取政企公司报表目录
     * @param dataProvince
     * @param acctMonth
     * @return
     */
    private String getZQNewFilePath(SettleDataProvince dataProvince, String acctMonth,Boolean isAdjust,String wordNamePrefix) {
        String busName = dataProvince.getBusName();
        String taxRate = dataProvince.getTaxRate();
        String settleType = dataProvince.getSettleType();
        String zqDataPath = wordPathConfig.getZqDataPath();
        String month;
        String year;
        if (StringUtils.isNotEmpty(acctMonth)){
            month=acctMonth;
            year = DateUtil.format(DateUtil.parse(month,"yyyyMM"), "yyyy");
            zqDataPath = zqDataPath.replace("$<YYYY>",year).replace("$<dateTime>",acctMonth);
        }else {
            month = DateTimeUtil.today();
            year = DateUtil.format(DateUtil.parse(month,"yyyyMM"), "yyyy");
            zqDataPath =  zqDataPath.replace("$<YYYY>",year).replace("$<dateTime>",month);
        }
        FileUtil.mkdir(zqDataPath);
        String newFilePath;
        if (isAdjust){
            //去掉settleType中的调账
            settleType = settleType.replace("调账","");
            newFilePath= zqDataPath+settleType+"_调账_"+"("+taxRate+"%税率)"+"_"+busName+"_"+month+".doc";
        }else {
            newFilePath=zqDataPath+settleType+"("+taxRate+"%税率)"+"_"+busName+"_"+month+".doc";
        }

        return newFilePath;
    }

    public static void genWord1(String acctmonth) throws IOException {
        log.info("create word report");

        List<SettleDataProvince> d208ProvinceSettleData = new ArrayList<>();
        SettleDataProvince settleDataProvince = new SettleDataProvince();
        settleDataProvince.setSettleFee("结出减结入：1000元");
        settleDataProvince.setSettleType("月结");
        settleDataProvince.setTaxRate("税率：10%");
        settleDataProvince.setBusName("D208");

        d208ProvinceSettleData.add(settleDataProvince);
        for (SettleDataProvince dataProvince : d208ProvinceSettleData) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("settleFee", dataProvince.getSettleFee());
            Configure configure = Configure.builder()
                    .bind("report",new CustomChartRenderPolicy())
                    .build();
            map.put("report", dataProvince.getSettleFee());
            //            Resource resource = new ClassPathResource("templates/D208SettleDataTemplate.docx");
            XWPFTemplate template = XWPFTemplate
                    .compile("D:\\IdeaProjects\\settle\\settle-service-tools\\src\\main\\resources\\templates\\D208SettleDataTemplate.docx",configure)
                    .render(map);
//            File file = SensitiveUtil.getTempFilePath(sensitiveFile,"report");
            String busName = dataProvince.getBusName();
            String settleType = dataProvince.getSettleType();
            String taxRate = dataProvince.getTaxRate();
            String lastMonth = DateTimeUtil.lastMonth();
            // 新文件名称
            String newFileName = "D208_"+busName+settleType+"结算单"+"("+taxRate+"%)"+"_"+lastMonth+".docx";
            FileOutputStream outputStream = new FileOutputStream(newFileName);
            template.writeAndClose(outputStream);
        }


    }

    @Override
    public void send2Email(String acctMonth) {
        this.sendEmail(acctMonth);
    }


    public static void main(String[] args) throws IOException {
        genWord1("2");
    }


}
