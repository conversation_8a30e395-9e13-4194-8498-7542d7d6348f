<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="BillList">
		<xs:annotation>
			<xs:documentation>Comment describing your root element</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="ProvCode">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="3"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="PayTag">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="ECList">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ECInfo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="CustomerProvinceNumber">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
													<xs:minLength value="1"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="CustomerName">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="256"/>
													<xs:minLength value="1"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="BizList">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="BizInfo" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="ProductSubsList">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="ProductInfo" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="ProductSubsID">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductID">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductName">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="BillingTerm">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="6"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="PayTerm" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="6"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="FeeList" minOccurs="0">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="FeeInfo" maxOccurs="unbounded">
																										<xs:complexType>
																											<xs:sequence>
																												<xs:element name="ProdChargeCode">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="POChargeCodeName">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="256"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeVal">
																													<xs:simpleType>
																														<xs:restriction base="xs:int">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="TaxRate" type="xs:float"/>
																												<xs:element name="Tax">
																													<xs:simpleType>
																														<xs:restriction base="xs:int">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeNoTax">
																													<xs:simpleType>
																														<xs:restriction base="xs:int">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeFlag" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:int">
																															<xs:totalDigits value="1"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="DiscountAmount">
																													<xs:simpleType>
																														<xs:restriction base="xs:int">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="Reserved0" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="Reserved1" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="Reserved2" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="50"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																											</xs:sequence>
																										</xs:complexType>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																						<xs:element name="SubProductInfo" minOccurs="0" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="ProductSubsID">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ProductID">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ProductName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="DbProductID">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="DbProductName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="CoProductId" minOccurs="0">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="0"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="BlProductID" minOccurs="0">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="0"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="OneProductID">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="BillingTerm">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:length value="6"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="PayTerm" minOccurs="0">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:length value="6"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="FeeList" minOccurs="0">
																										<xs:complexType>
																											<xs:sequence>
																												<xs:element name="FeeInfo" maxOccurs="unbounded">
																													<xs:complexType>
																														<xs:sequence>
																															<xs:element name="ProdChargeCode">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="1"/>
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="PoChargeCodeName">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="1"/>
																																		<xs:maxLength value="256"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="OneProChargeCode">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="1"/>
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="OneProChargeName">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="1"/>
																																		<xs:maxLength value="256"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="DbProdChargeCode">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:maxLength value="20"/>
																																		<xs:minLength value="1"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="FeeVal">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="TaxRate" type="xs:float"/>
																															<xs:element name="Tax">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="FeeNoTax">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="FeeFlag" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:int">
																																		<xs:totalDigits value="1"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="DiscountAmount">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="StandardFee">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="SettleFee">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="Province2CloudSettle" minOccurs="0">
																																<xs:complexType>
																																	<xs:sequence>
																																		<xs:element name="Province2CloudSettleinfo" minOccurs="0" maxOccurs="unbounded">
																																			<xs:complexType>
																																				<xs:sequence>
																																					<xs:element name="SettlementPartyIn">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementPartyOut">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementRate" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:maxLength value="10"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementType" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="10"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementAmount">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:integer">
																																								<xs:totalDigits value="16"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="PrdSettleDisvalue" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																				</xs:sequence>
																																			</xs:complexType>
																																		</xs:element>
																																	</xs:sequence>
																																</xs:complexType>
																															</xs:element>
																															<xs:element name="Province2ProvinceSettle" minOccurs="0">
																																<xs:complexType>
																																	<xs:sequence>
																																		<xs:element name="Province2ProvinceSettleinfo" minOccurs="0" maxOccurs="unbounded">
																																			<xs:complexType>
																																				<xs:sequence>
																																					<xs:element name="SettlementPartyIn">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementPartyOut">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementRate" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="10"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementType" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="10"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementAmount">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:integer">
																																								<xs:totalDigits value="16"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																				</xs:sequence>
																																			</xs:complexType>
																																		</xs:element>
																																	</xs:sequence>
																																</xs:complexType>
																															</xs:element>
																															<xs:element name="GhFeeType">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="1"/>
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="PartnerCode" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="50"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="PartnerName" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="256"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="ParSettleRate" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="10"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="SettlementType" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="1"/>
																																		<xs:maxLength value="10"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="ParSettlAmount" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:integer">
																																		<xs:totalDigits value="16"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="ParResSettlRate" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="10"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="SettlementClass" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="RateplanID" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="22"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="RateplanName" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="256"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="StandardSalePrice" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="4000"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="SettlePrice" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="4000"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="OriginalBillMonth" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:length value="6"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="Reserved1" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="Reserved2" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="50"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="FeeSeq" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="50"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="MemberNums" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="50"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="IfFreeResource" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:minLength value="0"/>
																																		<xs:maxLength value="50"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="DiscountType" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="SettleDisvalue" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="BsType" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:maxLength value="10"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="RatePlanGhCode" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:maxLength value="20"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="RatePlanGhName" minOccurs="0">
																																<xs:simpleType>
																																	<xs:restriction base="xs:string">
																																		<xs:maxLength value="256"/>
																																	</xs:restriction>
																																</xs:simpleType>
																															</xs:element>
																															<xs:element name="ZhiSuan2neiMengGu" minOccurs="0">
																																<xs:complexType>
																																	<xs:sequence>
																																		<xs:element name="ZhiSuan2neiMengGuInfo" minOccurs="0" maxOccurs="unbounded">
																																			<xs:complexType>
																																				<xs:sequence>
																																					<xs:element name="SettlementPartyIn">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementPartyOutType">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="2"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementPartyOut">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:minLength value="1"/>
																																								<xs:maxLength value="64"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementRate" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:maxLength value="10"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementType" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:maxLength value="10"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="SettlementAmount">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:integer">
																																								<xs:totalDigits value="16"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																					<xs:element name="PrdSettleDisvalue" minOccurs="0">
																																						<xs:simpleType>
																																							<xs:restriction base="xs:string">
																																								<xs:maxLength value="20"/>
																																							</xs:restriction>
																																						</xs:simpleType>
																																					</xs:element>
																																				</xs:sequence>
																																			</xs:complexType>
																																		</xs:element>
																																	</xs:sequence>
																																</xs:complexType>
																															</xs:element>
																														</xs:sequence>
																													</xs:complexType>
																												</xs:element>
																											</xs:sequence>
																										</xs:complexType>
																									</xs:element>
																									<xs:element name="OnProductCode">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="OnProductName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="EjProductCode">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="EjProductName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="SjProductCode">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="SjProductName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="GhCode">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="22"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ProductClass">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="20"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ProductReportName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ProductReportItemName">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="256"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="IssueTime">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:length value="14"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ExpireTime">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:length value="14"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ProductType">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="20"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="BusiType">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="1"/>
																												<xs:maxLength value="20"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="ContractMain" minOccurs="0">
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:minLength value="0"/>
																												<xs:maxLength value="20"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="CityCode" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="1"/>
													<xs:maxLength value="20"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="CreatorName" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="1"/>
													<xs:maxLength value="64"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="ECid" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="20"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
