package com.settle.server.module.mnp.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.mnp.StlBaseinfoSrv6Mapper;
import com.settle.server.entity.mnp.StlBaseinfoSrv6;
import com.settle.server.utils.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * SRv6业务基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service("stlBaseinfoSrv6Service")
@Slf4j
public class StlBaseinfoSrv6Service extends ServiceImpl<StlBaseinfoSrv6Mapper, StlBaseinfoSrv6> implements StlBaseService {

    @Override
    public void deleteHis(String acctMonth) {
        getBaseMapper().deleteHis(acctMonth);
    }

    @Override
    public void delete(String acctMonth) {
        getBaseMapper().delete(new LambdaQueryWrapper<>(StlBaseinfoSrv6.class)
                .eq(StlBaseinfoSrv6::getSettlemonth, acctMonth));
    }

    @Override
    public void insertBatch(List<String[]> contents, String acctMonth) {
        log.info("insertBatch: processing {} records for acctMonth: {}", contents.size(), acctMonth);
        List<StlBaseinfoSrv6> entities = contents.stream()
                .map(lines -> createStlBaseinfoSrv6(acctMonth, lines))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (entities.isEmpty()) {
            log.warn("insertBatch: no valid entities to insert for acctMonth: {}", acctMonth);
            return;
        }
        // 分批插入，提高数据库性能
        batchInsertEntities(entities);
    }


    /**
     * 创建StlBaseinfoSrv6对象并处理空字符串
     */
    private StlBaseinfoSrv6 createStlBaseinfoSrv6(String acctMonth, String[] lines) {
        try {
            // 数据清理和验证
            String[] cleanedLines = cleanDataArray(lines);

            StlBaseinfoSrv6 entity = getStlBaseinfoSrv6(acctMonth, cleanedLines);
            ObjectUtils.replaceEmptyStringWithNull(entity);
            return entity;
        } catch (Exception e) {
            log.error("Failed to create StlBaseinfoSrv6 entity for acctMonth: {}, error: {}",
                    acctMonth, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清理数据数组
     */
    private String[] cleanDataArray(String[] lines) {
        String[] cleaned = new String[lines.length];
        for (int i = 0; i < lines.length; i++) {
            if (lines[i] != null) {
                // 去除前后空格，处理特殊字符
                cleaned[i] = lines[i].trim();
                // 如果需要，可以添加更多数据清理逻辑
                // 例如：处理特殊字符、长度限制等
            } else {
                cleaned[i] = null;
            }
        }
        return cleaned;
    }

    /**
     * 分批插入实体
     */
    private void batchInsertEntities(List<StlBaseinfoSrv6> entities) {
        Lists.partition(entities, 1000).forEach(part -> {
            try {
                getBaseMapper().insertBatch(part);
                log.debug("insertBatch size: {}", part.size());
            } catch (Exception e) {
                log.error("Failed to insert batch of size: {}, error: {}", part.size(), e.getMessage(), e);
                throw new RuntimeException("批量插入失败", e);
            }
        });
    }

    /**
     * POSPECNUMBER,POSPECNAME,SOSPECNUMBER,SOSPECNAME,SOID,CUSTOMERNUMBER,START_PROV_NM,START_PROV,START_CITY,MANAGER_NM,MANAGER_CON,
     * END_PROV_NM,END_PROV,END_CITY,END_DISTR,CP_NM,CP_CON,BANDWIDTH,END_RENT_RATE,END_SETUP_RATE,END_SERV_RENT_RATE,END_SERV_ONCE_RATE,ADDRESS,SETTLEMONTH
     */
    private StlBaseinfoSrv6 getStlBaseinfoSrv6(String acctMonth, String[] lines) {
        StlBaseinfoSrv6 stlBaseinfoSrv6 = new StlBaseinfoSrv6();
        stlBaseinfoSrv6.setPospecnumber(lines[0]);
        stlBaseinfoSrv6.setPospecname(lines[1]);
        stlBaseinfoSrv6.setSospecnumber(lines[2]);
        stlBaseinfoSrv6.setSospecname(lines[3]);
        stlBaseinfoSrv6.setSoid(lines[4]);
        stlBaseinfoSrv6.setCustomernumber(lines[5]);
        stlBaseinfoSrv6.setStartProvNm(lines[6]);
        stlBaseinfoSrv6.setStartProv(lines[7]);
        stlBaseinfoSrv6.setStartCity(lines[8]);
        stlBaseinfoSrv6.setManagerNm(lines[9]);
        stlBaseinfoSrv6.setManagerCon(lines[10]);
        stlBaseinfoSrv6.setEndProvNm(lines[11]);
        stlBaseinfoSrv6.setEndProv(lines[12]);
        stlBaseinfoSrv6.setEndCity(lines[13]);
        stlBaseinfoSrv6.setEndDistr(lines[14]);
        stlBaseinfoSrv6.setCpNm(lines[15]);
        stlBaseinfoSrv6.setCpCon(lines[16]);
        stlBaseinfoSrv6.setBandwidth(lines[17]);
        stlBaseinfoSrv6.setEndRentRate(lines[18]);
        stlBaseinfoSrv6.setEndSetupRate(lines[19]);
        stlBaseinfoSrv6.setEndServRentRate(lines[20]);
        stlBaseinfoSrv6.setEndServOnceRate(lines[21]);
        stlBaseinfoSrv6.setAddress(lines[22]);

        stlBaseinfoSrv6.setSettlemonth(acctMonth);
        return stlBaseinfoSrv6;
    }
}
