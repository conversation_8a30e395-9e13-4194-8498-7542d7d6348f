package com.settle.server.module.cdn.writer;


import com.settle.server.module.cdn.record.Batch;

import java.util.List;

/**
 * 将记录写入委托给编写器列表的复合记录写入器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class CompositeRecordWriter<P> implements RecordWriter<P> {

    private List<RecordWriter<P>> writers;

    /**
     * 创建一个新的 {@link CompositeRecordWriter}
     *
     * @param writers 委托
     */
    public CompositeRecordWriter(List<RecordWriter<P>> writers) {
        this.writers = writers;
    }

    @Override
    public void open() throws Exception {
        for (RecordWriter<P> writer : writers) {
            writer.open();
        }
    }

    @Override
    public void writeRecords(Batch<P> batch) throws Exception {
        for (RecordWriter<P> writer : writers) {
            writer.writeRecords(batch);
        }
    }

    @Override
    public void close() throws Exception {
        for (RecordWriter<P> writer : writers) {
            writer.close();
        }
    }
}
