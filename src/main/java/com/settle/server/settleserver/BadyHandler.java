package com.settle.server.settleserver;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@Slf4j
public class BadyHandler extends BaseHandler {
  private String table_name = "";
  private StringBuffer SQL = new StringBuffer();

  private List<String> fieldList = new ArrayList();

  protected String spliceSQL() {
    this.table_name = this.fileName.substring(0, this.fileName.lastIndexOf("."));
    Properties t_ps = new Properties();
    try {
      t_ps.load(new FileInputStream(this.file));
      String[] fields = t_ps.get("table_comment").toString().split(",");
      for (int i = 0; i < fields.length; ++i) {
        this.fieldList.add(fields[i].trim());
      }
      this.SQL.append("INSERT INTO " + this.table_name.toUpperCase() + "(");
      StringBuffer sb1 = new StringBuffer();
      StringBuffer sb2 = new StringBuffer();
      sb1.append("SETTLEMONTH,");
      sb2.append("?,");
      for (int i = 0; i < this.fieldList.size(); ++i) {
        sb1.append(((String)this.fieldList.get(i)) + ",");
        sb2.append("?,");
      }
      sb1.append("PARTID");
      sb2.append("?");

      this.SQL.append(sb1.toString()).append(")values(")
        .append(sb2.toString()).append(")");
      log.info("Import Table SQL:" + this.SQL + "...");
    }
    catch (FileNotFoundException e) {
      log.error(this.file.getName() + " File Not Found...");
      log.error(e.getMessage());
    } catch (IOException e) {
      log.error("Read " + this.file.getName() + " File Error...");
      log.error(e.getMessage());
    }
    return null;
  }

  protected void delTable()
  {
    if ((this.conn = SettleServerDBUtil.Connection()) == null)
      log.error("DB internal error, retry later please!");
    else
      try {
        log.info("Clear Up Data SQL:DELETE FROM " +
          this.table_name.toUpperCase() + 
          " WHERE SETTLEMONTH=? AND PARTID=?");
        this.prep = this.conn.prepareStatement("DELETE FROM " + 
          this.table_name.toUpperCase() + 
          " WHERE SETTLEMONTH=? AND PARTID=?");
        log.info("Params : [" + this.lastMonth + "," +
          this.lastMonth.substring(4) + "]...");
        this.prep.setString(1, this.lastMonth);
        this.prep.setString(2, this.lastMonth.substring(4));
        this.prep.executeUpdate();
        log.info("Clear Up Data Success...");
      } catch (SQLException e) {
        log.error("");
        e.printStackTrace();
      }
  }

  protected String intoDB()
  {
    spliceSQL();
    delTable();
    this.dataFile = 
      new File(dataPath+ File.separator + this.lastMonth + File.separator + getStringValue(this.table_name).replace("YYYYMM", this.lastMonth));
    this.dataFileName = this.dataFile.getName();
    if ((this.dataFile.isFile()) && (this.dataFile.exists())) {
      if ((this.conn = SettleServerDBUtil.Connection()) == null)
        log.error("DB internal error, retry later please!");
      else {
        try {
          InputStreamReader read = new InputStreamReader(
            new FileInputStream(this.dataFile), "UTF-8");
          this.conn.setAutoCommit(false);
          this.prep = this.conn.prepareStatement(this.SQL.toString());
          BufferedReader bufferedReader = new BufferedReader(read);

          String lineTxt = null;
          int count = 0;
          while (((lineTxt = bufferedReader.readLine()) != null) && (!(
            lineTxt.trim().equals(""))))
          {
            String[] fields = lineTxt.split("\\u005E");
            this.prep.setString(1, this.lastMonth);
            for (int i = 0; i < this.fieldList.size(); ++i) {
              this.prep.setString(i + 2, fields[i].trim());
            }
            this.prep.setString(this.fieldList.size() + 2, this.lastMonth
              .substring(4));
            this.prep.addBatch();
            ++count;
            if (count % Integer.valueOf(COMMIT_COUNT.toLowerCase()).intValue() != 0) {
              continue;
            }
            this.prep.executeBatch();
            this.conn.commit();
            log.info(this.dataFileName + " Import Database " +
              count + " record...");
          }

          this.prep.executeBatch();
          this.conn.commit();
          log.info(this.dataFileName + " Import Database " + count +
            " record...");
          read.close();
        } catch (UnsupportedEncodingException e) {
          log.error("");
          e.printStackTrace();
        } catch (FileNotFoundException e) {
          log.error(this.dataFileName + " File Not Found...");
          e.printStackTrace();
        } catch (SQLException e) {
          log.error("");
          e.printStackTrace();
        } catch (IOException e) {
          log.error("Read " + this.dataFileName + " File Error...");
          e.printStackTrace();
        } finally {
          this.SQL.delete(0, this.SQL.length());
          this.fieldList.clear();
        }
      }
    }
    return null;
  }
}