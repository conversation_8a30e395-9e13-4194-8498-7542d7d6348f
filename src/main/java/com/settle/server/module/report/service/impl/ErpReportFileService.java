package com.settle.server.module.report.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.module.report.config.ReportConfig;
import com.settle.server.module.report.dto.ReportDTO;
import com.settle.server.module.report.service.ReportFileService;
import com.settle.server.stludrtool.Xls2TxtTool;
import com.settle.server.utils.ExceptionCast;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/9
 * @since 1.0.0
 */
@Service("erpReportFileService")
@Slf4j
public class ErpReportFileService implements ReportFileService {

    @Autowired
    private ReportConfig reportConfig;

    @Override
    public String reportFile(List<ReportDTO> reportDTOList) throws Exception {
        if (StringUtils.isBlank(reportConfig.getRemoteUrl())) {
            ExceptionCast.cast("报表服务远程地址为空");
        }
        List<CompletableFuture<Void>> futures = reportDTOList.stream().map(reportDTO -> {
            return CompletableFuture.runAsync(() -> {
                this.doFile(reportDTO);
            }).exceptionally(e -> {
                log.error("生成报表文件失败. {}", JSONUtil.toJsonStr(reportDTO), e);
                return null;
            });
        }).collect(Collectors.toList());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return reportConfig.getErpFilePath();
    }

    private void doFile(ReportDTO reportDTO) {
        //1.请求report服务，生成报表 xls
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("R_KEY", reportDTO.getRKey());
        paramMap.put("settlemonth", reportDTO.getSettleMonth());
        paramMap.put("sign", reportDTO.getSign());
        XxlJobHelper.log("请求地址：{},请求参数：{}", getUrl(),paramMap);
        String json = HttpUtil.get(getUrl(), paramMap, reportConfig.getTimeout());
        log.info("请求报表服务返回结果:{}", json);
        JSONObject jsonObject = JSONUtil.parseObj(json);

        if (jsonObject.getStr("code") != null && !jsonObject.getStr("code").equals("200")) {
            ExceptionCast.cast("请求报表服务失败. %s", jsonObject.getStr("msg"));
        }
        String fileName = jsonObject.getStr("data");
        if (StringUtils.isBlank(fileName)) {
            ExceptionCast.cast("请求报表服务失败. 返回文件名为空");
        }
        //2.将xls转换成txt
        Xls2TxtTool x = new Xls2TxtTool(reportConfig.getErpFilePath());
        x.setFileName(fileName);
        x.excel2Txt();
    }

    private String getUrl() {
        String remoteUrl = reportConfig.getRemoteUrl();
        if (!remoteUrl.endsWith("/")) {
            remoteUrl+="/";
        }
        String erpUri = reportConfig.getErpUri();
        if (erpUri.startsWith("/")) {
            erpUri = erpUri.substring(1);
        }
        String s = remoteUrl + erpUri;
        //去掉最后一个
        if (s.endsWith("/")) {
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }
}