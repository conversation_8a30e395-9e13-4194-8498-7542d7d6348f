package com.settle.server.module.cdn.listener;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.setting.Setting;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.constant.RuleConst;
import com.settle.server.module.cdn.job.JobMetrics;
import com.settle.server.module.cdn.job.JobStatus;

import javax.sql.DataSource;
import java.io.File;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 缺省文件监听器
 *
 * <AUTHOR>
 */
public class DefaultFileLister implements FileListener {
    /**
     * 变量Map
     */
    private Map<String, Object> variableMap;
    /**
     * 日志ID
     */
    private String logId;

    /**
     * 数据文件
     */
    private File dataFile;

    /**
     * work目录文件
     */
    private File workFile;

    /**
     * 规则设置
     */
    private Setting ruleSetting;

    private DataSource dataSource;

    public DefaultFileLister() {
        // no op
    }

    public DefaultFileLister(final String logId, final File dataFile, final Setting ruleSetting,final DataSource dataSource) {
        this.logId = logId;
        this.dataFile = dataFile;
        this.ruleSetting = ruleSetting;
        this.variableMap = new HashMap<>();
        this.dataSource = dataSource;
    }

    @Override
    public void beforeFileProcessing() {
        try {
            DateTime dateTimeNow = DateTime.now();
            Db.use(dataSource).tx(db -> {
                db.insert(Entity.create(NormalConst.DEFAULT_LOG_TABLE).set("id", this.logId)
                        .set("file_name", this.dataFile.getName())
                        .set("action", "csv2db")
                        .set("create_date", dateTimeNow)
                        .set("status", JobStatus.STARTING.name())
                        .set("error_code", 0)
                        .set("error_desc", "")
                        .set("read_count", -1)
                        .set("write_count", -1)
                        .set("filter_count", -1)
                        .set("error_count", -1));
            });

            //yyyy-MM-dd
            variableMap.put(DatePattern.NORM_DATE_PATTERN, dateTimeNow.toString(DatePattern.NORM_DATE_PATTERN));
            //yyyy-MM
            variableMap.put(DatePattern.NORM_MONTH_PATTERN, dateTimeNow.toString(DatePattern.NORM_MONTH_PATTERN));
            //yyyyMM
            variableMap.put(DatePattern.SIMPLE_MONTH_PATTERN, dateTimeNow.toString(DatePattern.SIMPLE_MONTH_PATTERN));
            //yyyyMMdd
            variableMap.put(DatePattern.PURE_DATE_PATTERN, dateTimeNow.toString(DatePattern.PURE_DATE_PATTERN));
            //yyyyMMddHHmmss
            variableMap.put(DatePattern.PURE_DATETIME_PATTERN, dateTimeNow.toString(DatePattern.PURE_DATETIME_PATTERN));

            String workPath = ruleSetting.getStr(RuleConst.G_K_WORK_PATH, NormalConst.DEFAULT_WORK_PATH);
            //初始化日志成功后将文件移至工作目录
            this.workFile = FileUtil.file(workPath + dataFile.getName());
            FileUtil.move(dataFile, this.workFile, true);
        } catch (SQLException ex) {
            ex.printStackTrace();
        }

    }

    @Override
    public void afterFileProcessing(final JobMetrics jobMetrics) {
        try {
            Db.use(dataSource).tx(db ->
                    db.update(Entity.create(NormalConst.DEFAULT_LOG_TABLE)
                            .set("status", JobStatus.COMPLETED.name())
                            .set("error_code", "000")
                            .set("error_desc", "")
                            .set("read_count", jobMetrics.getReadCount())
                            .set("write_count", jobMetrics.getWriteCount())
                            .set("filter_count", jobMetrics.getFilterCount())
                            .set("error_count", jobMetrics.getErrorCount()), Entity.create().set("id", this.logId)));

            //备份文件路径（如未配置，则缺省为：../backup/{yyyy-MM-dd}/）
            String backPath = StrUtil.format(ruleSetting.getStr(RuleConst.G_K_FILE_BAK_PATH, NormalConst.DEFAULT_FILE_BAK_PATH), variableMap);
            FileUtil.mkdir(backPath);
            FileUtil.move(this.workFile, FileUtil.file(backPath + this.dataFile.getName()), true);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onFileProcessingException(final Throwable throwable) {
        try {
            Db.use(dataSource).update(Entity.create(NormalConst.DEFAULT_LOG_TABLE)
                            .set("status", JobStatus.FAILED.name())
                            .set("error_code", "E110")
                            .set("error_desc", StrUtil.sub(throwable.getMessage(), 0, 255))
                    , Entity.create().set("id", this.logId));
            String errorPath = ruleSetting.getStr(RuleConst.G_K_ERROR_PATH, NormalConst.DEFAULT_FILE_ERR_PATH);
            FileUtil.move(this.workFile, FileUtil.file(errorPath  + this.dataFile.getName()), true);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
