package com.settle.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import com.settle.server.config.IotConfig;
import com.settle.server.dao.stludr.StlIotSettDao;
import com.settle.server.entity.StlCheckIotSett;
import com.settle.server.entity.StlIotSett;
import com.settle.server.entity.StlPaxcSett;
import com.settle.server.enums.IotSettConstants;
import com.settle.server.enums.PatternConstants;
import com.settle.server.enums.PaxcSettConstants;
import com.settle.server.service.IotSettService;
import com.settle.server.utils.EnumUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FilenameFilter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Classname IotSettServiceImpl
 * @Description TODO
 * @Date 2023/10/17 15:23
 * <AUTHOR>
 * @Version 1.0.0
 */
@Slf4j
@Service
public class IotSettServiceImpl implements IotSettService {

    public static final char VERTICAL_BAR = '|';

    @Autowired
    private IotConfig iotConfig;

    @Autowired
    private StlIotSettDao stlIotSettDao;

    @Override
    public void save(String acctMonth) throws Exception {
        Path errorPath = Paths.get(iotConfig.getErrorPath(), acctMonth);
        Path backupPath = Paths.get(iotConfig.getBackupPath(), acctMonth);
        List<Boolean> flags = new ArrayList();
        if (FileUtil.exist(iotConfig.getDataPath())) {
            File dataFile = FileUtil.file(iotConfig.getDataPath());
            File[] iotFiles = dataFile.listFiles(new FilenameFilter() {
                // 将被选出来，其余被过滤掉
                @Override
                public boolean accept(File dir, String fileName) {
                    Pattern pattern = Pattern.compile(iotConfig.getValidName());
                    if (pattern.matcher(fileName).matches()) {
                        flags.add(true);
                        return true;
                    }
                    flags.add(false);
                    return false;
                }
            });
            if (flags.contains(true)) {
                stlIotSettDao.deleteBySettleMonth(acctMonth, "", IotSettConstants.BizType.IOT.getValue());
            }
            if (iotFiles.length == 0) {
                log.warn("Iot No of input file not found under dir path:{}", iotConfig.getDataPath());
            }
            for (File iotFile : iotFiles) {
                String fileName = iotFile.getName();
                List<StlIotSett> stlIotSetts = new ArrayList<>();
                List<String> iotLines = FileUtil.readUtf8Lines(iotFile);
                iotLines.removeAll(Arrays.asList("", null));
                if (iotLines.size() > 0) {
                    log.info("Iot input file details path:{}, row:{}", iotFile.getAbsolutePath(), iotLines.size());
                    try {
                        stlIotSetts.addAll(iotLines.stream()
                                .map(iotLine -> {
                                    List<String> result = StrUtil.split(iotLine, VERTICAL_BAR, false, false);
                                    StlIotSett stlIotSett = new StlIotSett();
                                    if (result.size() != 11) {
                                        throw new RuntimeException(String.format("Iot Verification Error fileName: %s, 该行不是11个参数, is %s, iotLine is %s", fileName, result.size(), iotLine));
                                    }
                                    String settleMonth = result.get(0);
                                    String productSpecNum = result.get(1);
                                    String settleType = result.get(2);
                                    String settleOut = result.get(3);
                                    String settleIn = result.get(4);
                                    String partnerName = result.get(5);
                                    String productName = result.get(6);
                                    String settleAmount = result.get(7);
                                    String taxRate = result.get(8);
                                    String partnerNumber = result.get(9);
                                    String partnerRuleType = result.get(10);
                                    if ("05".equals(partnerNumber)){
                                        if (!(partnerName.length() > 0 && partnerName.length() <= 100 ) || !(productName.length() > 0 && productName.length() <= 100)){
                                            throw new RuntimeException(String.format("Iot Verification Error fileName: %s 当前行校验error: %s", fileName, iotLine));
                                        }
                                    }
                                    if (StrUtil.isNotBlank(partnerRuleType)){
                                        boolean include = EnumUtils.isInclude(IotSettConstants.partnerRuleType.class, partnerRuleType.trim());
                                        if (!include){
                                            log.error("合作伙伴规则类型parterRuleType不在枚举范围内");
                                            throw new RuntimeException(String.format("Iot Verification Error fileName: %s 当前行校验error: %s", fileName, iotLine));
                                        }
                                    }
                                    if ("06".equals(settleType) && "05".equals(partnerNumber) && StrUtil.isBlank(partnerRuleType)) {
                                        log.error("结算类型为06且合作伙伴编码=05时,合作伙伴规则类型parterRuleTyp必填");
                                        throw new RuntimeException(String.format("Iot Verification Error fileName: %s 当前行校验error: %s", fileName, iotLine));
                                    }
                                    if (settleMonth.length() == 6 &&
                                            settleMonth.equals(acctMonth) &&
                                            (EnumUtils.isInclude(IotSettConstants.ProductSpecNum.class, productSpecNum.trim())) &&
                                            (EnumUtils.isInclude(IotSettConstants.SettleType.class, settleType.trim())) &&
                                            ((("01".equals(settleType) || "06".equals(settleType)) && settleOut.length() > 0 && settleOut.length() <= 4)
                                                    || "02".equals(settleType) || "03".equals(settleType) || "04".equals(settleType) || "05".equals(settleType)) &&
                                            (("01".equals(settleType) && "00".equals(settleIn)) || ("01".equals(settleType) && "12".equals(settleIn))
                                                    || "02".equals(settleType) || "03".equals(settleType) || "04".equals(settleType) || "05".equals(settleType) || "06".equals(settleType)) &&
                                            ((("02".equals(settleType) || "03".equals(settleType) || "04".equals(settleType) || "05".equals(settleType)) && partnerName.length() > 0 && partnerName.length() <= 100)
                                                    || "01".equals(settleType) || "06".equals(settleType)) &&
                                            ((("02".equals(settleType) || "03".equals(settleType) || "04".equals(settleType) || "05".equals(settleType)) && productName.length() > 0 && productName.length() <= 100)
                                                    || "01".equals(settleType) || "06".equals(settleType)) &&
                                            (settleAmount.length() > 0 && settleAmount.length() <= 20) &&
                                            (Integer.parseInt(taxRate) > 0 && Integer.parseInt(taxRate) <= 100) &&
                                            (("06".equals(settleType) && EnumUtils.isInclude(IotSettConstants.parterNumber.class, partnerNumber.trim()))  ||
                                                    "01".equals(settleType) || "02".equals(settleType) || "03".equals(settleType) || "04".equals(settleType) || "05".equals(settleType))
                                    ) {
                                        stlIotSett.setSettleMonth(settleMonth);
                                        stlIotSett.setProductSpecNum(productSpecNum);
                                        stlIotSett.setSettleType(settleType);
                                        stlIotSett.setSettleOut(settleOut);
                                        stlIotSett.setSettleIn(settleIn);
                                        stlIotSett.setPartnerName(partnerName);
                                        stlIotSett.setProductName(productName);
                                        stlIotSett.setSettleAmount(settleAmount);
                                        stlIotSett.setTaxRate(taxRate);
                                        stlIotSett.setPartnerNumber(partnerNumber);
                                        stlIotSett.setSourceFileName(fileName);
                                        stlIotSett.setBizType(IotSettConstants.BizType.IOT.getValue());
                                        stlIotSett.setPartnerRuleType(partnerRuleType);
                                        return stlIotSett;
                                    } else {
                                        throw new RuntimeException(String.format("Iot Verification Error fileName: %s 当前行校验error: %s", fileName, iotLine));
                                    }
                                })
                                .collect(Collectors.toList()));
                        log.info("Iot loading_file_starts fileName:{} acctMonth:{}", fileName, acctMonth);
                        stlIotSettDao.batchInsert(stlIotSetts);
                        log.info("Iot loading_file_done fileName:{} acctMonth:{}", fileName, acctMonth);
                        Files.createDirectories(backupPath);
                        Files.move(iotFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                    } catch (Exception e) {
                        log.error("Iot 执行失败 fileName:{} acctMonth:{} error：{}", fileName, acctMonth, e.getMessage(), e);
                        Files.createDirectories(errorPath);
                        Files.move(iotFile.toPath(), errorPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                        stlIotSettDao.deleteBySettleMonth(acctMonth, fileName, IotSettConstants.BizType.IOT.getValue());
                        throw e;
                    }
                } else {
                    log.warn("Iot input file is empty dir {}. Exit now.", iotFile.getAbsolutePath());
                    Files.createDirectories(backupPath);
                    Files.move(iotFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                }
            }
            // 读取校验文件
            File[] checkFiles = dataFile.listFiles(new FilenameFilter() {
                // 将被选出来，其余被过滤掉
                @Override
                public boolean accept(File dir, String fileName) {
                    Pattern pattern = Pattern.compile(iotConfig.getCheckName());
                    if (pattern.matcher(fileName).matches()) {
                        return true;
                    }
                    return false;
                }
            });
            if (checkFiles.length == 0) {
                log.warn("Iot checkFile No of input file not found under dir path:{}", iotConfig.getDataPath());
            }
            for (File checkFile : checkFiles) {
                String checkFileName = checkFile.getName();
                try {
                    List<StlCheckIotSett> checkIotSetts = new ArrayList<>();
                    List<String> checkLines = FileUtil.readUtf8Lines(checkFile);
                    checkLines.removeAll(Arrays.asList("", null));
                    if (checkLines.size() > 0) {
                        log.info("Iot checkFile input file details checkPath:{}, row:{}", checkFile.getAbsolutePath(), checkLines.size());
                        Pattern regexPattern = Pattern.compile(PatternConstants.VERF_IOTSC_PATTERN);
                        Matcher matcher = regexPattern.matcher(checkFileName);
                        if (matcher.find()) {
                            // 匹配第四个括号内的内容，即 "202309"
                            String settleMonthGroup = matcher.group(4);
                            this.checkSettleMonth(checkFileName, acctMonth, settleMonthGroup);
                            this.checkFileSize(checkFile);
                            checkIotSetts.addAll(checkLines.stream()
                                    .map(checkLine -> {
                                        List<String> result = StrUtil.split(checkLine, VERTICAL_BAR, false, false);
                                        StlCheckIotSett checkIotSett = new StlCheckIotSett();
                                        checkIotSett.setFileName(result.get(0));
                                        checkIotSett.setFileTotal(result.get(1));
                                        checkIotSett.setTotalAmount(result.get(2));
                                        return checkIotSett;
                                    })
                                    .collect(Collectors.toList()));
                            for (StlCheckIotSett checkIotSett : checkIotSetts) {
                                String dbTotalCount = stlIotSettDao.selectCount(acctMonth, checkIotSett.getFileName(), IotSettConstants.BizType.IOT.getValue());
                                if (!dbTotalCount.equals(checkIotSett.getFileTotal())) {
                                    throw new RuntimeException(String.format("Iot checkFileName:%s dbCount:%s fileTotal:%s not same，Please confirm manually",
                                            checkFileName, dbTotalCount, checkIotSett.getFileTotal()));
                                }
                                // 数据库中是string 进行数字运算存在精度问题，接口规范为两位小数，所以此处也保留两位
                                BigDecimal dbSumAmount = stlIotSettDao.selectSumAmount(acctMonth, checkIotSett.getFileName(), IotSettConstants.BizType.IOT.getValue());
                                if (dbSumAmount.setScale(2, RoundingMode.HALF_UP).compareTo(new BigDecimal(checkIotSett.getTotalAmount()).setScale(2, RoundingMode.HALF_UP)) != 0) {
                                    throw new RuntimeException(String.format("Iot checkFileName:%s dbSumAmount:%s fileAmount:%s not same，Please confirm manually",
                                            checkFileName, dbSumAmount.doubleValue(), checkIotSett.getTotalAmount()));
                                }
                                log.info("Iot checkFile_done fileName:{} acctMonth:{}", checkIotSett.getFileName(), acctMonth);
                                Files.createDirectories(backupPath);
                                Files.move(checkFile.toPath(), backupPath.resolve(checkFileName), StandardCopyOption.REPLACE_EXISTING);
                            }
                        } else {
                            throw new RuntimeException(String.format("Iot No match found. Error checkFileName:%s，Please confirm manually", checkFileName));
                        }
                    } else {
                        log.warn("Iot checkFile No input file not found under dir {}. Exit now.", checkFile.getAbsolutePath());
                        Files.createDirectories(backupPath);
                        Files.move(checkFile.toPath(), backupPath.resolve(checkFileName), StandardCopyOption.REPLACE_EXISTING);
                    }
                } catch (Exception e) {
                    log.error("Iot checkFile 执行失败 acctMonth:{} error：{}", acctMonth, e.getMessage(), e);
                    Files.createDirectories(errorPath);
                    Files.move(checkFile.toPath(), errorPath.resolve(checkFileName), StandardCopyOption.REPLACE_EXISTING);
                    stlIotSettDao.deleteBySettleMonth(acctMonth, checkFile.getName(), IotSettConstants.BizType.IOT.getValue());
                    throw e;
                }
            }
        } else {
            log.error("Iot checkFile data path:{} does not exist", iotConfig.getDataPath());
        }
    }

    @Override
    public void paxcSave(String acctMonth) throws Exception {
        Path errorPath = Paths.get(iotConfig.getErrorPath(), acctMonth);
        Path backupPath = Paths.get(iotConfig.getBackupPath(), acctMonth);
        List<Boolean> flags = new ArrayList();
        File dataFile = FileUtil.file(iotConfig.getDataPath());
        File[] paxcFiles = dataFile.listFiles(new FilenameFilter() {
            // 将被选出来，其余被过滤掉
            @Override
            public boolean accept(File dir, String fileName) {
                Pattern pattern = Pattern.compile(iotConfig.getPaxcValidName());
                if (pattern.matcher(fileName).matches()) {
                    flags.add(true);
                    return true;
                }
                flags.add(false);
                return false;
            }
        });
        if (flags.contains(true)) {
            stlIotSettDao.deleteBySettleMonth(acctMonth, "", IotSettConstants.BizType.ESOP.getValue());
        }
        if (paxcFiles.length == 0) {
            log.warn("PAXC No of input file not found under dir path:{}", iotConfig.getDataPath());
        }
        for (File paxcFile : paxcFiles) {
            String fileName = paxcFile.getName();
            List<StlIotSett> stlPaxcSetts = new ArrayList<>();
            String paxcLines = FileUtil.readUtf8String(paxcFile);
            if (StrUtil.isNotBlank(paxcLines)) {
                log.info("PAXC input file details path:{}", paxcFile.getAbsolutePath());
                try {
                    Gson gson = new Gson();
                    StlPaxcSett stlPaxcSett = gson.fromJson(paxcLines, StlPaxcSett.class);
                    String settleMonth = stlPaxcSett.getSettleMonth();
                    if (settleMonth.length() != 6 || !settleMonth.equals(acctMonth)) {
                        throw new RuntimeException(String.format("PAXC Verification Error fileName:%s 账期校验error: 参数账期: %s 文件账期: %s", fileName, acctMonth, settleMonth));
                    }
                    stlPaxcSett.getBillList().forEach(bill -> {
                        String productSpecNum = bill.getProductSpecNum();
                        String settleType = bill.getSettleType();
                        List<StlPaxcSett.ProvSettle> provSettleList = bill.getProvSettleLIst();
                        if (!EnumUtils.isInclude(PaxcSettConstants.ProductSpecNum.class, productSpecNum.trim())) {
                            throw new RuntimeException(String.format("PAXC Verification Error fileName:%s billlist校验error: productspecnum: %s", fileName, productSpecNum));
                        }
                        if (!EnumUtils.isInclude(PaxcSettConstants.SettleType.class, settleType.trim())) {
                            throw new RuntimeException(String.format("PAXC Verification Error fileName:%s billlist校验error: settletype: %s", fileName, settleType));
                        }
                        provSettleList.forEach(provSettle -> {
                            String settleAmount = provSettle.getSettleAmount();
                            String settleIn = provSettle.getSettleIn();
                            String settleOut = provSettle.getSettleOut();
                            String taxRate = provSettle.getTaxRate();
                            if ((settleOut.length() > 0 && settleOut.length() <= 4) &&
                                    (settleAmount.length() > 0 && settleAmount.length() <= 20) &&
                                    Integer.parseInt(taxRate) > 0 && Integer.parseInt(taxRate) <= 100) {
                                StlIotSett stlIotSett = new StlIotSett();
                                stlIotSett.setSettleMonth(settleMonth);
                                stlIotSett.setProductSpecNum(productSpecNum);
                                stlIotSett.setSettleType(settleType);
                                stlIotSett.setSettleOut(settleOut);
                                stlIotSett.setSettleIn(settleIn);
                                stlIotSett.setSettleAmount(settleAmount);
                                stlIotSett.setTaxRate(taxRate);
                                stlIotSett.setSourceFileName(fileName);
                                stlIotSett.setBizType(IotSettConstants.BizType.ESOP.getValue());
                                stlPaxcSetts.add(stlIotSett);
                            } else {
                                throw new RuntimeException(String.format("PAXC Verification Error fileName:%s provsettlelist校验error: %s", fileName, provSettle));
                            }
                        });
                    });
                    log.info("PAXC loading_file_starts fileName:{} acctMonth:{}", fileName, acctMonth);
                    stlIotSettDao.batchInsert(stlPaxcSetts);
                    log.info("PAXC loading_file_done fileName:{} acctMonth:{}", fileName, acctMonth);
                    // 执行成功备份
                    Files.createDirectories(backupPath);
                    Files.move(paxcFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                } catch (Exception e) {
                    log.error("PAXC 执行失败 fileName:{} acctMonth:{} error：{}", fileName, acctMonth, e.getMessage(), e);
                    Files.createDirectories(errorPath);
                    Files.move(paxcFile.toPath(), errorPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                    stlIotSettDao.deleteBySettleMonth(acctMonth, fileName, IotSettConstants.BizType.ESOP.getValue());
                    throw e;
                }
            } else {
                log.warn("PAXC input file is empty dir {}. Exit now.", paxcFile.getAbsolutePath());
                // 执行成功备份
                Files.createDirectories(backupPath);
                Files.move(paxcFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
            }
        }
    }

    private void checkSettleMonth(String checkFileName, String srcAcctMonth, String destAcctMonth) {
        if (!srcAcctMonth.equals(destAcctMonth)) {
            throw new RuntimeException(String.format("Iot checkSettleMonth Error checkFileName:%s acctMonth:%s fileAcctMonth:%s - not same，Please confirm manually",
                    checkFileName, srcAcctMonth, destAcctMonth));
        }
    }

    private void checkFileSize(File checkFile) {
        if (checkFile.length() > 5 * 1024 * 1024) {
            throw new RuntimeException(String.format("Iot checkSettleMonth Error checkFileName:%s length:%s is > 5M，Please confirm manually",
                    checkFile.getName(), checkFile.length()));
        }
    }
}
