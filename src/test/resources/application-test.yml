# 测试环境配置，用于调试SQL问题
spring:
  datasource:
    url: *********************************************************************************************************************************
    username: test_user
    password: test_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
# MyBatis配置
mybatis-plus:
  configuration:
    # 开启SQL日志输出
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
  # 指定mapper xml文件位置
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    # 显示SQL语句
    com.settle.server.dao.stludr.mnp.StlBaseinfoSrv6Mapper: DEBUG
    # 显示SQL参数和结果
    org.apache.ibatis: DEBUG
    # 显示我们的Service日志
    com.settle.server.module.mnp.service: DEBUG
    # 根日志级别
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
