package com.settle.server.module.clearup.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.settle.server.module.clearup.config.ClearUpTableConfig;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.RunLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/23
 * @since 1.0.0
 */
@Service("dayPartitionClearService")
@Slf4j
public class DayPartitionClearService extends AbstractClearTableService{

    private int partitionNum = 5;
    private String partitionFormat = "p%04d";

    @Autowired
    private ClearUpTableConfig config;

    @Override
    protected void beforeProcess(RunLog runLog, DbNameEnum dbNameEnum) {
        runLog.setExec(true);
    }

    @Override
    protected List<String> partitionsBySelf(ClearLogDTO dto) {
        ArrayList<String> partitionList = Lists.newArrayList();

        String tableName = dto.getTableName();
        if ("dupchk_udr".equals(tableName)) {
            //取2个半月之前的日期
            DateTime halfMonthDay = DateUtil.offsetDay(DateTime.now(), -15);
            DateTime twoMonthHalfOfDate = DateUtil.offsetMonth(halfMonthDay, -2);
            int partition = Integer.parseInt(DateUtil.format(twoMonthHalfOfDate, "MMdd"));
            partitionList.add(String.format(partitionFormat, partition));
            return partitionList;
        }
        Integer backTrackDay = config.getBackTrackDay();
        //删除后 backTrackDay 个分区
        DateTime now = DateTime.of(DateTime.now());
        for (int i = 0; i < backTrackDay; i++) {
            if (partitionNum - i <= 0) {
                break;
            }
            Integer mmdd = Integer.parseInt(DateUtil.format(DateUtil.offsetDay(now, partitionNum - i), "MMdd"));
            String partition = String.format(partitionFormat, mmdd);
            partitionList.add(partition);
        }
        return partitionList;
    }
}