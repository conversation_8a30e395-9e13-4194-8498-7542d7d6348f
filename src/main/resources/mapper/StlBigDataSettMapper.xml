<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.StlBigDataSettDao">
    <resultMap id="BaseResultMap" type="com.settle.server.entity.StlBigDataSett">
        <result column="PROVINCE_CODE_IN" jdbcType="VARCHAR" property="provinceCodeIn" />
        <result column="PROVINCE_NAME_IN" jdbcType="VARCHAR" property="provinceNameIn" />
        <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName" />
        <result column="BUS_TYPE" jdbcType="VARCHAR" property="busType" />
        <result column="DOWN_DATA_TRAFFIC" jdbcType="VARCHAR" property="downDataTraffic" />
        <result column="SETTLE_MONTH" jdbcType="VARCHAR" property="settleMonth" />
        <result column="SOURCE_FILE_NAME" jdbcType="VARCHAR" property="sourceFileName" />
    </resultMap>

    <insert id="batchInsert" useGeneratedKeys="false" parameterType="java.util.List">
        insert into STLUDR.STL_BIG_DATA_SETT (PROVINCE_NAME_IN, PROVINCE_CODE_IN, PROVINCE_CODE,PROVINCE_NAME, BUS_TYPE, DOWN_DATA_TRAFFIC, SETTLE_MONTH, SOURCE_FILE_NAME)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            ( select
            #{item.provinceNameIn, jdbcType = VARCHAR},
            #{item.provinceCodeIn, jdbcType = VARCHAR},
            #{item.provinceCode, jdbcType = VARCHAR},
            #{item.provinceName, jdbcType = VARCHAR},
            #{item.busType, jdbcType = VARCHAR},
            #{item.downDataTraffic, jdbcType = VARCHAR},
            #{item.settleMonth, jdbcType = VARCHAR},
            #{item.sourceFileName, jdbcType = VARCHAR} from dual)
        </foreach>
    </insert>

    <delete id="deleteBySettleMonth" parameterType="java.lang.String">
        delete from STLUDR.STL_BIG_DATA_SETT
        <where>
            <if test="acctMonth != null and acctMonth != ''">
                AND SETTLE_MONTH = #{acctMonth, jdbcType = VARCHAR}
            </if>
            <if test="settleType != null and settleType != ''">
                AND BUS_TYPE = #{busType, jdbcType = VARCHAR}
            </if>
            <if test="fileName != null and fileName != ''">
                AND SOURCE_FILE_NAME = #{fileName, jdbcType = VARCHAR}
            </if>
        </where>
    </delete>
</mapper>