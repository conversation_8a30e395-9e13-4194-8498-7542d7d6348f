package com.settle.server.module.cdn.writer;


import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;

/**
 * 通过调用其 <code>toString</code> 方法将 {@link Record} 的 <strong>payload</strong> 写入标准输出的 RecordWriter
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class StandardOutputRecordWriter<P> implements RecordWriter<P> {

    @Override
    public void writeRecords(Batch<P> batch) {
        for (Record<P> record : batch) {
            System.out.println(record.getPayload().toString());
        }
    }

}
