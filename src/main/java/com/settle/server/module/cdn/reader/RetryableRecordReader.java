package com.settle.server.module.cdn.reader;

import com.settle.server.module.cdn.record.Record;
import com.settle.server.module.cdn.retry.RetryPolicy;
import com.settle.server.module.cdn.retry.RetryTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

/**
 * 当数据源暂时不可用时，装饰器使 {@link RecordReader} 可重试
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class RetryableRecordReader<P> implements RecordReader<P> {

    private RecordReader<P> delegate;
    private RecordReadingCallable<P> recordReadingCallable;
    private RecordReadingTemplate recordReadingTemplate;

    /**
     * 创建一个新的 {@link RetryableRecordReader}.
     *
     * @param delegate    记录读取器
     * @param retryPolicy 重试策略
     */
    public RetryableRecordReader(RecordReader<P> delegate, RetryPolicy retryPolicy) {
        this.delegate = delegate;
        this.recordReadingCallable = new RecordReadingCallable<>(delegate);
        this.recordReadingTemplate = new RecordReadingTemplate(retryPolicy);
    }

    @Override
    public void open() throws Exception {
        delegate.open();
    }

    @Override
    public Record<P> readRecord() throws Exception {
        return recordReadingTemplate.execute(recordReadingCallable);
    }

    @Override
    public void close() throws Exception {
        delegate.close();
    }

    private static class RecordReadingCallable<P> implements Callable<Record<P>> {

        private RecordReader<P> recordReader;

        RecordReadingCallable(RecordReader<P> recordReader) {
            this.recordReader = recordReader;
        }

        @Override
        public Record<P> call() throws Exception {
            return recordReader.readRecord();
        }

    }

    private static class RecordReadingTemplate extends RetryTemplate {

        private final Logger LOGGER = LoggerFactory.getLogger(RecordReadingTemplate.class.getName());

        RecordReadingTemplate(RetryPolicy retryPolicy) {
            super(retryPolicy);
        }

        @Override
        protected void beforeCall() {
            // no op
        }

        @Override
        protected void afterCall(Object result) {
            // no op
        }

        @Override
        protected void onException(Exception e) {
            LOGGER.error("Unable to read next record", e);
        }

        @Override
        protected void onMaxAttempts(Exception e) {
            LOGGER.error("Unable to read next record after {} attempt(s)", retryPolicy.getMaxAttempts());
        }

        @Override
        protected void beforeWait() {
            LOGGER.debug("Waiting for {} {} before retrying to read next record", retryPolicy.getDelay(), retryPolicy.getTimeUnit());
        }

        @Override
        protected void afterWait() {
            // no op
        }

    }
}
