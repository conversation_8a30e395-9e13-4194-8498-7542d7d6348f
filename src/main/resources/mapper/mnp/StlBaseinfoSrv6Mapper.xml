<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.mnp.StlBaseinfoSrv6Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.mnp.StlBaseinfoSrv6">
        <id column="id" property="id" />
        <result column="pospecnumber" property="pospecnumber" />
        <result column="pospecname" property="pospecname" />
        <result column="sospecnumber" property="sospecnumber" />
        <result column="sospecname" property="sospecname" />
        <result column="soid" property="soid" />
        <result column="customernumber" property="customernumber" />
        <result column="customername" property="customername" />
        <result column="start_prov_nm" property="startProvNm" />
        <result column="start_prov" property="startProv" />
        <result column="start_city" property="startCity" />
        <result column="manager_nm" property="managerNm" />
        <result column="manager_con" property="managerCon" />
        <result column="end_prov_nm" property="endProvNm" />
        <result column="end_prov" property="endProv" />
        <result column="end_city" property="endCity" />
        <result column="end_distr" property="endDistr" />
        <result column="cp_nm" property="cpNm" />
        <result column="cp_con" property="cpCon" />
        <result column="bandwidth" property="bandwidth" />
        <result column="address" property="address" />
        <result column="start_rent_rate" property="startRentRate" />
        <result column="end_rent_rate" property="endRentRate" />
        <result column="start_setup_rate" property="startSetupRate" />
        <result column="end_setup_rate" property="endSetupRate" />
        <result column="start_serv_rent_rate" property="startServRentRate" />
        <result column="end_serv_rent_rate" property="endServRentRate" />
        <result column="start_serv_once_rate" property="startServOnceRate" />
        <result column="end_serv_once_rate" property="endServOnceRate" />
        <result column="settlemonth" property="settlemonth" />
    </resultMap>
    <insert id="insertBatch">
        insert into stl_baseinfo_srv6
        (pospecnumber,pospecname,sospecnumber,sospecname,soid,customernumber,start_prov_nm,start_prov,start_city,manager_nm,manager_con,end_prov_nm,end_prov,end_city,end_distr,cp_nm,cp_con,bandwidth,address,end_rent_rate,end_setup_rate,end_serv_rent_rate,end_serv_once_rate,settlemonth)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.pospecnumber},
            #{item.pospecname},
            #{item.sospecnumber},
            #{item.sospecname},
            #{item.soid},
            #{item.customernumber},
            #{item.startProvNm},
            #{item.startProv},
            #{item.startCity},
            #{item.managerNm},
            #{item.managerCon},
            #{item.endProvNm},
            #{item.endProv},
            #{item.endCity},
            #{item.endDistr},
            #{item.cpNm},
            #{item.cpCon},
            #{item.bandwidth},
            #{item.address},
            #{item.endRentRate},
            #{item.endSetupRate},
            #{item.endServRentRate},
            #{item.endServOnceRate},
            #{item.settlemonth}
            )
        </foreach>
    </insert>
    <delete id="deleteHis" parameterType="java.lang.String">
        delete from stl_baseinfo_srv6_his where settlemonth = #{acctMonth}
    </delete>

</mapper>
