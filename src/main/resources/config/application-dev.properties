#开启sql日志
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl
logging.level.com.baomidou.mybatisplus=DEBUG
#关闭sql打印
#mybatis-plus.configuration.log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
mybatis-plus.configuration.map-underscore-to-camel-case=true
#MyBatis Mapper 所对应的 XML 文件位置
mybatis-plus.mapper-locations=classpath:/mapper/**/*.xml


#监控端点 全部放开
management.endpoint.health.show-details=always

#springboot 2.6.* 版本 默认路径匹配策略从AntPathMatcher 更改为PathPatternParser，导致swagger启动报错，重新改为ant匹配
spring.mvc.pathmatch.matching-strategy=ant_path_matcher


## jackson
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.default-property-inclusion=NON_EMPTY

# 开启优雅停机
server.shutdown=graceful
# 优雅停机宽限期时间
spring.lifecycle.timeout-per-shutdown-phase=20s


app.name=test3


swagger.enable-basic-auth=false


datasource.url.suffix=useSSL=false&useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true&allowMultiQueries=true&useOldAliasMetadataBehavior=true
datasource.url.prefix=***************************
datasource.password=ENC(zH+L0exfccIGRdj/AV0fTdswXk5CeCor)
#datasource.password=ENC(HxZ2cAo5mWmy5lemsqEYZLf+odgzBgxeYvzVUHBd7lw=)
bill.datasource.url.prefix=***************************

#datasource.username=greatdb
datasource.username=root

datasource.connection-timeout=300000
datasource.max-pool-size=10
datasource.idle-timeout=600000
datasource.max-life-time=1800000

jdbc.bossacctserver.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.bossacctServer.url=${bill.datasource.url.prefix}/stludr?${datasource.url.suffix}
jdbc.bossacctserver.username=${datasource.username}
jdbc.bossacctserver.password=${datasource.password}


# 数据库连接配置
jdbc.sttleserver.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.sttleserver.url=${datasource.url.prefix}/stluser?${datasource.url.suffix}
jdbc.sttleserver.username=${datasource.username}
jdbc.sttleserver.password=${datasource.password}


jdbc.stludrserver.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.stludrserver.url=${datasource.url.prefix}/stludr?${datasource.url.suffix}
jdbc.stludrserver.username=${datasource.username}
jdbc.stludrserver.password=${datasource.password}


jdbc.ds1.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.ds1.url=${datasource.url.prefix}/stludr?${datasource.url.suffix}
jdbc.ds1.username=${datasource.username}
jdbc.ds1.password=${datasource.password}

jdbc.stlusersserver.driver-class-name=com.mysql.cj.jdbc.Driver
jdbc.stlusersserver.url=${datasource.url.prefix}/stlusers?${datasource.url.suffix}
jdbc.stlusersserver.username=${datasource.username}
jdbc.stlusersserver.password=${datasource.password}

jdbc.sttlbizserver.driver-class-name=com.mysql.cj.jdbc.Driver
jdbc.sttlbizserver.url=${datasource.url.prefix}/settbiz?${datasource.url.suffix}
jdbc.sttlbizserver.username=${datasource.username}
jdbc.sttlbizserver.password=${datasource.password}


settleserver.COMMIT_COUNT=1000
settleserver.dataPath=/home/<USER>/bl1/data/SettleServer/data
settleserver.backupPath=/home/<USER>/bl1/data/SettleServer/backupData
settleserver.ex_orig_call_center=settle_orig_ccdc_YYYYMM.txt
settleserver.ex_orig_inter_line=settle_orig_apdl_internet_YYYYMM.txt
settleserver.ex_orig_line=settle_orig_apdl_data_YYYYMM.txt
settleserver.ex_inter_ent_tv=settle_inter_ent_tv_YYYYMM.txt
settleserver.confTable=/home/<USER>/bl1/data/SettleServer/tablePath


# FTP服务器配置
ftp.os.host=************
ftp.os.port=12002
ftp.os.username=BBSS
#SFTP_BBSs_123!
ftp.os.password=ENC(yad/jzwmJLHHTWYkD/QWIvep7W7w+o+Ev5B/a+qoc68=)
ftp.os.remoteUploadPath.CARD=/incoming/bbssdcsy/CARD
ftp.os.remoteDownloadPath.CARD=/outgoing/bbssdcsy/CARD
ftp.os.tempDir=/home/<USER>/bl1/data/Cmiot/cardTemp
ftp.os.bakDir=/home/<USER>/bl1/data/Cmiot/cardTemp_bak
ftp.os.feedBackDir=/home/<USER>/bl1/data/Cmiot/cardTemp_feedBack


### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
### 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。
# 执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.enabled=true
xxl.job.admin.addresses=http://127.0.0.1:9098/boss-service-taskadm
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=${spring.application.name}
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；
# 地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=0
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=
### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath=${log.path}/xxl-job/
### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
xxl.job.executor.logretentiondays=365

#################业务配置区域  start ########################

mnpserver.base-path=D:\\billing\\settle\\workspace
#mnp配置 CRM给结算文件
mnpserver.data-path=${mnpserver.base-path}/data/MNPSerer/data
mnpserver.backup-path=${mnpserver.base-path}/data/MNPSerer/backupData
mnpserver.error-path=${mnpserver.base-path}/data/MNPSerer/error
mnpserver.table-path=${mnpserver.base-path}/data/MNPSerer/conf/table

mnpserver.tables[0].file-type=INTERNET
mnpserver.tables[0].table-name=stl_baseinfo_internet
mnpserver.tables[0].table-desc=互联网基础信息
mnpserver.tables[0].column-name=POSPECNUMBER ,POSPECNAME ,SOSPECNUMBER ,SOSPECNAME ,SOID ,CUSTOMERNUMBER ,START_PROV_NM ,START_PROV ,START_CITY ,MANAGER_NM ,MANAGER_CON ,END_PROV_NM ,END_PROV ,END_CITY ,END_DISTR ,ADDRESS ,CP_NM ,CP_CON ,BANDWIDTH ,END_FUNC_RATE ,END_IP_RATE ,END_SETUP_RATE ,END_SLA_RATE,SETTLEMONTH
mnpserver.tables[0].file-regex=stl_baseinfo_internet_[0-9]{6}.[0-9]+

mnpserver.tables[1].file-type=VPN
mnpserver.tables[1].table-name=stl_baseinfo_vpn
mnpserver.tables[1].table-desc=VPN基础信息
mnpserver.tables[1].column-name=POSPECNUMBER ,POSPECNAME ,SOSPECNUMBER ,SOSPECNAME ,SOID ,CUSTOMERNUMBER ,START_PROV_NM ,START_PROV ,START_CITY ,MANAGER_NM ,MANAGER_CON ,END_PROV_NM ,END_PROV ,END_CITY ,END_DISTR ,CP_NM ,CP_CON ,BANDWIDTH ,END_ELE_RENT_RATE ,END_PE_RENT_RATE ,END_SETUP_RATE ,END_SERV_RENT_RATE ,END_SERV_ONCE_RATE ,ADDRESS,SETTLEMONTH
mnpserver.tables[1].file-regex=stl_baseinfo_vpn_[0-9]{6}.[0-9]+

mnpserver.tables[2].file-type=LINE
mnpserver.tables[2].table-name=stl_baseinfo_line
mnpserver.tables[2].table-desc=专线基础信息
mnpserver.tables[2].column-name=POSPECNUMBER ,POSPECNAME ,SOSPECNUMBER ,SOSPECNAME ,SOID ,CUSTOMERNUMBER ,START_PROV_NM ,START_PROV ,START_CITY ,MANAGER_NM ,MANAGER_CON ,A_PROV_NM ,A_PROV ,A_CITY ,A_DISTR ,A_ADDRESS ,A_CP_NM ,A_CP_CON ,Z_PROV_NM ,Z_PROV ,Z_CITY ,Z_DISTR ,Z_ADDRESS ,Z_CP_NM ,Z_CP_CON ,BANDWIDTH ,A_FUNC_RATE ,Z_FUNC_RATE ,A_SETUP_RATE ,Z_SETUP_RATE ,SETTLEMONTH
mnpserver.tables[2].file-regex=stl_baseinfo_line_[0-9]{6}.[0-9]+

mnpserver.tables[3].file-type=SRV6
mnpserver.tables[3].table-name=stl_baseinfo_srv6
mnpserver.tables[3].table-desc=srv6专线基础信息
mnpserver.tables[3].column-name=POSPECNUMBER,POSPECNAME,SOSPECNUMBER,SOSPECNAME,SOID,CUSTOMERNUMBER,START_PROV_NM,START_PROV,START_CITY,MANAGER_NM,MANAGER_CON,END_PROV_NM,END_PROV,END_CITY,END_DISTR,CP_NM,CP_CON,BANDWIDTH,END_RENT_RATE,END_SETUP_RATE,END_SERV_RENT_RATE,END_SERV_ONCE_RATE,ADDRESS,SETTLEMONTH
mnpserver.tables[3].file-regex=stl_baseinfo_srv6_[0-9]{6}.[0-9]+


word.config.upload-path=${base-path}/SettleData/upload/$<dateTime>/
word.config.data-path=${base-path}/SettleData/结算说明$<dateTime>/
word.config.base-data-path=${base-path}/SettleData/结算说明$<dateTime>/结算说明$<dateTime>/
word.config.cw-data-path=${base-path}/SettleData/结算说明$<dateTime>/结算说明$<dateTime>/财务公司报表/MAS/
word.config.zq-data-path=${base-path}/SettleData/结算说明$<dateTime>/结算说明$<dateTime>/政企公司财务报表/MAS/
word.config.file-zip-path=${base-path}/SettleData/zipFile/结算说明202405.zip
#邮箱配置地址
mail.config.remote-url=http://abs-svc-send.bboss-test:9262/mail/send
mail.config.file-path=/settle-service-tools
mail.config.file-name=结算说明$<dateTime>.zip
mail.config.to-email=<EMAIL>

sftp.config.ip=**************
sftp.config.port=22
sftp.config.username=appop
sftp.config.password=Paas_inf_Aqzzkw_123!
sftp.config.remotePath= /home/<USER>/settle/bl0/data/结算说明202405.zip
#平台结算文件配置
#基础目录
base-path=D:\\billing\\settle\\workspace

#iot
iot.config.valid-name=IOTSC_SETT_[0-9]{6}\.[0-9]{3}
iot.config.check-name=VERF_IOTSC_SETT_[0-9]{6}\.[0-9]{3}
iot.config.data-path=${base-path}/Cmiot/data
iot.config.backup-path=${base-path}/Cmiot/backupData
iot.config.error-path=${base-path}/Cmiot/error
iot.config.paxc-valid-name=PlatformBill_PAXC_[0-9]{6}\.[0-9]{3}

#cmiot CMIOT_SETT_[0-9]{6}\.[0-9]{3}.?
#cmiot.config.valid-name=CMIOT_SETT_[0-9]{6}\.[0-9]{3}\.[a-z]{3}
cmiot.config.valid-name=CMIOT_SETT_[0-9]{6}\.[0-9]{3}.?
cmiot.config.data-path=${base-path}/Cmiot/data
cmiot.config.backup-path=${base-path}/Cmiot/backupData
cmiot.config.error-path=${base-path}/Cmiot/error

#mc 4.16
mc.config.valid-name=EBOSS_BILL_BBOSS_M_DETAIL_[0-9]{6}\.[0-9]{3}
mc.config.data-path=${base-path}/mc/data
mc.config.backup-path=${base-path}/mc/backupData
mc.config.error-path=${base-path}/mc/error
mc.config.resp-path=${base-path}/mc/resp
mc.config.conf=${base-path}/mc/conf
mc.config.table=${base-path}/mc/conf/table
mc.config.txt-path=${base-path}/mc/txt
mc.config.xsd=conf/xmlcfg/xsd/mc.xsd
mc.config.xslt=conf/xmlcfg/xslt/mc.xslt

#membership 成员权益文件
member.config.valid-name=MembershipInterests_[0-9]{8}\.csv
member.config.data-path=${base-path}/Membership/data
member.config.backup-path=${base-path}/Membership/backupData
member.config.error-path=${base-path}/Membership/error
member.config.resp-path=${base-path}/Membership/resp

#bc2c 4.31
bc.config.valid-name=EBOSS_BC2C_BBOSS_M_DETAIL_[0-9]{6}\.[0-9]{4}
bc.config.data-path=${base-path}/BC2C/data/
bc.config.backup-path=${base-path}/BC2C/backupData/
bc.config.error-path=${base-path}/BC2C/error/
bc.config.resp-path=${base-path}/BC2C/resp/

#pvbill 4.29
pvbill.config.valid-name=EBOSS_PVBILL_BBOSS_M_DETAIL_[0-9]{6}\.[0-9]{4}
pvbill.config.data-path=${base-path}/PVBill/data/
pvbill.config.backup-path=${base-path}/PVBill/backupData/
pvbill.config.error-path=${base-path}/PVBill/error/
pvbill.config.resp-path=${base-path}/PVBill/resp/
pvbill.config.txt-path=${base-path}/PVBill/txt/
pvbill.config.xsd=conf/xmlcfg/xsd/PVBill.xsd
pvbill.config.xslt=conf/xmlcfg/xslt/PVBill.xslt

#pvsettle 4.30
pvsettle.config.valid-name=EBOSS_PVSETTLE_BBOSS_M_DETAIL_[0-9]{6}\.[0-9]{4}
pvsettle.config.data-path=${base-path}/PVSettle/data/
pvsettle.config.backup-path=${base-path}/PVSettle/backupData/
pvsettle.config.error-path=${base-path}/PVSettle/error/
pvsettle.config.resp-path=${base-path}/PVSettle/resp/
pvsettle.config.txt-path=${base-path}/PVSettle/txt/
pvsettle.config.xsd=conf/xmlcfg/xsd/PVSettle.xsd
pvsettle.config.xslt=conf/xmlcfg/xslt/PVSettle.xslt
pvsettle.config.batch-size=1000

#espacc 4.6
espacc.config.valid-name=ESP_ACC_E_BILL_[2-3]_[0-9]{6}_.{3,4}\.[0-9]{4}
espacc.config.data-path=${base-path}/ESPACC/data/
espacc.config.backup-path=${base-path}/ESPACC/backupData/
espacc.config.error-path=${base-path}/ESPACC/error/
espacc.config.resp-path=${base-path}/ESPACC/resp/
espacc.config.txt-path=${base-path}/ESPACC/txt/
espacc.config.xsd=conf/xmlcfg/xsd/esp.xsd
espacc.config.xslt=conf/xmlcfg/xslt/esp.xslt

#大数据平台 文件 75824接口
bigdata.config.file-reg=^a_10000_HDO_75824_[0-9]{6}_\\d{2}_\\d{3}\\.dat\\.gz
bigdata.config.data-path=${base-path}/Bigdata/data/
bigdata.config.back-path=${base-path}/Bigdata/backupData/
bigdata.config.err-path=${base-path}/Bigdata/error/
bigdata.config.resp-path=${base-path}/Bigdata/resp/
bigdata.config.resp-file-format=PrePaymentSettlementStatement_{yyyymm}.csv

#请求报表服务地址
report.config.remote-url=http://settle-service-report.bboss-settle:9232/settleReport
#请求超时时间，单位毫秒
report.config.timeout=60000
#erp 报表服务地址
report.config.erp-uri=/erpService
#暂估/销暂估报表服务地址
report.config.prediction-uri=/exportRaq
#报表批量下载清理地址
report.config.evict-uri=/api/evictReportBatch
#erp报表文件路径
report.config.erp-file-path=/home/<USER>/SettleFile/ERP_Files

cdn-base-path=/home/<USER>/bl1
cdn2eboss.config.group=CDN2EBOSS
cdn2eboss.config.action=db2csv
cdn2eboss.config.delimiter=##
cdn2eboss.config.batchSize=60000
cdn2eboss.config.fileOutPath=${cdn-base-path}/servs/bboss_batch/outgoing
cdn2eboss.config.fileNameReg=CDN_INCOMING_INFO_{yyyyMM}.dat
cdn2eboss.config.exportSql= SELECT /*+ parallel(t1 ,4)(t2 ,4) */ DISTINCT t1.eboss_customer_code, IFNULL(NULLIF(t1.customer_code, ''), ' '), IFNULL(NULLIF(t1.customer_name, ''), ' ') , t1.prov_code, IFNULL(NULLIF(t1.prod_order_id, ''), t1.order_id), t1.order_id, IFNULL(NULLIF(t1.product_code, ''), t1.service_code), IFNULL(NULLIF(t1.product_name, ''), t1.service_name), t1.service_code, t1.service_name, t1.acct_month, t1.amount, IFNULL(NULLIF(t1.adjust_acct_month, ''), ' '), IFNULL(NULLIF(t1.adjust_amount, ''), '0'), t1.tax_rate, t2.in_object, t2.settle_notaxfee_sum * 10, ROUND(t2.settle_notaxfee_sum * 10 / (1 + t1.tax_rate / 100)) FROM stludr.cust_prod_info t1 LEFT JOIN (SELECT customer_code, out_object, offer_order_id, product_order_id, offer_code, product_code, settle_month, in_object, SUM(settle_notaxfee) settle_notaxfee_sum FROM stludr.ur_recv_{yyyyMM}_t WHERE feetype = 0 AND dest_source = '98' and  rule_id not in (667,666,668)  GROUP BY customer_code, out_object, offer_order_id, product_order_id, offer_code, product_code, settle_month, in_object) t2 ON t1.eboss_customer_code = t2.customer_code AND t1.prov_code = t2.out_object AND t1.prod_order_id = t2.offer_order_id AND t1.order_id = t2.product_order_id AND t1.product_code = t2.offer_code AND t1.service_code = t2.product_code AND t1.acct_month = t2.settle_month WHERE t1.acct_month = {yyyyMM} AND t1.service_code = '9200397' AND t2.product_order_id IS NOT NULL
# 是否在日志中显示执行的SQL，默认false
cdn2eboss.config.showSql = true
# 是否格式化显示的SQL，默认false
cdn2eboss.config.formatSql = true
# 是否显示SQL参数，默认false
cdn2eboss.config.showParams = true
# 打印SQL的日志等级，默认debug
cdn2eboss.config.sqlLevel = info

eboss2settle.config.group=hwcdn
eboss2settle.config.action = csv2db
eboss2settle.config.delimiter = ##
eboss2settle.config.batchSize = 60000
eboss2settle.config.fileInPath = ${cdn-base-path}/servs/bboss_batch/incoming
eboss2settle.config.workPath = ${cdn-base-path}/servs/bboss_batch/work/
eboss2settle.config.errorPath =${cdn-base-path}/servs/bboss_batch/error/
eboss2settle.config.fileBakPath = ${cdn-base-path}/servs/bboss_batch/backup/{yyyy-MM-dd}/
eboss2settle.config.fileNameReg = CUST_PROD_INFO_\\d{6}.dat
eboss2settle.config.tableName = cust_prod_info
eboss2settle.config.tableFields = eboss_customer_code ,customer_code ,customer_name ,prov_code ,prod_order_id ,order_id ,product_code ,product_name ,service_code ,service_name ,acct_month ,amount ,adjust_acct_month ,adjust_amount ,tax_rate ,customer_type


#EBOSS 4.30接口文件处理线程配置
app.config.EBoss-files[0].biz-code=biz_4_30
app.config.EBoss-files[0].file-thread-num=30
app.config.EBoss-files[0].process-thread-num=30
##################业务配置区域 end ########################


spring.security.user.name= settle
#1qaz1qaz!QAZ
spring.security.user.password=ENC(z0gUxZ6u3XxzwrA1zoPtJ7a4L+0T0YlH0npb2YpbT6Y=)

security.whitelist[0]=/business/**
security.blacklist[0]=/actuator/**
#security.blacklist[1]=/druid/**
#security.blacklist[2]=/swagger/**
#security.blacklist[3]=/swagger-ui.html
#security.blacklist[4]=/swagger-resources/**
#security.blacklist[5]=/v2/api-docs
#security.blacklist[6]=/v3/api-docs/**
#security.blacklist[7]=/swagger-ui/**
#security.blacklist[8]=/api/swagger-ui.html
#security.blacklist[9]=csp/gateway/slc/api/swagger-ui.html
#security.blacklist[10]=/admin/**
#security.blacklist[11]=/eureka/apps
#security.blacklist[12]=/instances/**
#security.blacklist[13]=/redoc/**
#security.blacklist[14]=/features
#security.blacklist[15]=/health
#security.blacklist[16]=/info
#security.blacklist[17]=/services

log.path=D:\\billing\\settle\\workspace
log.lever=DEBUG

card.tempDir=/home/<USER>/bl1/data/Cmiot/cardTemp
card.bakDir=/home/<USER>/bl1/data/Cmiot/cardTemp_bak
card.feedBackDir=/home/<USER>/bl1/data/Cmiot/cardTemp_feedBack


# 带宽型文件获取
ftp.ht.tempDir=/home/<USER>/bl1/data/Cmiot/bigDataTemp
ftp.ht.gzBakDir=/home/<USER>/bl1/data/Cmiot/bigDataGzTemp_bak
ftp.ht.bakDir=/home/<USER>/bl1/data/Cmiot/bigDataTemp_bak
ftp.ht.feedBackDir=/home/<USER>/bl1/data/Cmiot/bigDataTemp_feedBack

clear.log.tables[0].db-name=stludr
clear.log.tables[0].table-name=udr2mq_log
clear.log.tables[0].partition-type=day

minio.endpoint="192.168.134.146"