package com.settle.server.module.cdn.flatfile;

import cn.hutool.core.util.StrUtil;
import com.settle.server.module.cdn.reader.AbstractFileRecordReader;
import com.settle.server.module.cdn.record.Header;
import com.settle.server.module.cdn.record.StringRecord;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.time.LocalDateTime;

/**
 * 从平面文件读取数据的 {@link AbstractFileRecordReader} 实现
 * 此读取器生成 {@link StringRecord} 实例。
 *
 * <AUTHOR>
 */
public class FlatFileRecordReader extends AbstractFileRecordReader<String> {

    private BufferedReader bufferedReader;
    private long currentRecordNumber;

    /**
     * 创建一个新的 {@link FlatFileRecordReader}
     *
     * @param path 要从中读取记录的文件路径
     */
    public FlatFileRecordReader(final Path path) {
        this(path, Charset.defaultCharset());
    }

    /**
     * 创建一个新的 {@link FlatFileRecordReader}
     *
     * @param path    要从中读取记录的文件路径
     * @param charset 输入文件的字符集
     */
    public FlatFileRecordReader(final Path path, final Charset charset) {
        super(path, charset);
    }

    @Override
    public StringRecord readRecord() throws IOException {
        Header header = new Header(++currentRecordNumber, getDataSourceName(), LocalDateTime.now());
        String line = bufferedReader.readLine();
        if (StrUtil.isNotBlank(line)) {
            return new StringRecord(header, line);
        } else {
            return null;
        }
    }

    @Override
    public void open() throws Exception {
        currentRecordNumber = 0;
        bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(path.toFile()), charset));
    }

    @Override
    public void close() throws IOException {
        if (bufferedReader != null) {
            bufferedReader.close();
        }
    }

    private String getDataSourceName() {
        return path.toAbsolutePath().toString();
    }

}
