package com.settle.server.module.mnp.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.mnp.StlBaseinfoVpnMapper;
import com.settle.server.entity.mnp.StlBaseinfoVpn;
import com.settle.server.utils.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service("stlBaseinfoVpnService")
@Slf4j
public class StlBaseinfoVpnService extends ServiceImpl<StlBaseinfoVpnMapper, StlBaseinfoVpn> implements StlBaseService{

    @Override
    public void deleteHis(String acctMonth) {
        getBaseMapper().deleteHis(acctMonth);
    }

    @Override
    public void delete(String acctMonth) {
        getBaseMapper().delete(new LambdaQueryWrapper<>(StlBaseinfoVpn.class)
                .eq(StlBaseinfoVpn::getSettlemonth, acctMonth));
    }

    /**
     * 批量插入

     */
    @Override
    public void insertBatch(List<String[]> contents, String acctMonth) {
        List<StlBaseinfoVpn> lists = Lists.newArrayList();
        for (String[] lines : contents) {
            StlBaseinfoVpn one = getStlBaseinfoVpn(acctMonth, lines);
            ObjectUtils.replaceEmptyStringWithNull(one);
            lists.add(one);
        }
        Lists.partition(lists, 1000).forEach(part -> {
            getBaseMapper().insertBatch(part);
            log.debug("insertBatch size:{}", part.size());
        });
    }

    /**
     POSPECNUMBER ,POSPECNAME ,SOSPECNUMBER ,SOSPECNAME ,SOID ,CUSTOMERNUMBER ,START_PROV_NM ,START_PROV ,START_CITY ,MANAGER_NM ,MANAGER_CON ,
     END_PROV_NM ,END_PROV ,END_CITY ,END_DISTR ,CP_NM ,CP_CON ,BANDWIDTH ,END_ELE_RENT_RATE ,END_PE_RENT_RATE ,END_SETUP_RATE ,END_SERV_RENT_RATE ,
     END_SERV_ONCE_RATE ,END_RENT_RATE, ADDRESS,SETTLEMONTH
     */
    private StlBaseinfoVpn getStlBaseinfoVpn(String acctMonth, String[] lines) {
        StlBaseinfoVpn one = new StlBaseinfoVpn();
        one.setSettlemonth(acctMonth);
        one.setPospecnumber(lines[0]);
        one.setPospecname(lines[1]);
        one.setSospecnumber(lines[2]);
        one.setSospecname(lines[3]);
        one.setSoid(lines[4]);
        one.setCustomernumber(lines[5]);
        one.setStartProvNm(lines[6]);
        one.setStartProv(lines[7]);
        one.setStartCity(lines[8]);
        one.setManagerNm(lines[9]);
        one.setManagerCon(lines[10]);
        one.setEndProvNm(lines[11]);
        one.setEndProv(lines[12]);
        one.setEndCity(lines[13]);
        one.setEndDistr(lines[14]);
        one.setCpNm(lines[15]);
        one.setCpCon(lines[16]);
        one.setBandwidth(lines[17]);
        one.setEndEleRentRate(lines[18]);
        one.setEndPeRentRate(lines[19]);
        one.setEndSetupRate(lines[20]);
        one.setEndServRentRate(lines[21]);
        one.setEndServOnceRate(lines[22]);
        one.setEndRentRate(lines[23]);
        one.setAddress(lines[24]);
        return one;
    }
}
