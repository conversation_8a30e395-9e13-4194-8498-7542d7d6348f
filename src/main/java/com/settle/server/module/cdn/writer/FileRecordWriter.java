package com.settle.server.module.cdn.writer;

import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;
import com.settle.server.utils.Utils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.nio.file.Path;

/**
 * 将记录写入文件的写入器
 *
 * <AUTHOR>
 */
public class FileRecordWriter implements RecordWriter<String> {

    private HeaderCallback headerCallback;
    private FooterCallback footerCallback;
    private Charset charset = Charset.defaultCharset();
    private String lineSeparator = Utils.LINE_SEPARATOR;
    private boolean append;
    private OutputStreamWriter outputStreamWriter;
    private Path path;

    /**
     * 创建一个新的 {@link FileRecordWriter}
     *
     * @param path 输出文件
     */
    public FileRecordWriter(final Path path) {
        this.path = path;
    }

    /**
     * 设置输入文件的字符集
     *
     * @param charset 输入文件的字符集
     */
    public void setCharset(Charset charset) {
        this.charset = charset;
    }

    /**
     * 设置行分隔符
     *
     * @param lineSeparator 使用
     */
    public void setLineSeparator(String lineSeparator) {
        this.lineSeparator = lineSeparator;
    }

    /**
     * 设置标头回调
     *
     * @param headerCallback 设置
     */
    public void setHeaderCallback(HeaderCallback headerCallback) {
        this.headerCallback = headerCallback;
    }

    /**
     * 设置页脚回调
     *
     * @param footerCallback 设置
     */
    public void setFooterCallback(FooterCallback footerCallback) {
        this.footerCallback = footerCallback;
    }

    /**
     * 在追加模式下打开写入器的参数
     *
     * @param append 如果写入器应以追加模式打开，则为 true
     */
    public void setAppend(boolean append) {
        this.append = append;
    }

    @Override
    public void open() throws Exception {
        outputStreamWriter = new OutputStreamWriter(new FileOutputStream(path.toFile(), append), charset);
        if (headerCallback != null) {
            headerCallback.writeHeader(outputStreamWriter);
            outputStreamWriter.write(lineSeparator);
            outputStreamWriter.flush();
        }
    }

    @Override
    public void writeRecords(Batch<String> batch) throws Exception {
        for (Record<String> record : batch) {
            outputStreamWriter.write(record.getPayload());
            outputStreamWriter.write(lineSeparator);
        }
        outputStreamWriter.flush();
    }

    @Override
    public void close() throws Exception {
        if (footerCallback != null && outputStreamWriter != null) {
            footerCallback.writeFooter(outputStreamWriter);
            outputStreamWriter.write(lineSeparator);
            outputStreamWriter.flush();
        }
        if (outputStreamWriter != null) {
            outputStreamWriter.close();
        }
    }

    /**
     * 回调以将标头写入输出文件
     * 实现不需要刷新写入器或在标头末尾写入行分隔符
     */
    public interface HeaderCallback {
        /**
         * 写标头
         *
         * @param writer 写入器
         * @throws IOException IO异常
         */
        void writeHeader(OutputStreamWriter writer) throws IOException;
    }

    /**
     * 回调以将页脚写入输出文件
     * 实现不需要刷新编写器或在页脚末尾写入行分隔符
     */
    public interface FooterCallback {
        /**
         * 写页脚
         *
         * @param writer 写入器
         * @throws IOException IO异常
         */
        void writeFooter(OutputStreamWriter writer) throws IOException;
    }
}
