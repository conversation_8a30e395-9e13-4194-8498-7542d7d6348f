package com.settle.server.datasource;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.io.IOException;

/**
 * @Classname stlusersConfig
 * @Description TODO
 * @Date 2023/4/13 20:19
 * <AUTHOR>
 * @Version 1.0.0
 */
@Configuration
@MapperScan(basePackages = "com.settle.server.dao.stlusers", sqlSessionTemplateRef = "stlusersSqlSessionTemplate")
public class StlusersConfig {
    @Value("${datasource.connection-timeout}")
    private int connectionTimeout;
    @Value("${datasource.max-pool-size}")
    private int maxPoolSize;
    @Value("${datasource.idle-timeout}")
    private int idleTimeout;
    @Value("${datasource.max-life-time}")
    private int maxLifeTime;

    private static String MYBATIS_CONFIG = "mybatis-config.xml";

    private static final String MAPPER_LOCATION = "classpath:/mapper/**/*.xml";


    /**
     * 主数据源配置stlusers数据源
     *
     * @return
     */
    @Bean(name = "stlusersDataSourceProperties")
    @ConfigurationProperties(prefix = "jdbc.stlusersserver")
    public DataSourceProperties dataSourceProperties() {
        return new DataSourceProperties();
    }
    /**
     * 主数据源 stlusers数据源
     * @param dataSourceProperties
     * @return
     */
    @Bean(name = "stlusersDataSource")
    public DataSource stlusersDataSource(@Qualifier("stlusersDataSourceProperties") DataSourceProperties dataSourceProperties) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setPoolName("stlusersPool");
        hikariConfig.setJdbcUrl(dataSourceProperties.getUrl());
        hikariConfig.setDriverClassName(dataSourceProperties.getDriverClassName());
        hikariConfig.setUsername(dataSourceProperties.getUsername());
        hikariConfig.setPassword(dataSourceProperties.getPassword());
        // 设置连接池参数
        hikariConfig.setMaximumPoolSize(maxPoolSize);
        hikariConfig.setMinimumIdle(maxPoolSize);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifeTime);
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setConnectionInitSql("select 1");
        hikariConfig.setConnectionTestQuery("select 'x'");
        return new HikariDataSource(hikariConfig);
    }
    @Bean("stlusersSqlSessionFactory")
    public MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean(@Qualifier("stlusersDataSource") DataSource dataSource) throws IOException {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setConfigLocation(new ClassPathResource(MYBATIS_CONFIG));
        mybatisPlus.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(StlusersConfig.MAPPER_LOCATION));
        return mybatisPlus;
    }

    @Bean(name = "stlusersTransactionManager")
    public DataSourceTransactionManager stlusersTransactionManager(@Qualifier("stlusersDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "stlusersSqlSessionTemplate")
    public SqlSessionTemplate stlusersSqlSessionTemplate(@Qualifier("stlusersSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
