package com.settle.server.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.settle.server.module.report.dto.ReportDTO;
import com.settle.server.module.report.service.ReportFileBeanFactory;
import com.settle.server.module.report.service.ReportFileService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 〈erp 报文文件生成〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/9
 * @since 1.0.0
 */
@Slf4j
@Component
public class ReportFileJob {

    @Autowired
    private ReportFileBeanFactory reportFileBeanFactory;
    @XxlJob("erpReport2txtHandler")
    public ReturnT<String> erpReport2txtHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("ERP文件生成处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            String rKey = entries.getStr("R_KEY");
            if (StringUtils.isBlank(rKey)) {
                rKey = "10047";
            }
            List<String> signList = Lists.newArrayList("0117", "0118", "0317", "0318");
            String signs = entries.getStr("sign");
            if (StringUtils.isNotBlank(signs)) {
                signList = Arrays.stream(signs.split(",")).map(String::trim).collect(Collectors.toList());
            }
            XxlJobHelper.log("当前账期：{},R_KEY:{},sign:{}", acctMonth, rKey, signList);
            ReportFileService reportFileService = reportFileBeanFactory.getReportFileService(ReportFileBeanFactory.ReportFileType.ERP);
            if (reportFileService == null) {
                XxlJobHelper.log("未找到对应的报表文件生成服务");
                return ReturnT.FAIL;
            }
            String finalRKey = rKey;
            String finalAcctMonth = acctMonth;
            List<ReportDTO> reportDTOList = signList.stream().map(sign -> {
                ReportDTO reportDTO = new ReportDTO();
                reportDTO.setSettleMonth(finalAcctMonth);
                reportDTO.setRKey(finalRKey);
                reportDTO.setSign(sign);
                return reportDTO;
            }).collect(Collectors.toList());

            String filePath = reportFileService.reportFile(reportDTOList);
            XxlJobHelper.log("ERP文件生成路径：{}", filePath);
            XxlJobHelper.log("ERP文件生成处理结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("ERP文件生成处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }


    // http://localhost:9232/exportRaq?settlemonth=202310&raqType=1
    @XxlJob("predictionReportHandler")
    public ReturnT<String> predictionReportHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("暂估/销暂估报表生成处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            String raqType = entries.getStr("raqType");
            if (StringUtils.isBlank(raqType)) {
                raqType = "1";
            }
            String raqKey = entries.getStr("raqKey");

            XxlJobHelper.log("当前账期：{},raqType:{}", acctMonth, raqType);

            ReportFileService reportFileService = reportFileBeanFactory.getReportFileService(ReportFileBeanFactory.ReportFileType.PREDICTION);
            if (reportFileService == null) {
                XxlJobHelper.log("未找到对应的报表文件生成服务");
                return ReturnT.FAIL;
            }

            List<ReportDTO> reportDTOList = Lists.newArrayList();
            ReportDTO reportDTO = new ReportDTO();
            reportDTO.setSettleMonth(acctMonth);
            reportDTO.setRaqType(raqType);
            reportDTO.setRaqKey(raqKey);
            reportDTOList.add(reportDTO);

            String filePath = reportFileService.reportFile(reportDTOList);
            XxlJobHelper.log("暂估/销暂估报表路径：{}", filePath);
            XxlJobHelper.log("暂估/销暂估报表处理结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("暂估/销暂估报表处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    //curl -X POST http://127.0.0.1:9232/settleReport/api/evictReportBatch
    @XxlJob("evictReportBatch")
    public ReturnT<String> evictReportBatch() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("报表批量下载清理->生成，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                // 当前月
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            String raqList = entries.getStr("raqList");
            if (StringUtils.isBlank(raqList)) {
                raqList = "10001,10002,10007,10008,10009,10010,10021,10022,10019,10020,10163,10164,10176,10180,10036,10037,10069,10178,10179";
            }
            XxlJobHelper.log("当前账期：{},raqList:{}", acctMonth, raqList);

            ReportFileService reportFileService = reportFileBeanFactory.getReportFileService(ReportFileBeanFactory.ReportFileType.EVICTREPORT);
            if (reportFileService == null) {
                XxlJobHelper.log("未找到对应的报表文件生成服务");
                return ReturnT.FAIL;
            }

            List<ReportDTO> reportDTOList = Lists.newArrayList();
            ReportDTO reportDTO = new ReportDTO();
            reportDTO.setSettleMonth(acctMonth);
            reportDTO.setRaqKey(raqList);
            reportDTOList.add(reportDTO);

            String data = reportFileService.reportFile(reportDTOList);
            XxlJobHelper.log("报表批量下载清理->生成：{}", data);
            XxlJobHelper.log("报表批量下载清理->生成.结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("报表批量下载清理->生成.失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

}