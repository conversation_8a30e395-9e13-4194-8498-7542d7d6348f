package com.settle.server.schedule;

import com.settle.server.service.RptP2cLimitedSpecialService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定时处理卡平台文件
 * <AUTHOR>
 */
@Component
@Slf4j
public class RptP2cLimitedSpecialJob {
    @Autowired
    private RptP2cLimitedSpecialService rptP2cLimitedSpecialService;

    @XxlJob("rptP2cLimitedSpecial")
    public ReturnT<String> handle() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("开始进行202412账期数据扣减3%特殊处理操作");
            rptP2cLimitedSpecialService.processData();
            XxlJobHelper.log("数据处理结束");
        }catch (RuntimeException re){
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("数据处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }


}
