package com.settle.server.xmlTransform;

import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Properties;

@Slf4j
public class XMLTransformDBUtil {
	private static XMLTransformDBUtil instance = null;

	private static String url = null;

	private static String userName = null;

	private static String password = null;

	private static String confPath = null;

	public static String getConfPath() {
		return confPath;
	}

	public static void setConfPath(String confPath) {
		XMLTransformDBUtil.confPath = confPath;
	}

	private XMLTransformDBUtil() {
		super();
		Properties prop = null;
		FileInputStream in = null;
		if (confPath == null) {
			confPath = "XML_DB.properties";
		}
		try {
			try {
				prop = new Properties();
				in = new FileInputStream(confPath);
				prop.load(in);

				Class.forName(prop.getProperty("DBDriver"));
				url = prop.getProperty("DBurl");
				userName = prop.getProperty("userName");
				password = /*DecipherUtil.decoder(prop.getProperty("password"))*/prop.getProperty("password");

			} finally {
				if (in != null) {
					in.close();
				}
			}
		} catch (FileNotFoundException e) {
			log.error("JDBC Exception: " + e.getMessage());
		} catch (IOException e) {
			log.error("JDBC Exception: " + e.getMessage());
		} catch (ClassNotFoundException e) {
			log.error("JDBC Exception: " + e.getMessage());
		}

	}

	public static XMLTransformDBUtil getInstance() {
		if (instance == null) {
			instance = new XMLTransformDBUtil();
		}
		return instance;
	}

	public boolean saveX2TResult(String bizType, String filename,
			Long rawCount, String errCode, String errMessage, int direction, long fileByte) {

		try {
			Connection conn = null;
			PreparedStatement pstmt = null;
			try {
				DriverManager.registerDriver(new com.mysql.jdbc.Driver());
				conn = DriverManager.getConnection(url, userName, password);
				try {
					pstmt = conn
							.prepareStatement("insert into LOG_X2T_T (IOID_ID0 , CREATED_TIME, MOD_TIME, BIZ_TYPE, FILE_NAME, DIRECTION, RAW_COUNT, ERR_CODE, ERR_MESSAGE, FILE_BYTE) values(SEQ_IOID_ID.nextval, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

					Date date = new Date(Calendar.getInstance()
							.getTimeInMillis());

					pstmt.setDate(1, date);
					pstmt.setDate(2, date);
					pstmt.setString(3, bizType);
					pstmt.setString(4, filename);
					pstmt.setInt(5, direction);
					pstmt.setLong(6, rawCount.longValue());
					pstmt.setString(7, errCode);
					pstmt.setString(8, errMessage);
					pstmt.setLong(9, fileByte);

					int i = pstmt.executeUpdate();
					if (i == 0) {
						System.out.println("i == 0");
					}
				} finally {
					if (pstmt != null) {
						pstmt.close();
					}
				}
				return true;
			} finally {
				if (conn != null) {
					conn.close();
				}
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			log.error("JDBC Exception: " + e.getMessage());
			return false;
		}
	}

	/*
	 * public static void main(String[] args) { Properties prop = null; try {
	 * DriverManager.registerDriver(new oracle.jdbc.OracleDriver()); prop = new
	 * Properties();
	 * 
	 * prop.load(new FileInputStream("DB.properties"));
	 * 
	 * Class.forName(prop.getProperty("DBDriver"));
	 * 
	 * String url = prop.getProperty("DBurl"); String userName =
	 * prop.getProperty("userName"); String password =
	 * prop.getProperty("password");
	 * 
	 * Connection conn = DriverManager.getConnection(url, userName, password);
	 * PreparedStatement pstmt = conn .prepareStatement("insert into LOG_X2T_T
	 * (IOID_ID0 , CREATED_TIME, MOD_TIME, BIZ_TYPE, FILE_NAME, RAW_COUNT,
	 * ERR_CODE, ERR_MESSAGE) values(SEQ_IOID_ID.nextval, ?, ?, ?, ?, ?, ?,
	 * ?)");
	 * 
	 * Date date = new Date(Calendar.getInstance().getTimeInMillis());
	 * 
	 * pstmt.setDate(1, date); pstmt.setDate(2, date); pstmt.setString(3, "MM");
	 * pstmt.setString(4, "Test_File"); pstmt.setLong(5, 12); pstmt.setString(6,
	 * null); pstmt.setString(7, "This is a test Error!");
	 * 
	 * int i = pstmt.executeUpdate(); if (i == 0) { System.out.println("i ==
	 * 0"); }
	 *  } catch (SQLException e) { FileUtil.getLog().info("JDBC Exception: " +
	 * e.getMessage()); } catch (ClassNotFoundException e) {
	 * FileUtil.getLog().info("JDBC Exception: " + e.getMessage()); } catch
	 * (FileNotFoundException e) { FileUtil.getLog().info("JDBC Exception: " +
	 * e.getMessage()); } catch (IOException e) { FileUtil.getLog().info("JDBC
	 * Exception: " + e.getMessage()); } }
	 */
	
}
