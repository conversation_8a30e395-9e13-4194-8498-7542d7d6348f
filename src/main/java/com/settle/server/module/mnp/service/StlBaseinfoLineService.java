package com.settle.server.module.mnp.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.mnp.StlBaseinfoLineMapper;
import com.settle.server.entity.mnp.StlBaseinfoLine;
import com.settle.server.utils.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service("stlBaseinfoLineService")
@Slf4j
public class StlBaseinfoLineService extends ServiceImpl<StlBaseinfoLineMapper, StlBaseinfoLine> implements StlBaseService{

    @Autowired
    private DataSource dataSource;
    @Override
    public void deleteHis(String acctMonth) {
        getBaseMapper().deleteHis(acctMonth);
    }

    @Override
    public void delete(String acctMonth) {
        getBaseMapper().delete(new LambdaQueryWrapper<>(StlBaseinfoLine.class)
                .eq(StlBaseinfoLine::getSettlemonth, acctMonth));

    }

    /**
     * 批量插入
     */
    @Override
    public void insertBatch(List<String[]> contents, String acctMonth) {
        log.info("datasource:{}",dataSource);
        List<StlBaseinfoLine> list = Lists.newArrayList();
        for (String[] lines : contents) {
            StlBaseinfoLine one = getStlBaseinfoLine(acctMonth, lines);
            ObjectUtils.replaceEmptyStringWithNull(one);
            list.add(one);
        }
        Lists.partition(list, 1000).forEach(part -> {
            getBaseMapper().insertBatch(part);
            log.debug("insertBatch size:{}", part.size());
        });
    }

    /**
     POSPECNUMBER ,POSPECNAME ,SOSPECNUMBER ,SOSPECNAME ,SOID ,CUSTOMERNUMBER ,START_PROV_NM ,START_PROV ,START_CITY ,MANAGER_NM ,MANAGER_CON ,
     A_PROV_NM ,A_PROV ,A_CITY ,A_DISTR ,A_ADDRESS ,A_CP_NM ,A_CP_CON ,Z_PROV_NM ,Z_PROV ,Z_CITY ,Z_DISTR ,Z_ADDRESS ,Z_CP_NM ,Z_CP_CON ,
     BANDWIDTH ,A_FUNC_RATE ,Z_FUNC_RATE ,A_SETUP_RATE ,Z_SETUP_RATE ,SETTLEMONTH
     */
    private StlBaseinfoLine getStlBaseinfoLine(String acctMonth, String[] lines) {
        StlBaseinfoLine one = new StlBaseinfoLine();
        one.setSettlemonth(acctMonth);
        one.setPospecnumber(lines[0]);
        one.setPospecname(lines[1]);
        one.setSospecnumber(lines[2]);
        one.setSospecname(lines[3]);
        one.setSoid(lines[4]);
        one.setCustomernumber(lines[5]);
        one.setStartProvNm(lines[6]);
        one.setStartProv(lines[7]);
        one.setStartCity(lines[8]);
        one.setManagerNm(lines[9]);
        one.setManagerCon(lines[10]);
        one.setAProvNm(lines[11]);
        one.setAProv(lines[12]);
        one.setACity(lines[13]);
        one.setADistr(lines[14]);
        one.setAAddress(lines[15]);
        one.setACpNm(lines[16]);
        one.setACpCon(lines[17]);
        one.setZProvNm(lines[18]);
        one.setZProv(lines[19]);
        one.setZCity(lines[20]);
        one.setZDistr(lines[21]);
        one.setZAddress(lines[22]);
        one.setZCpNm(lines[23]);
        one.setZCpCon(lines[24]);
        one.setBandwidth(lines[25]);
        one.setAFuncRate(lines[26]);
        one.setZFuncRate(lines[27]);
        one.setASetupRate(lines[28]);
        one.setZSetupRate(lines[29]);
        return one;
    }
}
