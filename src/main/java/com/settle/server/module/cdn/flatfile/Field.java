package com.settle.server.module.cdn.flatfile;

/**
 * 平面文件记录中的字段
 *
 * <AUTHOR>
 */
class Field {

    private int index;
    private String rawContent;

    /**
     * 创建一个新字段
     *
     * @param index      字段索引
     * @param rawContent 从记录中读取
     */
    Field(final int index, final String rawContent) {
        this.index = index;
        this.rawContent = rawContent;
    }

    /**
     * 获取字段索引
     *
     * @return 字段索引
     */
    int getIndex() {
        return index;
    }

    /**
     * 获取原始内容
     *
     * @return 原始内容
     */
    String getRawContent() {
        return rawContent;
    }

    @Override
    public String toString() {
        return "Field: {" +
                "index=" + index +
                ", rawContent='" + rawContent + '\'' +
                '}';
    }
}
