<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.pvsettle.SyncInterfacePvsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.pvsettle.SyncInterfacePvs">
        <result column="PROV_CODE" property="provCode" />
        <result column="IS_TO_C" property="isToC" />
        <result column="CUSTOMER_PROVINCE_NUMBER" property="customerProvinceNumber" />
        <result column="CUSTOMER_NAME" property="customerName" />
        <result column="PRODUCT_SUBS_ID" property="productSubsId" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="PRODUCT_NAME" property="productName" />
        <result column="DB_PRODUCT_ID" property="dbProductId" />
        <result column="DB_PRODUCT_NAME" property="dbProductName" />
        <result column="CO_PRODUCT_ID" property="coProductId" />
        <result column="BL_PRODUCT_ID" property="blProductId" />
        <result column="ONE_PRODUCT_ID" property="oneProductId" />
        <result column="BILLING_TERM" property="billingTerm" />
        <result column="PAY_TERM" property="payTerm" />
        <result column="PROD_CHARGE_CODE" property="prodChargeCode" />
        <result column="PO_CHARGE_CODE_NAME" property="poChargeCodeName" />
        <result column="ONE_PRO_CHARGE_CODE" property="oneProChargeCode" />
        <result column="ONE_PRO_CHARGE_NAME" property="oneProChargeName" />
        <result column="DB_PROD_CHARGE_CODE" property="dbProdChargeCode" />
        <result column="FEE_VAL" property="feeVal" />
        <result column="TAX_RATE" property="taxRate" />
        <result column="TAX" property="tax" />
        <result column="FEE_NO_TAX" property="feeNoTax" />
        <result column="FEE_FLAG" property="feeFlag" />
        <result column="DISCOUNT_AMOUNT" property="discountAmount" />
        <result column="STANDARD_FEE" property="standardFee" />
        <result column="SETTLE_FEE" property="settleFee" />
        <result column="GH_FEE_TYPE" property="ghFeeType" />
        <result column="RATEPLAN_ID" property="rateplanId" />
        <result column="RATEPLAN_NAME" property="rateplanName" />
        <result column="STANDARD_SALE_PRICE" property="standardSalePrice" />
        <result column="SETTLE_PRICE" property="settlePrice" />
        <result column="ORIGINAL_BILL_MONTH" property="originalBillMonth" />
        <result column="FEE_SEQ" property="feeSeq" />
        <result column="SALES_BASE_DISCOUNT" property="salesBaseDiscount" />
        <result column="PV_SETTLE_RATE" property="pvSettleRate" />
        <result column="PV_SETTLE_VALUE" property="pvSettleValue" />
        <result column="RATE_PLAN_GH_CODE" property="ratePlanGhCode" />
        <result column="RATE_PLAN_GH_NAME" property="ratePlanGhName" />
        <result column="IF_SUPPORT_PARTNER_SETTLE" property="ifSupportPartnerSettle" />
        <result column="SETTLEMENT_CLASS" property="settlementClass" />
        <result column="ON_PRODUCT_CODE" property="onProductCode" />
        <result column="ON_PRODUCT_NAME" property="onProductName" />
        <result column="EJ_PRODUCT_CODE" property="ejProductCode" />
        <result column="EJ_PRODUCT_NAME" property="ejProductName" />
        <result column="SJ_PRODUCT_CODE" property="sjProductCode" />
        <result column="SJ_PRODUCT_NAME" property="sjProductName" />
        <result column="GH_CODE" property="ghCode" />
        <result column="PRODUCT_CLASS" property="productClass" />
        <result column="PRODUCT_REPORT_NAME" property="productReportName" />
        <result column="PRODUCT_REPORT_ITEM_NAME" property="productReportItemName" />
        <result column="ISSUE_TIME" property="issueTime" />
        <result column="EXPIRE_TIME" property="expireTime" />
        <result column="PRODUCT_TYPE" property="productType" />
        <result column="BUSI_TYPE" property="busiType" />
        <result column="CONTRACT_MAIN" property="contractMain" />
        <result column="PV_PRODUCT_CLASS" property="pvProductClass" />
        <result column="JOINT_FLAG" property="jointFlag" />
        <result column="CITY_CODE" property="cityCode" />
        <result column="CREATOR_NAME" property="creatorName" />
        <result column="EC_ID" property="ecId" />
        <result column="INNER_CUSTOMER_FLAG" property="innerCustomerFlag" />
        <result column="YN_INNER_CUSTOMER_FLAG" property="ynInnerCustomerFlag" />
        <result column="SETTLEMENT_PARTY_OUT_NAME" property="settlementPartyOutName" />
        <result column="RESERVED_1" property="reserved1" />
        <result column="RESERVED_2" property="reserved2" />
        <result column="STATUS" property="status" />
        <result column="FILE_NAME" property="fileName" />
        <result column="TAG" property="tag" />
    </resultMap>
    <insert id="insertBatch">
        INSERT INTO SYNC_INTERFACE_PVS_${acctMonth} (PROV_CODE, IS_TO_C, CUSTOMER_PROVINCE_NUMBER, CUSTOMER_NAME, PRODUCT_SUBS_ID, PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID, DB_PRODUCT_NAME, CO_PRODUCT_ID, BL_PRODUCT_ID, ONE_PRODUCT_ID, BILLING_TERM, PAY_TERM, PROD_CHARGE_CODE, PO_CHARGE_CODE_NAME, ONE_PRO_CHARGE_CODE, ONE_PRO_CHARGE_NAME, DB_PROD_CHARGE_CODE, FEE_VAL, TAX_RATE, TAX, FEE_NO_TAX, FEE_FLAG, DISCOUNT_AMOUNT, STANDARD_FEE, SETTLE_FEE, GH_FEE_TYPE, RATEPLAN_ID, RATEPLAN_NAME, STANDARD_SALE_PRICE, SETTLE_PRICE, ORIGINAL_BILL_MONTH, FEE_SEQ, SALES_BASE_DISCOUNT, PV_SETTLE_RATE, PV_SETTLE_VALUE, RATE_PLAN_GH_CODE, RATE_PLAN_GH_NAME, IF_SUPPORT_PARTNER_SETTLE, SETTLEMENT_CLASS, ON_PRODUCT_CODE, ON_PRODUCT_NAME, EJ_PRODUCT_CODE, EJ_PRODUCT_NAME, SJ_PRODUCT_CODE, SJ_PRODUCT_NAME, GH_CODE, PRODUCT_CLASS, PRODUCT_REPORT_NAME, PRODUCT_REPORT_ITEM_NAME, ISSUE_TIME, EXPIRE_TIME, PRODUCT_TYPE, BUSI_TYPE, CONTRACT_MAIN, PV_PRODUCT_CLASS, JOINT_FLAG, CITY_CODE, CREATOR_NAME, EC_ID, INNER_CUSTOMER_FLAG, YN_INNER_CUSTOMER_FLAG, SETTLEMENT_PARTY_OUT_NAME, RESERVED_1, RESERVED_2, STATUS, FILE_NAME, TAG)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.provCode},
            #{item.isToC},
            #{item.customerProvinceNumber},
            #{item.customerName},
            #{item.productSubsId},
            #{item.productId},
            #{item.productName},
            #{item.dbProductId},
            #{item.dbProductName},
            #{item.coProductId},
            #{item.blProductId},
            #{item.oneProductId},
            #{item.billingTerm},
            #{item.payTerm},
            #{item.prodChargeCode},
            #{item.poChargeCodeName},
            #{item.oneProChargeCode},
            #{item.oneProChargeName},
            #{item.dbProdChargeCode},
            #{item.feeVal},
            #{item.taxRate},
            #{item.tax},
            #{item.feeNoTax},
            #{item.feeFlag},
            #{item.discountAmount},
            #{item.standardFee},
            #{item.settleFee},
            #{item.ghFeeType},
            #{item.rateplanId},
            #{item.rateplanName},
            #{item.standardSalePrice},
            #{item.settlePrice},
            #{item.originalBillMonth},
            #{item.feeSeq},
            #{item.salesBaseDiscount},
            #{item.pvSettleRate},
            #{item.pvSettleValue},
            #{item.ratePlanGhCode},
            #{item.ratePlanGhName},
            #{item.ifSupportPartnerSettle},
            #{item.settlementClass},
            #{item.onProductCode},
            #{item.onProductName},
            #{item.ejProductCode},
            #{item.ejProductName},
            #{item.sjProductCode},
            #{item.sjProductName},
            #{item.ghCode},
            #{item.productClass},
            #{item.productReportName},
            #{item.productReportItemName},
            #{item.issueTime},
            #{item.expireTime},
            #{item.productType},
            #{item.busiType},
            #{item.contractMain},
            #{item.pvProductClass},
            #{item.jointFlag},
            #{item.cityCode},
            #{item.creatorName},
            #{item.ecId},
            #{item.innerCustomerFlag},
            #{item.ynInnerCustomerFlag},
            #{item.settlementPartyOutName},
            #{item.reserved1},
            #{item.reserved2},
            #{item.status},
            #{item.fileName},
            #{item.tag}
            )
            </foreach>
    </insert>
    <update id="truncateTable">
        TRUNCATE TABLE SYNC_INTERFACE_PVS_${acctMonth}
    </update>
    <update id="updateStatusByAcctMonth">
        update stludr.SYNC_INTERFACE_PVS_${acctMonth}
        set status='23'
        where prov_code not in (select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1'))
          and status is null
          and file_name = #{fileName}
    </update>
    <update id="updateStatus0">
        update stludr.SYNC_INTERFACE_PVS_${acctMonth} set status='0'
        where status is null
          and file_name = #{fileName}
    </update>
    <select id="queryErrorByFileName" resultMap="BaseResultMap">
        select PROV_CODE, IS_TO_C, CUSTOMER_PROVINCE_NUMBER, CUSTOMER_NAME, PRODUCT_SUBS_ID, PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID, DB_PRODUCT_NAME, CO_PRODUCT_ID, BL_PRODUCT_ID, ONE_PRODUCT_ID, BILLING_TERM, PAY_TERM, PROD_CHARGE_CODE, PO_CHARGE_CODE_NAME, ONE_PRO_CHARGE_CODE, ONE_PRO_CHARGE_NAME, DB_PROD_CHARGE_CODE, FEE_VAL, TAX_RATE, TAX, FEE_NO_TAX, FEE_FLAG, DISCOUNT_AMOUNT, STANDARD_FEE, SETTLE_FEE, GH_FEE_TYPE, RATEPLAN_ID, RATEPLAN_NAME, STANDARD_SALE_PRICE, SETTLE_PRICE, ORIGINAL_BILL_MONTH, FEE_SEQ, SALES_BASE_DISCOUNT, PV_SETTLE_RATE, PV_SETTLE_VALUE, RATE_PLAN_GH_CODE, RATE_PLAN_GH_NAME, IF_SUPPORT_PARTNER_SETTLE, SETTLEMENT_CLASS, ON_PRODUCT_CODE, ON_PRODUCT_NAME, EJ_PRODUCT_CODE, EJ_PRODUCT_NAME, SJ_PRODUCT_CODE, SJ_PRODUCT_NAME, GH_CODE, PRODUCT_CLASS, PRODUCT_REPORT_NAME, PRODUCT_REPORT_ITEM_NAME, ISSUE_TIME, EXPIRE_TIME, PRODUCT_TYPE, BUSI_TYPE, CONTRACT_MAIN, PV_PRODUCT_CLASS, JOINT_FLAG, CITY_CODE, CREATOR_NAME, EC_ID, INNER_CUSTOMER_FLAG, YN_INNER_CUSTOMER_FLAG, SETTLEMENT_PARTY_OUT_NAME, RESERVED_1, RESERVED_2, STATUS, FILE_NAME, TAG
        from SYNC_INTERFACE_PVS_${acctMonth}
        where FILE_NAME = #{fileName}
        <![CDATA[AND STATUS <> '0'
        ]]>
    </select>

</mapper>
