package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Batch;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * 将处理委托给其他侦听器的复合监听器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class CompositeRecordWriterListener<P> implements RecordWriterListener<P> {

    private List<RecordWriterListener<P>> listeners;

    /**
     * 创建一个新的 {@link CompositeRecordWriterListener}
     */
    public CompositeRecordWriterListener() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositeRecordWriterListener}
     *
     * @param listeners 委托
     */
    public CompositeRecordWriterListener(List<RecordWriterListener<P>> listeners) {
        this.listeners = listeners;
    }

    @Override
    public void beforeRecordWriting(Batch<P> batch) {
        for (RecordWriterListener<P> listener : listeners) {
            listener.beforeRecordWriting(batch);
        }
    }

    @Override
    public void afterRecordWriting(Batch<P> batch) {
        for (ListIterator<RecordWriterListener<P>> iterator
             = listeners.listIterator(listeners.size());
             iterator.hasPrevious(); ) {
            iterator.previous().afterRecordWriting(batch);
        }
    }

    @Override
    public void onRecordWritingException(Batch<P> batch, Throwable throwable) {
        for (ListIterator<RecordWriterListener<P>> iterator
             = listeners.listIterator(listeners.size());
             iterator.hasPrevious(); ) {
            iterator.previous().onRecordWritingException(batch, throwable);
        }
    }

    /**
     * 添加委托监听器
     *
     * @param recordWriterListener 添加
     */
    public void addRecordWriterListener(RecordWriterListener<P> recordWriterListener) {
        listeners.add(recordWriterListener);
    }
}
