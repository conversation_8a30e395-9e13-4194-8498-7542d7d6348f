package com.settle.server.module.cdn.reader;


import com.settle.server.module.cdn.record.GenericRecord;
import com.settle.server.module.cdn.record.Header;
import com.settle.server.utils.Utils;

import java.time.LocalDateTime;
import java.util.Iterator;

/**
 * 从 {@link Iterable} 数据源读取记录
 * 此读取器生成 {@link GenericRecord} 实例，其中包含来自数据源的原始对象
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class IterableRecordReader<P> implements RecordReader<P> {

    private long currentRecordNumber;
    private Iterator<P> iterator;

    /**
     * 创建一个新的 {@link IterableRecordReader}
     *
     * @param dataSource 从中读取记录的数据源
     */
    public IterableRecordReader(final Iterable<P> dataSource) {
        Utils.checkNotNull(dataSource, "data source");
        this.iterator = dataSource.iterator();
    }

    @Override
    public void open() {
        currentRecordNumber = 0;
    }

    @Override
    public GenericRecord<P> readRecord() {
        Header header = new Header(++currentRecordNumber, getDataSourceName(), LocalDateTime.now());
        if (iterator.hasNext()) {
            return new GenericRecord<>(header, iterator.next());
        } else {
            return null;
        }
    }

    private String getDataSourceName() {
        return "In-Memory Iterable";
    }

}
