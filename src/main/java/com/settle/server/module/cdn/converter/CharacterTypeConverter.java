package com.settle.server.module.cdn.converter;

import com.settle.server.utils.Utils;

/**
 * Character 类型转换器
 * 选取字符串的第一个字符
 * 不接受 {@code null} 或空字符串
 *
 * <AUTHOR>
 */
public class CharacterTypeConverter implements TypeConverter<String, Character> {

    /**
     * {@inheritDoc}
     */
    @Override
    public Character convert(final String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        return value.charAt(0);
    }

}
