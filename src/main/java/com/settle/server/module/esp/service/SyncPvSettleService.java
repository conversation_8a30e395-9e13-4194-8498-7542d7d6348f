package com.settle.server.module.esp.service;

import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.pvsettle.SyncInterfacePvsMapper;
import com.settle.server.dao.stludr.pvsettle.SyncInterfacePvsTocMapper;
import com.settle.server.entity.pvsettle.SyncInterfacePvs;
import com.settle.server.entity.pvsettle.SyncInterfacePvsToc;
import com.settle.server.service.impl.MobileCloudServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/17
 * @since 1.0.0
 */
@Service
@Slf4j
public class SyncPvSettleService {

    @Autowired
    private SyncInterfacePvsMapper syncInterfacePvsMapper;
    @Autowired
    private SyncInterfacePvsTocMapper syncInterfacePvsTocMapper;

    /**
     * 处理toc文件
     TAG
     PROV_CODE
     IS_TO_C
     PRODUCT_SUBS_ID
     PRODUCT_ID
     PRODUCT_NAME
     DB_PRODUCT_ID
     DB_PRODUCT_NAME
     PV_PRODUCT_CLASS
     BILLING_TERM
     PAY_TERM
     FEE_VAL
     TAX_RATE
     TAX
     FEE_NO_TAX
     PV_SETTLE_RATE
     PV_SETTLE_VALUE
     ISSUE_TIME
     EXPIRE_TIME
     FILE_NAME
     */
    public void optPvsToc(String acctMonth, String filename, List<SyncInterfacePvsToc> pvsTocList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 22) {
            log.error("TOC文件，数据长度不对，acctMonth={},filename={},line={}", acctMonth, filename, line);
            throw new RuntimeException("TOC文件，数据长度不对");
        }
        SyncInterfacePvsToc syncInterfacePvsToc = new SyncInterfacePvsToc();
        syncInterfacePvsToc.setTag(split[0]);
        syncInterfacePvsToc.setProvCode(split[1]);
        syncInterfacePvsToc.setIsToC(split[2]);
        syncInterfacePvsToc.setProductSubsId(split[3]);
        syncInterfacePvsToc.setProductId(split[4]);
        syncInterfacePvsToc.setProductName(split[5]);
        syncInterfacePvsToc.setDbProductId(split[6]);
        syncInterfacePvsToc.setDbProductName(split[7]);
        syncInterfacePvsToc.setPvProductClass(split[8]);
        syncInterfacePvsToc.setBillingTerm(split[9]);
        syncInterfacePvsToc.setPayTerm(split[10]);
        syncInterfacePvsToc.setFeeVal(Long.valueOf(StringUtils.isNotEmpty(split[11]) ? split[11] : "0"));
        syncInterfacePvsToc.setTaxRate(split[12]);
        syncInterfacePvsToc.setTax(Long.valueOf(StringUtils.isNotEmpty(split[13]) ? split[13] : "0"));
        syncInterfacePvsToc.setFeeNoTax(Long.valueOf(StringUtils.isNotEmpty(split[14]) ? split[14] : "0"));
        syncInterfacePvsToc.setPvSettleRate(split[15]);
        syncInterfacePvsToc.setPvSettleValue(Long.valueOf(StringUtils.isNotEmpty(split[16]) ? split[16] : "0"));
        syncInterfacePvsToc.setRatePlanGhCode(split[17]);
        syncInterfacePvsToc.setRatePlanGhName(split[18]);
        syncInterfacePvsToc.setIssueTime(split[19]);
        syncInterfacePvsToc.setExpireTime(split[20]);
        syncInterfacePvsToc.setBzType(split[21]);
        syncInterfacePvsToc.setFileName(filename);

        pvsTocList.add(syncInterfacePvsToc);
    }

    /**
     * 处理pvs文件
     TAG
     PROV_CODE
     IS_TO_C
     CUSTOMER_PROVINCE_NUMBER
     CUSTOMER_NAME
     PRODUCT_SUBS_ID
     PRODUCT_ID
     PRODUCT_NAME
     DB_PRODUCT_ID
     DB_PRODUCT_NAME
     CO_PRODUCT_ID
     BL_PRODUCT_ID
     ONE_PRODUCT_ID
     BILLING_TERM
     PAY_TERM
     PROD_CHARGE_CODE
     PO_CHARGE_CODE_NAME
     ONE_PRO_CHARGE_CODE
     ONE_PRO_CHARGE_NAME
     DB_PROD_CHARGE_CODE
     FEE_VAL
     TAX_RATE
     TAX
     FEE_NO_TAX
     FEE_FLAG
     DISCOUNT_AMOUNT
     STANDARD_FEE
     SETTLE_FEE
     GH_FEE_TYPE
     RATEPLAN_ID
     RATEPLAN_NAME
     STANDARD_SALE_PRICE
     SETTLE_PRICE
     ORIGINAL_BILL_MONTH
     FEE_SEQ
     SALES_BASE_DISCOUNT
     PV_SETTLE_RATE
     PV_SETTLE_VALUE
     ON_PRODUCT_CODE
     ON_PRODUCT_NAME
     EJ_PRODUCT_CODE
     EJ_PRODUCT_NAME
     SJ_PRODUCT_CODE
     SJ_PRODUCT_NAME
     GH_CODE
     PRODUCT_CLASS
     PRODUCT_REPORT_NAME
     PRODUCT_REPORT_ITEM_NAME
     ISSUE_TIME
     EXPIRE_TIME
     PRODUCT_TYPE
     BUSI_TYPE
     CONTRACT_MAIN
     PV_PRODUCT_CLASS
     CITY_CODE
     CREATOR_NAME
     EC_ID
     FILE_NAME
     */
    public void optPvs(String acctMonth, String filename, List<SyncInterfacePvs> pvsList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 65) {
            log.error("非TOC文件，数据长度不对，acctMonth={},filename={},line={}", acctMonth, filename, line);
            throw new RuntimeException("非TOC文件，数据长度不对");
        }
        SyncInterfacePvs syncInterfacePvs = new SyncInterfacePvs();
        syncInterfacePvs.setTag(split[0]);
        syncInterfacePvs.setProvCode(split[1]);
        syncInterfacePvs.setIsToC(split[2]);
        syncInterfacePvs.setCustomerProvinceNumber(split[3]);
        syncInterfacePvs.setCustomerName(split[4]);
        syncInterfacePvs.setProductSubsId(split[5]);
        syncInterfacePvs.setProductId(split[6]);
        syncInterfacePvs.setProductName(split[7]);
        syncInterfacePvs.setDbProductId(split[8]);
        syncInterfacePvs.setDbProductName(split[9]);
        syncInterfacePvs.setCoProductId(split[10]);
        syncInterfacePvs.setBlProductId(split[11]);
        syncInterfacePvs.setOneProductId(split[12]);
        syncInterfacePvs.setBillingTerm(split[13]);
        syncInterfacePvs.setPayTerm(split[14]);
        syncInterfacePvs.setProdChargeCode(split[15]);
        syncInterfacePvs.setPoChargeCodeName(split[16]);
        syncInterfacePvs.setOneProChargeCode(split[17]);
        syncInterfacePvs.setOneProChargeName(split[18]);
        syncInterfacePvs.setDbProdChargeCode(split[19]);
        syncInterfacePvs.setFeeVal(StringUtils.isNotEmpty(split[20]) ? Long.valueOf(split[20]) : null);
        syncInterfacePvs.setTaxRate(split[21]);
        syncInterfacePvs.setTax(StringUtils.isNotEmpty(split[22]) ? Long.valueOf(split[22]) : null);
        syncInterfacePvs.setFeeNoTax(StringUtils.isNotEmpty(split[23]) ? Long.valueOf(split[23]) : null);
        syncInterfacePvs.setFeeFlag(split[24]);
        syncInterfacePvs.setDiscountAmount(StringUtils.isNotEmpty(split[25]) ? Long.valueOf(split[25]) : null);
        syncInterfacePvs.setStandardFee(StringUtils.isNotEmpty(split[26]) ? Long.valueOf(split[26]) : null);
        syncInterfacePvs.setSettleFee(StringUtils.isNotEmpty(split[27]) ? Long.valueOf(split[27]) :null);
        syncInterfacePvs.setGhFeeType(split[28]);
        syncInterfacePvs.setRateplanId(split[29]);
        syncInterfacePvs.setRateplanName(split[30]);
        syncInterfacePvs.setStandardSalePrice(split[31]);
        syncInterfacePvs.setSettlePrice(split[32]);
        syncInterfacePvs.setOriginalBillMonth(split[33]);
        syncInterfacePvs.setFeeSeq(split[34]);
        syncInterfacePvs.setSalesBaseDiscount(split[35]);
        syncInterfacePvs.setPvSettleRate(split[36]);
        syncInterfacePvs.setPvSettleValue(split[37]);
        syncInterfacePvs.setRatePlanGhCode(split[38]);
        syncInterfacePvs.setRatePlanGhName(split[39]);
        syncInterfacePvs.setIfSupportPartnerSettle(split[40]);
        syncInterfacePvs.setSettlementClass(split[41]);
        syncInterfacePvs.setOnProductCode(split[42]);
        syncInterfacePvs.setOnProductName(split[43]);
        syncInterfacePvs.setEjProductCode(split[44]);
        syncInterfacePvs.setEjProductName(split[45]);
        syncInterfacePvs.setSjProductCode(split[46]);
        syncInterfacePvs.setSjProductName(split[47]);
        syncInterfacePvs.setGhCode(split[48]);
        syncInterfacePvs.setProductClass(split[49]);
        syncInterfacePvs.setProductReportName(split[50]);
        syncInterfacePvs.setProductReportItemName(split[51]);
        syncInterfacePvs.setIssueTime(split[52]);
        syncInterfacePvs.setExpireTime(split[53]);
        syncInterfacePvs.setProductType(split[54]);
        syncInterfacePvs.setBusiType(split[55]);
        syncInterfacePvs.setContractMain(split[56]);
        syncInterfacePvs.setPvProductClass(split[57]);
        syncInterfacePvs.setJointFlag(split[58]);
        syncInterfacePvs.setCityCode(split[59]);
        syncInterfacePvs.setCreatorName(split[60]);
        syncInterfacePvs.setEcId(split[61]);
        syncInterfacePvs.setInnerCustomerFlag(split[62]);
        syncInterfacePvs.setYnInnerCustomerFlag(split[63]);
        syncInterfacePvs.setSettlementPartyOutName(split[64]);
        syncInterfacePvs.setFileName(filename);

        pvsList.add(syncInterfacePvs);
    }


    public void truncate(String acctMonth) {
        log.info("truncate pvsettle start");
        syncInterfacePvsMapper.truncateTable(acctMonth);
        syncInterfacePvsTocMapper.truncateTable(acctMonth);
        log.info("truncate pvsettle end");
    }

    public void saveSyncInterfacePvs(String acctMonth, List<SyncInterfacePvs> pvsList,int batchSize) {
        log.info("saveSyncInterfacePvs start");
        if (pvsList == null || pvsList.isEmpty()) {
            log.info("saveSyncInterfacePvs end, pvsList is empty");
            return;
        }
        Lists.partition(pvsList, batchSize).forEach(list -> syncInterfacePvsMapper.insertBatch(acctMonth, list));
        log.info("saveSyncInterfacePvs end");
    }

    public void saveSyncInterfacePvsToc(String acctMonth, List<SyncInterfacePvsToc> pvsTocList,int batchSize) {
        log.info("saveSyncInterfacePvsToc start");
        if (pvsTocList == null || pvsTocList.isEmpty()) {
            log.info("saveSyncInterfacePvsToc end, pvsTocList is empty");
            return;
        }
        Lists.partition(pvsTocList, batchSize).forEach(list -> syncInterfacePvsTocMapper.insertBatch(acctMonth, list));
        log.info("saveSyncInterfacePvsToc end");
    }

    /**
     * 更新状态为23
     * update stludr.SYNC_INTERFACE_PVS_${acct_month} set status='23'  where prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) and status is null;
     * update stludr.SYNC_INTERFACE_PVS_TOC_${acct_month} set status='23' where prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) and status is null;
     */
    public void updateStatus23(String acctMonth,String filename) {
        log.info("updateStatus23 start");
        syncInterfacePvsMapper.updateStatusByAcctMonth(acctMonth,filename);
        syncInterfacePvsTocMapper.updateStatusByAcctMonth(acctMonth,filename);
        log.info("updateStatus23 end");
    }

    public void updateStatus0(String acctMonth,String filename) {
        log.info("updateStatus0 start");
        syncInterfacePvsMapper.updateStatus0(acctMonth,filename);
        syncInterfacePvsTocMapper.updateStatus0(acctMonth,filename);
        log.info("updateStatus0 end");
    }


    public List<SyncInterfacePvs> queryErrorByFileName(String acctMonth, String fileName) {
        return syncInterfacePvsMapper.queryErrorByFileName(acctMonth, fileName);
    }

    public List<SyncInterfacePvsToc> queryToCErrorByFileName(String acctMonth, String filename) {
        return syncInterfacePvsTocMapper.queryErrorByFileName(acctMonth, filename);
    }
}
