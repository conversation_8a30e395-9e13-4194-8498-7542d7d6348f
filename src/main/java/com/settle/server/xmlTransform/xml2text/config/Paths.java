/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 0.9.6</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.xml2text.config;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import java.io.IOException;
import java.io.Reader;
import java.io.Serializable;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Enumeration;
import org.exolab.castor.xml.MarshalException;
import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;
import org.exolab.castor.xml.ValidationException;
import org.xml.sax.ContentHandler;

/**
 * Class Paths.
 * 
 * @version $Revision$ $Date$
 */
public class Paths implements Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _log4j
     */
    private String _log4j;

    /**
     * Field _DBConf
     */
    private String _DBConf;

    /**
     * Field _basepathList
     */
    private ArrayList _basepathList;


      //----------------/
     //- Constructors -/
    //----------------/

    public Paths() {
        super();
        _basepathList = new ArrayList();
    } //-- com.hp.xmlusage.xml2text.config.Paths()


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * Method addBasepath
     * 
     * 
     * 
     * @param vBasepath
     */
    public void addBasepath(Basepath vBasepath)
        throws IndexOutOfBoundsException
    {
        _basepathList.add(vBasepath);
    } //-- void addBasepath(com.hp.xmlusage.xml2text.config.Basepath) 

    /**
     * Method addBasepath
     * 
     * 
     * 
     * @param index
     * @param vBasepath
     */
    public void addBasepath(int index, Basepath vBasepath)
        throws IndexOutOfBoundsException
    {
        _basepathList.add(index, vBasepath);
    } //-- void addBasepath(int, com.hp.xmlusage.xml2text.config.Basepath) 

    /**
     * Method clearBasepath
     * 
     */
    public void clearBasepath()
    {
        _basepathList.clear();
    } //-- void clearBasepath() 

    /**
     * Method enumerateBasepath
     * 
     * 
     * 
     * @return Enumeration
     */
    public Enumeration enumerateBasepath()
    {
        return new org.exolab.castor.util.IteratorEnumeration(_basepathList.iterator());
    } //-- java.util.Enumeration enumerateBasepath() 

    /**
     * Method getBasepath
     * 
     * 
     * 
     * @param index
     * @return Basepath
     */
    public Basepath getBasepath(int index)
        throws IndexOutOfBoundsException
    {
        //-- check bounds for index
        if ((index < 0) || (index > _basepathList.size())) {
            throw new IndexOutOfBoundsException();
        }
        
        return (Basepath) _basepathList.get(index);
    } //-- com.hp.xmlusage.xml2text.config.Basepath getBasepath(int) 

    /**
     * Method getBasepath
     * 
     * 
     * 
     * @return Basepath
     */
    public Basepath[] getBasepath()
    {
        int size = _basepathList.size();
        Basepath[] mArray = new Basepath[size];
        for (int index = 0; index < size; index++) {
            mArray[index] = (Basepath) _basepathList.get(index);
        }
        return mArray;
    } //-- com.hp.xmlusage.xml2text.config.Basepath[] getBasepath() 

    /**
     * Method getBasepathCount
     * 
     * 
     * 
     * @return int
     */
    public int getBasepathCount()
    {
        return _basepathList.size();
    } //-- int getBasepathCount() 

    /**
     * Returns the value of field 'DBConf'.
     * 
     * @return String
     * @return the value of field 'DBConf'.
     */
    public String getDBConf()
    {
        return this._DBConf;
    } //-- java.lang.String getDBConf() 

    /**
     * Returns the value of field 'log4j'.
     * 
     * @return String
     * @return the value of field 'log4j'.
     */
    public String getLog4j()
    {
        return this._log4j;
    } //-- java.lang.String getLog4j() 

    /**
     * Method isValid
     * 
     * 
     * 
     * @return boolean
     */
    public boolean isValid()
    {
        try {
            validate();
        }
        catch (ValidationException vex) {
            return false;
        }
        return true;
    } //-- boolean isValid() 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param out
     */
    public void marshal(Writer out)
        throws MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, out);
    } //-- void marshal(java.io.Writer) 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param handler
     */
    public void marshal(ContentHandler handler)
        throws IOException, MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, handler);
    } //-- void marshal(org.xml.sax.ContentHandler) 

    /**
     * Method removeBasepath
     * 
     * 
     * 
     * @param vBasepath
     * @return boolean
     */
    public boolean removeBasepath(Basepath vBasepath)
    {
        boolean removed = _basepathList.remove(vBasepath);
        return removed;
    } //-- boolean removeBasepath(Basepath)

    /**
     * Method setBasepath
     * 
     * 
     * 
     * @param index
     * @param vBasepath
     */
    public void setBasepath(int index, Basepath vBasepath)
        throws IndexOutOfBoundsException
    {
        //-- check bounds for index
        if ((index < 0) || (index > _basepathList.size())) {
            throw new IndexOutOfBoundsException();
        }
        _basepathList.set(index, vBasepath);
    } //-- void setBasepath(int, com.hp.xmlusage.xml2text.config.Basepath) 

    /**
     * Method setBasepath
     * 
     * 
     * 
     * @param basepathArray
     */
    public void setBasepath(Basepath[] basepathArray)
    {
        //-- copy array
        _basepathList.clear();
        for (int i = 0; i < basepathArray.length; i++) {
            _basepathList.add(basepathArray[i]);
        }
    } //-- void setBasepath(com.hp.xmlusage.xml2text.config.Basepath) 

    /**
     * Sets the value of field 'DBConf'.
     * 
     * @param DBConf the value of field 'DBConf'.
     */
    public void setDBConf(String DBConf)
    {
        this._DBConf = DBConf;
    } //-- void setDBConf(java.lang.String) 

    /**
     * Sets the value of field 'log4j'.
     * 
     * @param log4j the value of field 'log4j'.
     */
    public void setLog4j(String log4j)
    {
        this._log4j = log4j;
    } //-- void setLog4j(java.lang.String) 

    /**
     * Method unmarshal
     * 
     * 
     * 
     * @param reader
     * @return Object
     */
    public static Object unmarshal(Reader reader)
        throws MarshalException, ValidationException
    {
        return (Paths) Unmarshaller.unmarshal(Paths.class, reader);
    } //-- java.lang.Object unmarshal(java.io.Reader) 

    /**
     * Method validate
     * 
     */
    public void validate()
        throws ValidationException
    {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    } //-- void validate() 

}
