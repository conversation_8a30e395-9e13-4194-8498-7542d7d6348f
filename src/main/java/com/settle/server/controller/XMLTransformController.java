package com.settle.server.controller;


import com.settle.server.utils.CommandUtil;
import com.settle.server.utils.FileUtil;
import com.settle.server.xmlTransform.text2xml.Config;
import com.settle.server.xmlTransform.text2xml.T2XEngine;
import com.settle.server.xmlTransform.text2xml.config.Entry;
import com.settle.server.xmlTransform.xml2text.X2TEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RestController
public class XMLTransformController {
    public static void showUsage()
    {
        System.out.println("Usage:");
        System.out.println("\tjava -cp xmlusage.jar com.hp.xmlusage.text2xml.Parser -i Config.xml");
    }

    @PostMapping("/text2xml")
    public  void text2xml(HttpServletRequest req, @RequestParam Map<String,Object> request){
        String arg= (String) request.get("request");
        List<String> args= Arrays.asList(arg.split(","));
        {
            if (args.size() != 2 || !args.get(0).equals("-i"))
            {
                showUsage();
                return;
            }

            //System.out.println("I AM STARTING ......");
            String cfgFile = args.get(1);
            if (!Config.init(cfgFile))
            {
                log.error("Initializing from config file failed!");
                System.out.println("Initializing from config file failed!");
                return;
            }
            if (!CommandUtil.isSingle("com.hp.xmlusage.text2xml.Parser", cfgFile))
            {
                log.error("There is one same thread running already!");
                //System.out.println("There is one same thread running already!");
                return;
            }
            log.info("Initialized from config file successfully.");

            for (int i=0; i<Config.getEntryCount(); i++)
            {
                Entry entry = Config.getEntry(i);
                new Thread(new T2XEngine(entry)).start();
                log.info(FileUtil.comb(entry.getName(), entry.getType())
                        + " thread of text2xml is started.");
            }

            log.info("All threads are started.");

            //log.info("Program Over.");

        }
    }
    @PostMapping("/xml2text")
    public  void xml2text(HttpServletRequest req, @RequestBody List<String> args){
        if (args.size() != 2 || !args.get(0).equals("-i")) {
            showUsage();
            return;
        }

        System.out.println("I AM STARTING ......");
        String cfgFile = args.get(1);
        if (!com.settle.server.xmlTransform.xml2text.Config.init(cfgFile)) {
            System.out.println("Initializing from config file failed!");
            return;
        }
        if (!CommandUtil.isSingle("com.hp.xmlusage.xml2text.Parser", cfgFile)) {
            log.error("There is one same thread running already!");
            return;
        }
        log.info("Initialized from config file successfully.");

        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(30);
        for (int i = 0; i < com.settle.server.xmlTransform.xml2text.Config.getEntryCount(); i++) {
            com.settle.server.xmlTransform.xml2text.config.Entry entry = com.settle.server.xmlTransform.xml2text.Config.getEntry(i);
            try {
//				new Thread(new X2TEngine(entry)).start();
                fixedThreadPool.execute(new X2TEngine(entry));
                log.info(entry.getName() + " thread of xml2text is started.");
            } catch (Exception ex) {
                log.error("Start Entry: "+entry.getName()+"Error, Exception: " + ex.getMessage());
                // return;
                System.exit(1);
            }
        }

        log.info("All threads are started.");
    }

}
