package com.settle.server.module.esp.service;

import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.pvbill.SyncInterfacePvbAdjMapper;
import com.settle.server.dao.stludr.pvbill.SyncInterfacePvbMapper;
import com.settle.server.entity.pvbill.SyncInterfacePvb;
import com.settle.server.entity.pvbill.SyncInterfacePvbAdj;
import com.settle.server.service.impl.MobileCloudServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/16
 * @since 1.0.0
 */
@Service
@Slf4j
public class SyncPvBillService {

    @Autowired
    private SyncInterfacePvbAdjMapper syncInterfacePvbAdjMapper;

    @Autowired
    private SyncInterfacePvbMapper syncInterfacePvbMapper;
    public void deleteByAcctMonth(String acctMonth) {
        syncInterfacePvbMapper.deleteByMonth(acctMonth);
        syncInterfacePvbAdjMapper.deleteByMonth(acctMonth);
    }

    public void pvbAdj(String acctMonth, String filename, List<SyncInterfacePvbAdj> pvbAdjList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 4) {
            log.error("pvbAdj line error:{}", line);
            return;
        }
        SyncInterfacePvbAdj syncInterfacePvbAdj = new SyncInterfacePvbAdj();
        syncInterfacePvbAdj.setBillMonth(acctMonth);
        syncInterfacePvbAdj.setFileName(filename);
        syncInterfacePvbAdj.setAdjustmentFeeVal(Long.parseLong(split[3]));
        syncInterfacePvbAdj.setHisBillMonth(split[2]);
        syncInterfacePvbAdj.setReserved1(split[1]); // 文件里的账期
        syncInterfacePvbAdj.setReserved2(null); // 保留字段2
        syncInterfacePvbAdj.setStatus("0");
        pvbAdjList.add(syncInterfacePvbAdj);

    }

    public void pvb(String acctMonth, String filename, List<SyncInterfacePvb> pvbList, String line) {
        String[] split = line.split(MobileCloudServiceImpl.FOUR_BAR, -1);
        if (split.length != 4) {
            log.error("pvb line error:{}", line);
            return;
        }
        SyncInterfacePvb syncInterfacePvb = new SyncInterfacePvb();
        syncInterfacePvb.setBillMonth(acctMonth);
        syncInterfacePvb.setFileName(filename);
        syncInterfacePvb.setAccumulatedFeeVal(null);
        syncInterfacePvb.setReserved1(split[0]); // 文件里的账期
        syncInterfacePvb.setReserved2(null); // 保留字段2
        syncInterfacePvb.setStatus("0");
        syncInterfacePvb.setProvCode(split[1]);
        syncInterfacePvb.setDirectRecAmount(split[2]);
        syncInterfacePvb.setJointRecAmount(split[3]);
        pvbList.add(syncInterfacePvb);
    }

    public void savePVBAdj(String acctMonth, List<SyncInterfacePvbAdj> pvbAdjList) {
        if (CollectionUtils.isEmpty(pvbAdjList)) {
            return;
        }
        Lists.partition(pvbAdjList, 1000).forEach(pvbAdjList1 -> {
            syncInterfacePvbAdjMapper.insertBatch(acctMonth, pvbAdjList1);
        });
    }

    public void savePVB(String acctMonth, List<SyncInterfacePvb> pvbList) {
        if (CollectionUtils.isEmpty(pvbList)) {
            return;
        }
        Lists.partition(pvbList, 1000).forEach(pvbList1 -> {
            syncInterfacePvbMapper.insertBatch(acctMonth, pvbList1);
        });
    }
}
