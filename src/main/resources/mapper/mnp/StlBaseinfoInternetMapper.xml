<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.mnp.StlBaseinfoInternetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.mnp.StlBaseinfoInternet">
        <result column="POSPECNUMBER" property="pospecnumber" />
        <result column="POSPECNAME" property="pospecname" />
        <result column="SOSPECNUMBER" property="sospecnumber" />
        <result column="SOSPECNAME" property="sospecname" />
        <result column="SOID" property="soid" />
        <result column="CUSTOMERNUMBER" property="customernumber" />
        <result column="START_PROV_NM" property="startProvNm" />
        <result column="START_PROV" property="startProv" />
        <result column="START_CITY" property="startCity" />
        <result column="MANAGER_NM" property="managerNm" />
        <result column="MANAGER_CON" property="managerCon" />
        <result column="END_PROV_NM" property="endProvNm" />
        <result column="END_PROV" property="endProv" />
        <result column="END_CITY" property="endCity" />
        <result column="END_DISTR" property="endDistr" />
        <result column="CP_NM" property="cpNm" />
        <result column="CP_CON" property="cpCon" />
        <result column="BANDWIDTH" property="bandwidth" />
        <result column="END_FUNC_RATE" property="endFuncRate" />
        <result column="END_IP_RATE" property="endIpRate" />
        <result column="END_SETUP_RATE" property="endSetupRate" />
        <result column="END_SLA_RATE" property="endSlaRate" />
        <result column="ADDRESS" property="address" />
        <result column="CUSTOMERNAME" property="customername" />
        <result column="START_FUNC_RATE" property="startFuncRate" />
        <result column="START_IP_RATE" property="startIpRate" />
        <result column="START_SETUP_RATE" property="startSetupRate" />
        <result column="START_SLA_RATE" property="startSlaRate" />
        <result column="SETTLEMONTH" property="settlemonth" />
    </resultMap>
    <insert id="insertBatch">
        insert into stl_baseinfo_internet
        (POSPECNUMBER, POSPECNAME, SOSPECNUMBER, SOSPECNAME, SOID, CUSTOMERNUMBER, START_PROV_NM, START_PROV,
         START_CITY, MANAGER_NM, MANAGER_CON, END_PROV_NM, END_PROV, END_CITY, END_DISTR, CP_NM, CP_CON, BANDWIDTH,
         END_FUNC_RATE, END_IP_RATE, END_SETUP_RATE, END_SLA_RATE, ADDRESS, CUSTOMERNAME, START_FUNC_RATE,
         START_IP_RATE, START_SETUP_RATE, START_SLA_RATE, SETTLEMONTH)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.pospecnumber},
            #{item.pospecname},
            #{item.sospecnumber},
            #{item.sospecname},
            #{item.soid},
            #{item.customernumber},
            #{item.startProvNm},
            #{item.startProv},
            #{item.startCity},
            #{item.managerNm},
            #{item.managerCon},
            #{item.endProvNm},
            #{item.endProv},
            #{item.endCity},
            #{item.endDistr},
            #{item.cpNm},
            #{item.cpCon},
            #{item.bandwidth},
            #{item.endFuncRate},
            #{item.endIpRate},
            #{item.endSetupRate},
            #{item.endSlaRate},
            #{item.address},
            #{item.customername},
            #{item.startFuncRate},
            #{item.startIpRate},
            #{item.startSetupRate},
            #{item.startSlaRate},
            #{item.settlemonth}
            )
        </foreach>

    </insert>
    <delete id="deleteHis" parameterType="string">
        delete from stl_baseinfo_internet_his where settlemonth = #{acctMonth}
    </delete>

</mapper>
