package com.settle.server.module.mnp.validators.impl;


import cn.hutool.core.util.StrUtil;
import com.settle.server.module.mnp.dto.MessageResult;
import com.settle.server.module.mnp.validators.BaseFileValidator;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.Map;

@Service("lineFileValidator")
public class LineFileValidator extends BaseFileValidator {
    @Override
    protected MessageResult validateFields(Map fieldMap) {
        if (StrUtil.byteLength((CharSequence)fieldMap.get("START_PROV"), Charset.forName("GBK")) > 3
                || StrUtil.byteLength((CharSequence)fieldMap.get("A_PROV"), Charset.forName("GBK")) > 3
                || StrUtil.byteLength((CharSequence)fieldMap.get("Z_PROV"), Charset.forName("GBK")) > 3) {
            return MessageResult.error(MessageResult.FAIL_002, "province code length is greater than 3");
        }

        return MessageResult.success();
    }
}

