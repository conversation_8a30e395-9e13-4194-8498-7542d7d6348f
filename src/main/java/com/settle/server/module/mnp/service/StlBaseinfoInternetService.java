package com.settle.server.module.mnp.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.mnp.StlBaseinfoInternetMapper;
import com.settle.server.entity.mnp.StlBaseinfoInternet;
import com.settle.server.utils.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service("stlBaseinfoInternetService")
@Slf4j
public class StlBaseinfoInternetService extends ServiceImpl<StlBaseinfoInternetMapper, StlBaseinfoInternet> implements StlBaseService{

    public void deleteHis(String acctMonth) {
        getBaseMapper().deleteHis(acctMonth);
    }

    public void delete(String acctMonth) {
        getBaseMapper().delete(new LambdaQueryWrapper<>(StlBaseinfoInternet.class)
                .eq(StlBaseinfoInternet::getSettlemonth, acctMonth));
    }

    /**
     * 批量插入
     *POSPECNUMBER ,POSPECNAME ,SOSPECNUMBER ,SOSPECNAME ,SOID ,CUSTOMERNUMBER ,START_PROV_NM ,START_PROV ,START_CITY ,MANAGER_NM ,MANAGER_CON ,
     *END_PROV_NM ,END_PROV ,END_CITY ,END_DISTR ,ADDRESS ,CP_NM ,CP_CON ,BANDWIDTH ,END_FUNC_RATE ,END_IP_RATE ,END_SETUP_RATE ,END_SLA_RATE,SETTLEMONTH
     */
    public void insertBatch(List<String[]> contents, String acctMonth) {
        List<StlBaseinfoInternet> list = Lists.newArrayList();
        for (String[] lines : contents) {
            StlBaseinfoInternet one = getStlBaseinfoInternet(acctMonth, lines);
            ObjectUtils.replaceEmptyStringWithNull(one);
            list.add(one);
        }
        Lists.partition(list, 1000).forEach(part -> {
            getBaseMapper().insertBatch(part);
            log.debug("insertBatch size:{}", part.size());
        });
    }

    private  StlBaseinfoInternet getStlBaseinfoInternet(String acctMonth, String[] lines) {
        StlBaseinfoInternet one = new StlBaseinfoInternet();
        one.setSettlemonth(acctMonth);
        one.setPospecnumber(lines[0]);
        one.setPospecname(lines[1]);
        one.setSospecnumber(lines[2]);
        one.setSospecname(lines[3]);
        one.setSoid(lines[4]);
        one.setCustomernumber(lines[5]);
        one.setStartProvNm(lines[6]);
        one.setStartProv(lines[7]);
        one.setStartCity(lines[8]);
        one.setManagerNm(lines[9]);
        one.setManagerCon(lines[10]);
        one.setEndProvNm(lines[11]);
        one.setEndProv(lines[12]);
        one.setEndCity(lines[13]);
        one.setEndDistr(lines[14]);
        one.setAddress(lines[15]);
        one.setCpNm(lines[16]);
        one.setCpCon(lines[17]);
        one.setBandwidth(lines[18]);
        one.setEndFuncRate(lines[19]);
        one.setEndIpRate(lines[20]);
        one.setEndSetupRate(lines[21]);
        one.setEndSlaRate(lines[22]);
        return one;
    }
}
