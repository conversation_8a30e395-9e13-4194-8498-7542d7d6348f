<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.StlIotCardSettDao">
    <resultMap id="BaseResultMap" type="com.settle.server.entity.StlIotCardSett">
        <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
        <result column="SETTLE_MONTH" jdbcType="VARCHAR" property="settleMonth" />
        <result column="BUS_TYPE" jdbcType="VARCHAR" property="busType" />
        <result column="SETTLE_TYPE" jdbcType="VARCHAR" property="settleType" />
        <result column="SETTLE_OUT" jdbcType="VARCHAR" property="settleOut" />
        <result column="SETTLE_IN" jdbcType="VARCHAR" property="settleIn" />
        <result column="SETTLE_AMOUNT" jdbcType="VARCHAR" property="settleAmount" />
        <result column="TAX_RATE" jdbcType="VARCHAR" property="taxRate" />
        <result column="SOURCE_FILE_NAME" jdbcType="VARCHAR" property="sourceFileName" />
    </resultMap>

    <insert id="batchInsert" useGeneratedKeys="false" parameterType="java.util.List">
        insert into STLUDR.STL_IOT_CARD_SETT (SERIAL_NUMBER,SETTLE_MONTH,BUS_TYPE ,SETTLE_TYPE, SETTLE_OUT, SETTLE_IN, SETTLE_AMOUNT, TAX_RATE, SOURCE_FILE_NAME)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            ( select
            #{item.serialNumber, jdbcType = VARCHAR},
            #{item.settleMonth, jdbcType = VARCHAR},
            #{item.busType, jdbcType = VARCHAR},
            #{item.settleType, jdbcType = VARCHAR},
            #{item.settleOut, jdbcType = VARCHAR},
            #{item.settleIn, jdbcType = VARCHAR},
            #{item.settleAmount, jdbcType = VARCHAR},
            #{item.taxRate, jdbcType = VARCHAR},
            #{item.sourceFileName, jdbcType = VARCHAR} from dual)
        </foreach>
    </insert>
    <delete id="deleteBySettleMonth" parameterType="java.lang.String">
        delete from STLUDR.STL_IOT_CARD_SETT
        <where>
            <if test="acctMonth != null and acctMonth != ''">
                AND SETTLE_MONTH = #{acctMonth, jdbcType = VARCHAR}
            </if>
            <if test="busType != null and busType != ''">
                AND BUS_TYPE = #{busType, jdbcType = VARCHAR}
            </if>
            <if test="fileName != null and fileName != ''">
                AND SOURCE_FILE_NAME = #{fileName, jdbcType = VARCHAR}
            </if>
        </where>
    </delete>
</mapper>