package com.settle.server.module.cdn.converter;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * {@link LocalTime} 类型转换器
 * 将 {@link DateTimeFormatter#ISO_LOCAL_TIME} 格式的字符串时间转换为 {@link LocalTime} 类型
 *
 * <AUTHOR>
 */
public class LocalTimeConverter implements TypeConverter<String, LocalTime> {

    @Override
    public LocalTime convert(String value) {
        return LocalTime.parse(value);
    }
}
