<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.bboss.LogFileDao">
    <resultMap id="BaseResultMap" type="com.settle.server.entity.LogFileEntity">
        <result column="bizType" jdbcType="VARCHAR" property="bizType" />
        <result column="count" jdbcType="VARCHAR" property="count" />
    </resultMap>
    <select id="queryLogFile" resultMap="BaseResultMap">
        with
            a as (select /*+parallel(a,8)*/ a.biz_type,sum(a.usage_count) cnt from boss_billing.log_file_t a
                  where a.acct_month = #{acctMonth} and a.partition_id_month = #{mm}
                    and a.biz_type in ('SMS','MMS') and a.modual = 'VALID' and a.file_type='COR'
                    and a.file_name not like '%recycle' group by a.biz_type),
            b as (select /*+parallel(a,8)*/ a.biz_type,sum(a.usage_count) cnt from boss_billing.log_file_t a
                  where a.acct_month = #{acctMonth} and a.partition_id_month = #{mm}
                    and a.biz_type in ('SMS','MMS') and a.modual = 'IMP' and a.file_type in ('COR', 'COS', 'CRR', 'COT','SND')
                    and a.file_name not like '%recycle' group by a.biz_type),
            c as (select/*+parallel(a,8)*/ a.biz_type,nvl(sum(a.raw_count-2),0) cnt from boss_billing.log_collect_t a
                  where a.acct_month = #{acctMonth} and a.partition_id_month = #{mm}
                    and a.biz_type in ('SMS','MMS') and a.state='F' and a.file_name not like '%recycle' group by a.biz_type),
            d as (select /*+parallel(a,8)*/ a.biz_type,sum(a.usage_count) cnt from boss_billing.log_file_t a
                  where a.acct_month = #{acctMonth} and a.partition_id_month = #{mm}
                    and a.biz_type in ('SMS','MMS') and a.modual = 'IMP' and a.file_type like '%ERR%'
                    and a.file_name not like '%recycle' group by a.biz_type),
            e as (select /*+parallel(a,8)*/ a.biz_type,nvl(sum(a.usage_count),0) cnt from boss_billing.log_file_t a
                  where a.acct_month = #{acctMonth} and a.partition_id_month = #{mm}
                    and a.biz_type in ('SMS','MMS') and a.modual = 'IMP' and a.file_type='DUP'
                    and a.file_name not like '%recycle' group by a.biz_type)
        select a.biz_type AS bizType,
               a.cnt || ' = ' ||
               NVL(b.cnt, '0') || ' + ' ||
               NVL(c.cnt, '0') || ' + ' ||
               NVL(d.cnt, '0') || ' + ' ||
               NVL(e.cnt, '0') AS count
        FROM
            a
                LEFT JOIN b ON a.biz_type = b.biz_type
                LEFT JOIN c ON a.biz_type = c.biz_type
                LEFT JOIN d ON a.biz_type = d.biz_type
                LEFT JOIN e ON a.biz_type = e.biz_type;

    </select>


</mapper>