package com.settle.server.module.cdn.flatfile;

import com.settle.server.module.cdn.mapper.AbstractRecordMapper;
import com.settle.server.module.cdn.mapper.ObjectMapper;
import com.settle.server.module.cdn.mapper.RecordMapper;
import com.settle.server.module.cdn.record.GenericRecord;
import com.settle.server.module.cdn.record.Record;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 固定长度记录到对象映射器实现
 *
 * @param <P> 记录有效负载的类型
 * <AUTHOR>
 */
public class FixedLengthRecordMapper<P> extends AbstractRecordMapper<P> implements RecordMapper<String, P> {

    public static final boolean DEFAULT_WHITESPACE_TRIMMING = false;

    private int[] fieldsLength;
    private int[] fieldsOffsets;
    private String[] fieldNames;
    private int recordExpectedLength;
    private boolean trimWhitespaces = DEFAULT_WHITESPACE_TRIMMING;

    /**
     * 创建一个新的 {@link FixedLengthRecordMapper} 实例
     *
     * @param recordClass  目标域对象类
     * @param fieldsLength FLR 平面文件中以相同顺序的字段长度数组
     * @param fieldNames   在 FLR 平面文件中以相同顺序表示字段名称的字符串数组
     */
    public FixedLengthRecordMapper(Class<P> recordClass, int[] fieldsLength, String[] fieldNames) {
        super(recordClass);
        this.fieldsLength = fieldsLength.clone();
        this.fieldNames = fieldNames.clone();
        objectMapper = new ObjectMapper<>(recordClass);
        for (int fieldLength : fieldsLength) {
            recordExpectedLength += fieldLength;
        }
        fieldsOffsets = calculateOffsets(fieldsLength);
    }

    @Override
    public Record<P> processRecord(final Record<String> record) throws Exception {

        List<Field> fields = parseRecord(record);
        Map<String, String> fieldsContents = new HashMap<>();
        for (Field field : fields) {
            String fieldName = fieldNames[field.getIndex()];
            String fieldValue = field.getRawContent();
            fieldsContents.put(fieldName, fieldValue);
        }
        return new GenericRecord<>(record.getHeader(), objectMapper.mapObject(fieldsContents));
    }

    protected List<Field> parseRecord(final Record<String> record) throws Exception {

        String payload = record.getPayload();
        int recordLength = payload.length();

        if (recordLength != recordExpectedLength) {
            throw new Exception("record length " + recordLength + " not equal to expected length of " + recordExpectedLength);
        }

        List<Field> fields = new ArrayList<>();
        for (int i = 0; i < fieldsLength.length; i++) {
            String token = payload.substring(fieldsOffsets[i], fieldsOffsets[i + 1]);
            token = trimWhitespaces(token);
            Field field = new Field(i, token);
            fields.add(field);
        }

        return fields;
    }

    /**
     * 计算用于从记录中提取字段的字段偏移量
     *
     * @param lengths
     * @return
     */
    private int[] calculateOffsets(final int[] lengths) {
        int[] offsets = new int[lengths.length + 1];
        offsets[0] = 0;
        for (int i = 0; i < lengths.length; i++) {
            offsets[i + 1] = offsets[i] + lengths[i];
        }
        return offsets;
    }

    private String trimWhitespaces(final String token) {
        if (trimWhitespaces) {
            return token.trim();
        }
        return token;
    }

    /**
     * 解析固定长度记录时去除空格
     *
     * @param trimWhitespaces 如果要去除空格，则为 true
     */
    public void setTrimWhitespaces(final boolean trimWhitespaces) {
        this.trimWhitespaces = trimWhitespaces;
    }

}
