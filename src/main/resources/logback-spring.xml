<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <springProperty scope="context" name="log.context" source="spring.application.name"/>
    <springProperty scope="context" name="log.path" source="log.path" defaultValue="/home/<USER>/logs"/>
    <springProperty scope="context" name="log.lever" source="log.lever" defaultValue="INFO"/>
    <property name="FileName" value="${HOSTNAME}"/>
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
    <property name="pattern" value="[%d{yyyy-MM-dd HH:mm:ss}][%-5level][%thread][%class{5}.%method:%line][%X{traceId}]:%m%n"/>

    <!--控制台输出-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    <!--文件输出-->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${log.context}/${FileName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件扩展名设置为.zip/.gz后在文件滚动时会自动对旧日志进行压缩 -->
            <fileNamePattern>${log.path}/${log.context}/%d{yyyy-MM,aux}/${FileName}-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <!--单个日志文件最大值-->
            <maxFileSize>500MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>30</maxHistory>
            <!--日志保留大小的上限，超过将删除旧的日志-->
            <totalSizeCap>100GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${pattern}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 文件 异步日志(async) -->
    <appender name="ASYNC-FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>10000</queueSize>
        <!--不丢弃消息-->
        <neverBlock>false</neverBlock>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="FILE"/>
    </appender>

    <!--打印任意包日志-->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>

    <!--日志级别 DEBUG > INFO > WARN >ERROR -->
    <!--  additivity="true" 便是可以继承root里面的配置-->
    <logger name="com.settle.server" level="${log.lever}"  additivity="false">
        <appender-ref ref="ASYNC-FILE"/>
        <appender-ref ref="STDOUT"/>
    </logger>
</configuration>