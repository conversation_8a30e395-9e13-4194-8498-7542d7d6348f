package com.settle.server.module.cdn.processor;


import com.settle.server.module.cdn.record.Record;

import java.util.ArrayList;
import java.util.List;

/**
 * 记录收集器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class RecordCollector<P> implements RecordProcessor<P, P> {

    private List<Record<P>> records = new ArrayList<>();

    @Override
    public Record<P> processRecord(final Record<P> record) {
        records.add(record);
        return record;
    }

    public List<Record<P>> getRecords() {
        return records;
    }

}
