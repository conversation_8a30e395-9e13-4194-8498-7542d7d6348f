package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.job.JobParameters;
import com.settle.server.module.cdn.job.JobReport;

/**
 * 使实现类在批处理作业之前/之后得到通知
 *
 * <AUTHOR>
 */
public interface JobListener {

    /**
     * 在开始作业之前调用
     *
     * @param jobParameters 作业参数
     */
    default void beforeJob(final JobParameters jobParameters) {
        // no-op
    }

    /**
     * 在作业完成（成功或失败）后调用
     *
     * @param jobReport 作业执行报告
     */
    default void afterJob(final JobReport jobReport) {
        // no-op
    }
}
