package com.settle.server.module.cdn.mapper;

import com.settle.server.module.cdn.converter.*;
import com.settle.server.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static java.lang.String.format;

/**
 * 将记录的有效负载映射到域对象实例
 *
 * <AUTHOR>
 */
public class ObjectMapper<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ObjectMapper.class.getName());

    private Class<T> objectType;
    private Map<String, Method> setters;
    private Map<Class<?>, TypeConverter<String, ?>> typeConverters;

    /**
     * 创建一个新的 {@link ObjectMapper}
     *
     * @param objectType 目标对象类型
     */
    public ObjectMapper(final Class<T> objectType) {
        this.objectType = objectType;
        initializeTypeConverters();
        initializeSetters();
    }

    /**
     * 将值映射到目标对象类型的字段
     *
     * @param values 字段值
     * @return 目标类型的填充实例
     * @throws Exception 如果值无法映射到目标对象字段
     */
    public T mapObject(final Map<String, String> values) throws Exception {

        T result = createInstance();

        // 对于每个字段
        for (Map.Entry<String, String> entry : values.entrySet()) {

            String field = entry.getKey();
            //获取字段原始值
            String value = values.get(field);

            Method setter = setters.get(field);
            if (setter == null) {
                LOGGER.warn("No public setter found for field {}, this field will be set to null (if object type) or default value (if primitive type)", field);
                continue;
            }

            Class<?> type = setter.getParameterTypes()[0];
            TypeConverter<String, ?> typeConverter = typeConverters.get(type);
            if (typeConverter == null) {
                LOGGER.warn("Type conversion not supported for type {}, field {} will be set to null (if object type) or default value (if primitive type)", type, field);
                continue;
            }

            if (value == null) {
                LOGGER.warn("Attempting to convert null to type {} for field {}, this field will be set to null (if object type) or default value (if primitive type)", type, field);
                continue;
            }

            if (value.isEmpty()) {
                LOGGER.debug("Attempting to convert an empty string to type {} for field {}, this field will be ignored", type, field);
                continue;
            }

            convertValue(result, field, value, setter, type, typeConverter);

        }

        return result;
    }

    private void initializeSetters() {
        setters = new HashMap<>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(objectType);
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            getSetters(propertyDescriptors);
        } catch (IntrospectionException e) {
            throw new BeanIntrospectionException("Unable to introspect target type " + objectType.getName(), e);
        }
    }

    private void getSetters(PropertyDescriptor[] propertyDescriptors) {
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            setters.put(propertyDescriptor.getName(), propertyDescriptor.getWriteMethod());
        }
        //排除属性 "class"
        setters.remove("class");
    }

    private T createInstance() throws Exception {
        try {
            return objectType.newInstance();
        } catch (Exception e) {
            throw new Exception(format("Unable to create a new instance of target type %s", objectType.getName()), e);
        }
    }

    private void convertValue(Object result, String field, String value, Method setter, Class<?> type, TypeConverter<String, ?> typeConverter) throws Exception {
        try {
            Object typedValue = typeConverter.convert(value);
            setter.invoke(result, typedValue);
        } catch (Exception e) {
            throw new Exception(format("Unable to convert %s to type %s for field %s", value, type, field), e);
        }
    }

    private void initializeTypeConverters() {
        typeConverters = new HashMap<>();
        typeConverters.put(AtomicInteger.class, new AtomicIntegerTypeConverter());
        typeConverters.put(AtomicLong.class, new AtomicLongTypeConverter());
        typeConverters.put(BigDecimal.class, new BigDecimalTypeConverter());
        typeConverters.put(BigInteger.class, new BigIntegerTypeConverter());
        typeConverters.put(Boolean.class, new BooleanTypeConverter());
        typeConverters.put(Boolean.TYPE, new BooleanTypeConverter());
        typeConverters.put(Byte.class, new ByteTypeConverter());
        typeConverters.put(Byte.TYPE, new ByteTypeConverter());
        typeConverters.put(Character.class, new CharacterTypeConverter());
        typeConverters.put(Character.TYPE, new CharacterTypeConverter());
        typeConverters.put(Double.class, new DoubleTypeConverter());
        typeConverters.put(Double.TYPE, new DoubleTypeConverter());
        typeConverters.put(Float.class, new FloatTypeConverter());
        typeConverters.put(Float.TYPE, new FloatTypeConverter());
        typeConverters.put(Integer.class, new IntegerTypeConverter());
        typeConverters.put(Integer.TYPE, new IntegerTypeConverter());
        typeConverters.put(Long.class, new LongTypeConverter());
        typeConverters.put(Long.TYPE, new LongTypeConverter());
        typeConverters.put(Short.class, new ShortTypeConverter());
        typeConverters.put(Short.TYPE, new ShortTypeConverter());
        typeConverters.put(java.util.Date.class, new DateTypeConverter());
        typeConverters.put(java.util.Calendar.class, new GregorianCalendarTypeConverter());
        typeConverters.put(java.util.GregorianCalendar.class, new GregorianCalendarTypeConverter());
        typeConverters.put(java.sql.Date.class, new SqlDateTypeConverter());
        typeConverters.put(java.sql.Time.class, new SqlTimeTypeConverter());
        typeConverters.put(java.sql.Timestamp.class, new SqlTimestampTypeConverter());
        typeConverters.put(java.time.LocalDate.class, new LocalDateConverter());
        typeConverters.put(java.time.LocalTime.class, new LocalTimeConverter());
        typeConverters.put(java.time.LocalDateTime.class, new LocalDateTimeConverter());
        typeConverters.put(String.class, new StringTypeConverter());
    }

    /**
     * 注册一个用于解析字段的 {@link TypeConverter}
     *
     * @param typeConverter 注册
     */
    public void registerTypeConverter(final TypeConverter<String, ?> typeConverter) {
        Class<?> clazz;
        try {
            clazz = Utils.getGenericTypeNameFromTypeConverter(typeConverter, 1);
        } catch (Exception e) {
            throw new TypeConverterRegistrationException("Unable to register custom type converter " + typeConverter.getClass().getName(), e);
        }
        typeConverters.put(clazz, typeConverter);
    }

}
