<?xml version="1.0" encoding="UTF-8"?>
<!--
- author:   <PERSON> Kitt
- date:     2020/11/18
- verson:   v1.0
- descprition:
    this file is used to transform the incoming inventory XML file to txt file.
-->
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:fn="http://www.w3.org/2005/xpath-functions" exclude-result-prefixes="fn xs xsi xsl">
	<xsl:output method="text" encoding="UTF-8" media-type="text/plain"/>
	<xsl:variable name="sep">
		<xsl:text>!!!!</xsl:text>
	</xsl:variable>
	<xsl:variable name="zero">
		<xsl:text>0</xsl:text>
	</xsl:variable>
	<xsl:variable name="hyphen">
		<xsl:text>-</xsl:text>
	</xsl:variable>
	<xsl:variable name="crlf">
		<xsl:text>&#xd;&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="carr">
		<xsl:text>&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="P2P">
		<xsl:text>P2P</xsl:text>
	</xsl:variable>
	<xsl:variable name="P2C">
		<xsl:text>P2C</xsl:text>
	</xsl:variable>
	<xsl:variable name="PARTNER">
		<xsl:text>PARTNER</xsl:text>
	</xsl:variable>
      
    <xsl:template match="/BillList">
		<xsl:variable name="v_ProvCode" select="ProvCode"/>
		<xsl:variable name="v_PayTag" select="PayTag"/>
		<xsl:for-each select="ECList">
			<xsl:for-each select="ECInfo">
				<xsl:variable name="v_GroupCustomerNumber" select="GroupCustomerNumber"/>
				<xsl:variable name="v_GroupCustomerName" select="GroupCustomerName"/>
				<xsl:variable name="v_EBOSSCustomerNumber" select="EBOSSCustomerNumber"/>
				<xsl:variable name="v_EBOSSCustomerName" select="EBOSSCustomerName"/>
				<xsl:variable name="v_AddressProvCode" select="AddressProvCode"/>
				<xsl:variable name="v_CreatorName" select="CreatorName"/>
				<xsl:variable name="v_InnerECFlag" select="InnerECFlag"/>
				<xsl:variable name="v_EcDepartmentName" select="EcDepartmentName"/>
				<xsl:variable name="v_ECCreatorName" select="ECCreatorName"/>
				<xsl:variable name="v_ECCreatorTel" select="ECCreatorTel"/>
				<xsl:variable name="pos_ECInfo" select="position()"/>
				<xsl:for-each select="FeeList">
					<xsl:for-each select="FeeInfo">
						<xsl:variable name="v_AccountID" select="AccountID"/>
						<xsl:variable name="v_AccountName" select="AccountName"/>
						<xsl:variable name="v_SubsID" select="SubsID"/>
						<xsl:variable name="v_ProductClassName" select="ProductClassName"/>
						<xsl:variable name="v_ProductDetailName" select="ProductDetailName"/>
						<xsl:variable name="v_ProductID" select="ProductID"/>
						<xsl:variable name="v_ProductName" select="ProductName"/>
						<xsl:variable name="v_MainContract" select="MainContract"/>
						<xsl:variable name="v_RunDepartmentName" select="RunDepartmentName"/>
						<xsl:variable name="v_ICTFLAG" select="ICTFLAG"/>
						<xsl:variable name="v_RateplanID" select="RateplanID"/>
						<xsl:variable name="v_RateplanName" select="RateplanName"/>
						<xsl:variable name="v_FeeVal" select="FeeVal"/>
						<xsl:variable name="v_TaxRate" select="TaxRate"/>
						<xsl:variable name="v_Tax" select="Tax"/>
						<xsl:variable name="v_FeeNoTax" select="FeeNoTax"/>
						<xsl:variable name="v_FeeFlag" select="FeeFlag"/>
						<xsl:variable name="v_OriginalBillMonth" select="OriginalBillMonth"/>
						<xsl:variable name="v_DiscountAmount" select="DiscountAmount"/>
						<xsl:variable name="v_StandardFee" select="StandardFee"/>
						<xsl:variable name="v_SettleFee" select="SettleFee"/>
						<xsl:variable name="v_BillingTerm" select="BillingTerm"/>
						<xsl:variable name="v_PayTerm" select="PayTerm"/>
						<xsl:variable name="v_SettleItem" select="SettleItem"/>
						<xsl:variable name="v_BusiMode" select="BusiMode"/>
						<xsl:variable name="v_Chargecode" select="Chargecode"/>
						<xsl:variable name="v_ChargeCodeName" select="ChargeCodeName"/>
						<xsl:variable name="v_CityCode" select="CityCode"/>
						<xsl:variable name="v_IniPrice" select="IniPrice"/>
						<xsl:variable name="v_SettlePrice" select="SettlePrice"/>
						<xsl:variable name="v_DbProductCode" select="DbProductCode"/>
						<xsl:variable name="v_DbProdChargeCode" select="DbProdChargeCode"/>
						<xsl:variable name="pos_FeeInfo" select="position()"/>
						
						<xsl:value-of select="$pos_ECInfo"/>
						<xsl:value-of select="$hyphen"/>
						<xsl:value-of select="$pos_FeeInfo"/>
						
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ProvCode"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_PayTag"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_GroupCustomerNumber"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_GroupCustomerName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_EBOSSCustomerNumber"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_EBOSSCustomerName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_AddressProvCode"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_InnerECFlag"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_EcDepartmentName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ECCreatorName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ECCreatorTel"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_AccountID"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_AccountName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_SubsID"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ProductClassName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ProductDetailName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ProductID"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ProductName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_MainContract"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_RunDepartmentName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ICTFLAG"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_RateplanID"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_RateplanName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_FeeVal"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_TaxRate"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_Tax"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_FeeNoTax"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_FeeFlag"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_OriginalBillMonth"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_DiscountAmount"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_StandardFee"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_SettleFee"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_BillingTerm"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_PayTerm"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_SettleItem"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_BusiMode"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_Chargecode"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_ChargeCodeName"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_CityCode"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_IniPrice"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_SettlePrice"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_DbProductCode"/>
						<xsl:value-of select="$sep"/>
						<xsl:value-of select="$v_DbProdChargeCode"/>
						<xsl:value-of select="$crlf"/>
		
						<xsl:for-each select="Province2SpecialSettle">
							<xsl:for-each select="Province2SpecialSettleinfo">
								<xsl:variable name="s_SettlementPartyIn1" select="SettlementPartyIn"/>
								<xsl:variable name="s_SettlementPartyOut1" select="SettlementPartyOut"/>
								<xsl:variable name="s_SettlementRate1" select="SettlementRate"/>
								<xsl:variable name="s_SettlementType1" select="SettlementType"/>
								<xsl:variable name="s_SettlementAmount1" select="SettlementAmount"/>
								<xsl:variable name="s_SettleClass1" select="SettleClass"/>
								
								<xsl:value-of select="$pos_ECInfo"/>
								<xsl:value-of select="$hyphen"/>
								<xsl:value-of select="$pos_FeeInfo"/>
						
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$P2C"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementPartyIn1"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementPartyOut1"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementRate1"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementType1"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementAmount1"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettleClass1"/>
								<xsl:value-of select="$crlf"/>
							</xsl:for-each>
						</xsl:for-each>
						
						<xsl:for-each select="Province2ProvinceSettle">
							<xsl:for-each select="Province2Provinceinfo">
								<xsl:variable name="s_SettlementPartyIn2" select="SettlementPartyIn"/>
								<xsl:variable name="s_SettlementPartyOut2" select="SettlementPartyOut"/>
								<xsl:variable name="s_SettlementRate2" select="SettlementRate"/>
								<xsl:variable name="s_SettlementType2" select="SettlementType"/>
								<xsl:variable name="s_SettlementAmount2" select="SettlementAmount"/>
								<xsl:variable name="s_Remark2" select="Remark"/>
								
								<xsl:value-of select="$pos_ECInfo"/>
								<xsl:value-of select="$hyphen"/>
								<xsl:value-of select="$pos_FeeInfo"/>
						
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$P2P"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementPartyIn2"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementPartyOut2"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementAmount2"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementType2"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementRate2"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_Remark2"/>
								<xsl:value-of select="$crlf"/>
							</xsl:for-each>
						</xsl:for-each>
						
						<xsl:for-each select="ParSettle">
							<xsl:for-each select="ParSettleInfo">
								<xsl:variable name="s_PartnerCode3" select="PartnerCode"/>
								<xsl:variable name="s_PartnerName3" select="PartnerName"/>
								<xsl:variable name="s_ParSettleRate3" select="ParSettleRate"/>
								<xsl:variable name="s_SettlementType3" select="SettlementType"/>
								<xsl:variable name="s_ParSettlAmount3" select="ParSettlAmount"/>
								<xsl:variable name="s_ParResSettlRate3" select="ParResSettlRate"/>
								<xsl:variable name="s_ParSettlePayType3" select="ParSettlePayType"/>
								
								<xsl:value-of select="$pos_ECInfo"/>
								<xsl:value-of select="$hyphen"/>
								<xsl:value-of select="$pos_FeeInfo"/>
						
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$PARTNER"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_PartnerCode3"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_PartnerName3"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_ParSettleRate3"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_SettlementType3"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_ParSettlAmount3"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_ParResSettlRate3"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$s_ParSettlePayType3"/>
								<xsl:value-of select="$crlf"/>
							</xsl:for-each>
						</xsl:for-each>
					</xsl:for-each>
				</xsl:for-each>
			</xsl:for-each>
		</xsl:for-each>
    </xsl:template>
</xsl:stylesheet>
