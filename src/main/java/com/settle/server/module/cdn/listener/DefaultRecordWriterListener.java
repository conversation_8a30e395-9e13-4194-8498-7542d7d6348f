package com.settle.server.module.cdn.listener;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class DefaultRecordWriterListener<P> implements RecordWriterListener<P> {

    /**
     * 日志ID
     */
    private String logId;
    private DataSource dataSource;

    public DefaultRecordWriterListener(final String logId, final DataSource dataSource) {
        this.logId = logId;
        this.dataSource = dataSource;

    }

    @Override
    public void onRecordWritingException(Batch<P> batch, final Throwable throwable) {
        try {
            for (Record<P> record : batch) {
                Db.use(dataSource).tx(db -> {
                    db.insert(Entity.create(NormalConst.DEFAULT_RECORD_ERR_LOG_TABLE).set("org_log_id", this.logId)
                            .set("line_num", record.getHeader().getNumber())
                            .set("raw_content", record.getPayload().toString())
                            .set("create_date", DateTime.now())
                            .set("error_code", "E001")
                            .set("error_desc", StrUtil.sub(throwable.getMessage(), 0, 255))
                    );
                });
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
