package com.settle.server.module.clearup.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/27
 * @since 1.0.0
 */
@Data
@Builder
public class RunLog {

    private int logMmdd;
    private Date logTs;
    private int logSid;
    private int logAusid;
    private String logLevel;
    private String logSrc;
    private int logLine;
    private String logTag;
    private String logText;

    private boolean isExec;
}