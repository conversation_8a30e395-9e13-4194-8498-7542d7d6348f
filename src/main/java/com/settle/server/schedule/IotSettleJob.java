package com.settle.server.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.service.IotSettService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/7
 * @since 1.0.0
 */
@Component
@Slf4j
public class IotSettleJob {
    @Autowired
    private IotSettService iotSettService;

    @XxlJob("iotSettleHandler")
    public ReturnT<String> iotSettleHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("iot平台[IOTSC_SETT]文件处理开始，接受参数：{}", param);
        try {
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            }else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            iotSettService.save(acctMonth);
            XxlJobHelper.log("iot平台[IOTSC_SETT]文件处理结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("iot平台[IOTSC_SETT]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    @XxlJob("iotPaxcSettleHandler")
    public ReturnT<String> iotPaxcSettleHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("iot平台[PlatformBill_PAXC]文件处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            }else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            iotSettService.paxcSave(acctMonth);
            XxlJobHelper.log("iot平台[PlatformBill_PAXC]文件处理结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("iot平台[PlatformBill_PAXC]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

}