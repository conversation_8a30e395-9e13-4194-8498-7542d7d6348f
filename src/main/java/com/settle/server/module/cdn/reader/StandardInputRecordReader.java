package com.settle.server.module.cdn.reader;


import com.settle.server.module.cdn.record.Header;
import com.settle.server.module.cdn.record.StringRecord;

import java.time.LocalDateTime;
import java.util.Scanner;

/**
 * 记录读取器从标准输入读取数据（对测试有用），直到读取终止符（可以在构造时指定，默认情况下为“quit”）
 * 此读取器生成 {@link StringRecord}
 *
 * <AUTHOR>
 */
public class StandardInputRecordReader implements RecordReader<String> {

    private static final String DEFAULT_TERMINATION_WORD = "quit";

    private Scanner scanner;
    private long recordNumber;
    private String terminationWord;

    /**
     * 创建一个新的 {@link StandardInputRecordReader}，默认终止符等于“quit”
     */
    public StandardInputRecordReader() {
        this(DEFAULT_TERMINATION_WORD);
    }

    /**
     * 创建一个带有终止符的新 {@link StandardInputRecordReader} 实例
     *
     * @param terminationWord 要键入的字符以停止从标准输入读取
     */
    public StandardInputRecordReader(final String terminationWord) {
        this.terminationWord = terminationWord;
    }

    @Override
    public void open() {
        scanner = new Scanner(System.in);
    }

    @Override
    public StringRecord readRecord() {
        String payload = scanner.nextLine();
        boolean stop = payload != null && !payload.isEmpty() && payload.equalsIgnoreCase(terminationWord);
        if (stop) {
            return null;
        }
        Header header = new Header(++recordNumber, getDataSourceName(), LocalDateTime.now());
        return new StringRecord(header, payload);
    }

    private String getDataSourceName() {
        return "Standard Input";
    }

    @Override
    public void close() {
        scanner.close();
    }

}
