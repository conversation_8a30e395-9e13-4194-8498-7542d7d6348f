<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.esp.SyncInterfaceEspPartMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.esp.SyncInterfaceEspPart">
        <result column="ID" property="id" />
        <result column="PARTNER_CODE" property="partnerCode" />
        <result column="PARTNER_NAME" property="partnerName" />
        <result column="PAR_SETTLE_RATE" property="parSettleRate" />
        <result column="PAR_RES_SETTL_RATE" property="parResSettlRate" />
        <result column="SETTLEMENT_TYPE" property="settlementType" />
        <result column="PAR_SETTLE_PAY_TYPE" property="parSettlePayType" />
        <result column="PAR_SETTL_AMOUNT" property="parSettlAmount" />
        <result column="FILE_NAME" property="fileName" />
        <result column="STATUS" property="status" />
    </resultMap>
    <insert id="savePart">
        INSERT INTO SYNC_INTERFACE_ESP_PART_${acctMonth}
            (
             ID,
             PARTNER_CODE,
             PARTNER_NAME,
             PAR_SETTLE_RATE,
             PAR_RES_SETTL_RATE,
             SETTLEMENT_TYPE,
             PAR_SETTLE_PAY_TYPE,
             PAR_SETTL_AMOUNT,
             FILE_NAME,
             STATUS
            )
        VALUES
            <foreach collection="list" item="item" index="index" separator=",">
                (
                 #{item.id},
                 #{item.partnerCode},
                 #{item.partnerName},
                 #{item.parSettleRate},
                 #{item.parResSettlRate},
                 #{item.settlementType},
                 #{item.parSettlePayType},
                 #{item.parSettlAmount},
                 #{item.fileName},
                 #{item.status}
                )
            </foreach>
    </insert>
    <update id="truncate">
        TRUNCATE TABLE SYNC_INTERFACE_ESP_PART_${acctMonth};
    </update>
    <update id="updateStatus23">
        update stludr.SYNC_INTERFACE_ESP_PART_${acctMonth}
        set status='F023'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acctMonth} where status='F023')
        and status is null
        and file_name=#{fileName}
    </update>

</mapper>
