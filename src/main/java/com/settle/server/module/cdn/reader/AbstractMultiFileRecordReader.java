package com.settle.server.module.cdn.reader;


import com.settle.server.module.cdn.record.Record;
import com.settle.server.utils.Utils;

import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.Iterator;
import java.util.List;

/**
 * 多文件记录读取器模板类
 * 实现应该提供如何在 {@link AbstractMultiFileRecordReader#createReader()} 方法中创建委托读取器。
 * 使用多文件读取器假定<strong>所有文件都具有相同的格式</strong>。
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public abstract class AbstractMultiFileRecordReader<P> implements RecordReader<P> {

    protected List<Path> files;
    protected Path currentFile;
    protected AbstractFileRecordReader<P> delegate;
    protected Iterator<Path> iterator;
    protected Charset charset;

    /**
     * 创建一个新的多文件记录读取器
     *
     * @param files 要读取的文件
     */
    public AbstractMultiFileRecordReader(List<Path> files) {
        this(files, Charset.defaultCharset());
    }

    /**
     * 创建一个新的多文件记录读取器
     *
     * @param files   要读取的文件
     * @param charset 字符编码
     */
    public AbstractMultiFileRecordReader(List<Path> files, Charset charset) {
        Utils.checkNotNull(files, "files");
        Utils.checkNotNull(charset, "charset");
        this.files = files;
        this.charset = charset;
    }

    @Override
    public void open() throws Exception {
        iterator = files.iterator();
        currentFile = iterator.next();
        if (currentFile != null) {
            delegate = createReader();
            delegate.open();
        }
    }

    @Override
    public Record<P> readRecord() throws Exception {
        if (delegate == null) {
            return null;
        }
        Record<P> record = delegate.readRecord();
        //读完当前文件，跳转到下一个文件
        if (record == null) {
            delegate.close();
            if (iterator.hasNext()) {
                currentFile = iterator.next();
                delegate = createReader();
                delegate.open();
                return readRecord();
            }
        }
        return record;
    }

    @Override
    public void close() throws Exception {
        if (delegate != null) {
            delegate.close();
        }
    }

    /**
     * 创建读取器
     *
     * @return
     * @throws Exception
     */
    protected abstract AbstractFileRecordReader<P> createReader() throws Exception;
}
