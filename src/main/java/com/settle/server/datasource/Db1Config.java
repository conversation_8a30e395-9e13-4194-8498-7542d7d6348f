package com.settle.server.datasource;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * Mybatis主数据源ds1配置
 * 多数据源配置依赖数据源配置
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = Db1Config.PACKAGE, sqlSessionTemplateRef = "ds1SqlSessionTemplate")
public class Db1Config {

    /**
     * @Fields field:field:指代：mybatis-config.xml
     */
    private static String MYBATIS_CONFIG = "mybatis-config.xml";
    /**
     * @Fields field:dao层接口所在的位置
     */
    static final String PACKAGE = "com.settle.server.dao.bboss";
    /**
     * @Fields field:mapper映射文件的位置
     */
    private static final String MAPPER_LOCATION = "classpath:/mapper/**/*.xml";

    @Value("${datasource.connection-timeout}")
    private int connectionTimeout;
    @Value("${datasource.max-pool-size}")
    private int maxPoolSize;
    @Value("${datasource.idle-timeout}")
    private int idleTimeout;
    @Value("${datasource.max-life-time}")
    private int maxLifeTime;


    /**
     * 主数据源配置 ds1数据源
     *
     * @return
     */
    @Bean(name = "ds1DataSourceProperties")
    @ConfigurationProperties(prefix = "jdbc.ds1")
    public DataSourceProperties ds1DataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * 主数据源 ds1数据源
     *
     * @param dataSourceProperties
     * @return
     */
    @Bean(name = "ds1DataSource")
    public DataSource ds1DataSource(@Qualifier("ds1DataSourceProperties") DataSourceProperties dataSourceProperties) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setPoolName("bossBillingPool");
        hikariConfig.setJdbcUrl(dataSourceProperties.getUrl());
        hikariConfig.setDriverClassName(dataSourceProperties.getDriverClassName());
        hikariConfig.setUsername(dataSourceProperties.getUsername());
        hikariConfig.setPassword(dataSourceProperties.getPassword());
        // 设置连接池参数
        hikariConfig.setMaximumPoolSize(maxPoolSize);
        hikariConfig.setMinimumIdle(maxPoolSize);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifeTime);
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setConnectionInitSql("select 1");
        hikariConfig.setConnectionTestQuery("select 'x'");

        return new HikariDataSource(hikariConfig);
    }

    /**
     * 主数据源 ds1数据源
     *
     * @param dataSource
     * @return
     * @throws Exception
     */
    @Bean("ds1SqlSessionFactory")
    public SqlSessionFactory ds1SqlSessionFactory(@Qualifier("ds1DataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sqlSessionFactory = new SqlSessionFactoryBean();
        sqlSessionFactory.setConfigLocation(new ClassPathResource(MYBATIS_CONFIG));
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(Db1Config.MAPPER_LOCATION));
        return sqlSessionFactory.getObject();
    }

    @Bean(name = "ds1TransactionManager")
    public DataSourceTransactionManager ds1TransactionManager(@Qualifier("ds1DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "ds1SqlSessionTemplate")
    public SqlSessionTemplate ds1SqlSessionTemplate(@Qualifier("ds1SqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
