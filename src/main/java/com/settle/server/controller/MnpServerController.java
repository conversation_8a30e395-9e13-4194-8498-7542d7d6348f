package com.settle.server.controller;

import com.settle.server.module.mnp.MNPHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/6/20
 * @since 1.0.0
 */
@Slf4j
@RestController
@Api(tags = "MNP服务")
public class MnpServerController {

    @Autowired
    private MNPHandler handler;

    @ApiOperation(value = "MNP服务处理请求", notes = "MNP服务处理请求")
    @PostMapping("/mnp")
    public String handleMnpRequest(@RequestParam(name = "acctMonth") String acctMonth) {
        try {
            handler.start(acctMonth);
        } catch (RuntimeException e) {
            log.error("MNP服务处理请求失败", e);
        }
        return "success";
    }
}