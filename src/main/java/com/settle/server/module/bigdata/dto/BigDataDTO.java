package com.settle.server.module.bigdata.dto;

import com.settle.server.entity.bigdata.BigDataFileLog;
import com.settle.server.entity.bigdata.SyncInterfaceBigdataFlow;
import lombok.Data;

import java.io.File;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/9/6
 * @since 1.0.0
 */
@Data
public class BigDataDTO {

    private File srcFile;

    private File ungzipFile;

    private BigDataFileLog fileLog;

    private String acctMonth;

    private String interfaceCode;

    private List<SyncInterfaceBigdataFlow> syncInterfaceBigdataFlows;


}