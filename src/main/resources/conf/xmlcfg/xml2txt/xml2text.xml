<?xml version="1.0" encoding="UTF-8"?>
<!--Sample XML file generated by XMLSpy v2008 sp1 (http://www.altova.com)-->
<Configure xsi:noNamespaceSchemaLocation="xml2text.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Paths>
		<Log4j>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/x2tlog4j.properties</Log4j>
		<DBConf>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/XML_DB.properties</DBConf>		
		<Basepath name="bl1">
			<In>$BBOSS_HOME/$BBOSS_APPID/data/xml2txt/BILLLIST</In>
			<Out>$BBOSS_HOME/$BBOSS_APPID/data/incoming/BILLLIST</Out>
			<Back>$BBOSS_HOME/$BBOSS_APPID/arch/xml2txt/BILLLIST</Back>
			<Resp>$BBOSS_HOME/$BBOSS_APPID/data/txt2xml/BILLLIST</Resp>
			<Xslt>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/xslt</Xslt>
			<Xsd>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/xsd</Xsd>
		</Basepath>
		<Basepath name="inc_Resp">
			<In>$BBOSS_HOME/$BBOSS_APPID/data/xml2txt/BILLLIST</In>
			<Out>$BBOSS_HOME/$BBOSS_APPID/data/xml2txt/RESP</Out>
			<Back>$BBOSS_HOME/$BBOSS_APPID/arch/xml2txt/BILLLIST</Back>
			<Resp>$BBOSS_HOME/$BBOSS_APPID/data/txt2xml/BILLLIST</Resp>
			<Xslt>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/xslt</Xslt>
			<Xsd>$BBOSS_HOME/$BBOSS_APPID/conf/xmlcfg/xsd</Xsd>
		</Basepath>
	</Paths>
	
	<Category>
		<Entry name="MLIST">
			<Basepath>bl1</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Resp></Resp>
			<Xslt>list_xml2txt.xslt</Xslt>
			<Xsd>list.xsd</Xsd>
			<Regexp>^BBOSS_ACC_M_LIST_[0-9]{8}_[0-9]{3}_[0-9]{3}.*</Regexp>
		</Entry>

		<Entry name="EBILL">
			<Basepath>bl1</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Resp></Resp>
			<Xslt>bill_xml2txt.xslt</Xslt>
			<Xsd>bill.xsd</Xsd>
			<Regexp>^BBOSS_ACC_E_BILL_[0-9]{8}_[0-9]{3}_[0-9]{3}.*</Regexp>
		</Entry>
	
		<Entry name="Resp">
			<Basepath>inc_Resp</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Resp></Resp>
			<Xslt>Resp_xml2txt.xslt</Xslt>
			<Xsd>Resp.xsd</Xsd>
			<Regexp>^RESP_BBOSS_ACC_E_BILL_[0-9]{8}_[0-9]{3}_[0-9]{3}.*</Regexp>
		</Entry>
		
		<Entry name="SBILL">
			<Basepath>bl1</Basepath>
			<In></In>
			<Out></Out>
			<Back></Back>
			<Resp></Resp>
			<Xslt>sett_xml2txt.xslt</Xslt>
			<Xsd>sett.xsd</Xsd>
			<Regexp>^BBOSS_Billing_LIST_[0-9]{8}_[0-9]{3}_000.([0-8]\d\d|9[0-8]\d|99[0-8])$|^BBOSS_Billing_LIST_[0-9]{8}_[0-9]{3}_000.([0-8]\d\d|9[0-8]\d|99[0-8])_tst$</Regexp>
		</Entry>
	
	</Category>
</Configure>
