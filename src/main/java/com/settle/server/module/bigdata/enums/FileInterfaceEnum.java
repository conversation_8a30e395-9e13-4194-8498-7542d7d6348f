package com.settle.server.module.bigdata.enums;

import lombok.Getter;

@Getter
public enum FileInterfaceEnum {
    INTERFACE_75824("75824", "政企宽带省间结算预出账流量月表"),
    INTERFACE_75983("75983", "政企宽带省间结算流量月表"),

    ;
    private String interfaceCode;

    private String interfaceName;

    FileInterfaceEnum(String interfaceCode, String interfaceName) {
        this.interfaceCode = interfaceCode;
        this.interfaceName = interfaceName;
    }
}
