package com.settle.server.controller;

import com.settle.server.service.IotSettService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Classname IotSettController
 * @Description
 * @Date 2023/10/13 18:44
 * <AUTHOR>
 * @Version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/iot")
public class IotSettController {

    @Autowired
    private IotSettService iotSettService;

    @GetMapping(value = "/save")
    public String save(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            iotSettService.save(acctMonth);
            iotSettService.paxcSave(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("IotSettController --->save error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }

}
