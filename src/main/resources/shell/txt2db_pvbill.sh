#!/bin/bash
export TXT2DB_HOME=/home/<USER>/workspace/txt2db
export BIN_PATH=${TXT2DB_HOME}/bin
export DATA_PATH=${TXT2DB_HOME}/data
export BACKUP_PATH=${TXT2DB_HOME}/backupData
export ERROR_PATH=${TXT2DB_HOME}/error
export CONF_PATH=${TXT2DB_HOME}/conf
export TABLE_PATH=${TXT2DB_HOME}/conf/table
export RESP_PATH=${TXT2DB_HOME}/resp
export LOG_PATH=${TXT2DB_HOME}/log
export WORK_PATH=${TXT2DB_HOME}/work

####4.29
log_file=${LOG_PATH}/txt2db_pvbill_`date +%Y%m%d`.log
echo $log_file
response_file=${WORK_PATH}/txt2db_pvbill_response.txt

function f_Print
{
    cur_ts=`date +%Y%m%d%H%M%S`
    echo "${cur_ts} - $1" >> $log_file
}

function f_init_config
{
    f_Print "f_init_config starts"

    if [ ! -f "${CONF_PATH}/txt2db_pvbill.conf" ]; then
        f_Print "f_init_config - config file not found - ${CONF_PATH}/txt2db_pvbill.conf"
        exit
    fi 

    . ${CONF_PATH}/txt2db_pvbill.conf

    f_Print "f_init_config finishes"
}  ## f_init_config

function f_truncate_table
{

    f_Print "f_truncate_table starts"

    echo "delete from stludr.SYNC_INTERFACE_PVB where bill_month = '${acct_month}';
     delete from stludr.SYNC_INTERFACE_PVB_ADJ where bill_month = '${acct_month}'; " | mysql -u$db_user -p$db_passwd -h$db_host -P$db_port
   
    f_Print "f_truncate_table finishes"
    
}  ## f_truncate_table

function f_load_data
{

    f_Print "f_load_data starts"

    for filename in `find ${DATA_PATH} -type f -name "${naming_convention}"`
    do
        filename_only=`basename $filename`
        f_Print "f_load_data - ${filename_only} - Starting loading file"

        tr -d '\r' < $filename > ${WORK_PATH}/${filename_only}.mod

        grep -v 'AdjustFee!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $0"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.tmp > ${WORK_PATH}/${filename_only}

	    grep 'AdjustFee!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.ADJ.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $2"!!!!"$3"!!!!"$4"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.ADJ.tmp > ${WORK_PATH}/${filename_only}.ADJ

        cd ${WORK_PATH}

        cp ${WORK_PATH}/${filename_only} ${WORK_PATH}/SYNC_INTERFACE_PVB.dat
        #### sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_PVB.ctl log=${LOG_PATH}/sqlldr_log_${filename_only} bad=${WORK_PATH}/bad.${filename_only}

        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_PVB.dat' into table stludr.SYNC_INTERFACE_PVB  fields terminated by '!!!!' lines terminated by '\n' (BILL_MONTH,ACCUMULATED_FEE_VAL,FILE_NAME);" 

        cp ${WORK_PATH}/${filename_only}.ADJ ${WORK_PATH}/SYNC_INTERFACE_PVB_ADJ.dat
        #### sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_PVB_ADJ.ctl log=${LOG_PATH}/sqlldr_log_${filename_only}.ADJ bad=${WORK_PATH}/bad.${filename_only}.ADJ

        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_PVB_ADJ.dat' into table stludr.SYNC_INTERFACE_PVB_ADJ  fields terminated by '!!!!' lines terminated by '\n' (BILL_MONTH,HIS_BILL_MONTH,ADJUSTMENT_FEE_VAL,FILE_NAME);" 

        cd -

        ##original data file move to backup dir
        mv  $filename ${BACKUP_PATH}/${acct_month}/

        ##delete file from work dir
        rm ${WORK_PATH}/${filename_only} ${WORK_PATH}/${filename_only}.tmp
        rm ${WORK_PATH}/${filename_only}.ADJ*
        rm ${WORK_PATH}/SYNC_INTERFACE_PVB*.dat

        f_Print "f_load_data - ${filename_only} - loading file - done"
    done

    f_Print "f_load_data finishes" 

}  ## f_load_data

function f_validation
{

    f_Print "f_validation starts"

    echo "update stludr.SYNC_INTERFACE_PVB a set status = '0' where bill_month = '${acct_month}' and status is null;
     update stludr.SYNC_INTERFACE_PVB_ADJ set status='0' where bill_month = '${acct_month}' and status is null;
      commit; " | mysql -u$db_user -p$db_passwd -h$db_host -P$db_port

    f_Print "f_validation finishes"
    
}  ## f_validation

function f_get_response
{

    f_Print "f_get_response starts"

    mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -N -s -e "  select *  from (
        select distinct file_name from stludr.sync_interface_pvb where bill_month = '${acct_month}' and status = '0'
    ) order by file_name; " >  ${response_file};

    f_Print "f_get_response finishes"
    
}  ## f_get_response

function f_get_response_file 
{
    f_Print "f_get_response_file starts"

    ##Remove empty lines and spaces
    sed '/^  *$/d' ${response_file} | sed '/^ *$/d' | sed 's/ //g' > ${response_file}.tmp
    mv ${response_file}.tmp ${response_file}

    response_header_1="<BillList>\n\t<Org_FileName>"
    response_header_2="</Org_FileName>\n\t<Resp_Date>"
    response_header_3="</Resp_Date>\n\t<FileStatus>0</FileStatus>\n</BillList>"


    ##Create file-level response files
    for filename in `awk -F "," '{print $1}' ${response_file} | sort | uniq`
    do
        f_Print "f_get_response_file - ${filename} - generating response file"
        current_ts=`date +%Y%m%d%H%M%S`
        echo "filename=$filename"
        echo -e "${response_header_1}${filename}${response_header_2}${current_ts}${response_header_3}" > ${WORK_PATH}/Resp_${filename}
        mv ${WORK_PATH}/Resp_${filename} ${RESP_PATH}/Resp_${filename}
        f_Print "f_get_response_file - ${filename} - response file created - ${RESP_PATH}/Resp_${filename}"
    done

    rm ${response_file}

    f_Print "f_get_response_file finishes"

} ## f_get_response_file

##main starts here
if (( $# < 1 )); then
    echo "usage: txt2db_pvbill.sh <acct_month>"
    exit
fi

f_Print "*****************************************************"
f_Print "                txt2db_pvbill.sh starts                    "
f_Print "*****************************************************"

##Read input param acct_month
acct_month=$1
f_Print "acct_month=$acct_month"

##Create backup data path
mkdir -p ${BACKUP_PATH}/${acct_month}
f_Print "backup data path ${BACKUP_PATH}/${acct_month} created"


f_init_config
if (( $? != 0)); then
   f_Print "f_init_config failure. Exit now." 
fi

##Check input dir, exit when no input
input_file_no=`find ${DATA_PATH} -type f -name "${naming_convention}" | wc -l`
echo input_file_no=$input_file_no
if (( input_file_no < 1 )); then
    f_Print "No input file found under dir ${DATA_PATH}. Exit now."
    exit
else
    f_Print "No of input file found under dir ${DATA_PATH}: ${input_file_no}"
fi

##Truncate data table before loading data files into DB
f_truncate_table
if (( $? != 0)); then
   f_Print "f_truncate_table failure. Exit now." 
fi

f_load_data
if (( $? != 0)); then
   f_Print "f_load_data failure. Exit now." 
fi

f_validation
if (( $? != 0)); then
   f_Print "f_validation failure. Exit now." 
fi

f_get_response
if (( $? != 0)); then
   f_Print "f_get_response failure. Exit now." 
fi

f_get_response_file
if (( $? != 0)); then
   f_Print "f_get_response_file failure. Exit now." 
fi

f_Print "*****************************************************"
f_Print "                txt2db_pvbill.sh finishes                   "
f_Print "*****************************************************"
f_Print ""
f_Print ""


