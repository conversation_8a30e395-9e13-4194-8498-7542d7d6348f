package com.settle.server.controller;

import com.settle.server.service.SettleDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算数据说明
 */
@Slf4j
@RestController
@RequestMapping("/settleData")
public class SettleDataGenerateController {

    @Autowired
    private SettleDataService settleDataService;

    @GetMapping(value = "/genWord")
    public String genWord(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            settleDataService.genWord(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("SettleDataGenerateController --->genWord error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }

    /**
     * 手动发邮件
     * @return
     */
    @GetMapping(value = "/sendEmail")
    public String sendEmail(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            settleDataService.send2Email(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("SettleDataGenerateController --->send2Email error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }

}
