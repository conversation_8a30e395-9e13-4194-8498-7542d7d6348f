package com.settle.server.module.cdn.record;

import java.nio.file.Path;

/**
 * 表示目录中文件的记录
 *
 * <AUTHOR>
 */
public class FileRecord extends GenericRecord<Path> {

    /**
     * 创建一个新的 {@link FileRecord}
     *
     * @param header  记录头
     * @param payload 记录有效载荷
     */
    public FileRecord(final Header header, final Path payload) {
        super(header, payload);
    }

    @Override
    public String toString() {
        return "Record: {" +
                "header=" + header +
                ", payload=" + payload.toAbsolutePath().toString() +
                '}';
    }

}
