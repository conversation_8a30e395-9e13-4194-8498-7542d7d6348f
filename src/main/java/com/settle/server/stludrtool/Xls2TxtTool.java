package com.settle.server.stludrtool;

import com.settle.server.utils.Xls2Txt;

import java.io.File;


/**
 * @ClassName: Xls2TxtTool
 * @Description: xls文件转txt且生成md5校验文件的工具入口
 * @company HPE
 * <AUTHOR>
 * @date 2016-10-26 下午03:37:08
 *
 */
public class Xls2TxtTool {
	private String fileName;
	private String toPath;
	private String toName;
	private final String FILE_TYPE = ".xls";
	/**
	 * @Title: Xls2Txt
	 * @Description: 开始执行文件转换
	 * @param     参数
	 * @return void    返回类型
	 * @throws
	 */
	public void excel2Txt(){
		if(fileName != null){
			Xls2Txt txt = new Xls2Txt();
			txt.toTxt(new File(this.toPath + File.separator + this.fileName + this.FILE_TYPE), this.toPath, this.toName);
		}else{
			throw new RuntimeException("请提供源文件名称，不带后缀名!");
		}
	}
	public Xls2TxtTool(String toPath) {
		super();
		this.toPath = toPath;
	}
	public Xls2TxtTool(String fileName, String toPath, String toName) {
		super();
		this.fileName = fileName;
		this.toPath = toPath;
		this.toName = toName;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
		this.toName = fileName;
	}
	public String getToPath() {
		return toPath;
	}
	public void setToPath(String toPath) {
		this.toPath = toPath;
	}
	public String getToName() {
		return toName;
	}
	public void setToName(String toName) {
		this.toName = toName;
	}
}
