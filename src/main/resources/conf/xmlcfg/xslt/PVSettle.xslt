<?xml version="1.0" encoding="UTF-8"?>
<!--
- author:   <PERSON> Kitt
- date:     2023/08/17
- verson:   v1.0
- descprition:
    this file is used to transform the incoming inventory XML file to txt file.
-->
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:fn="http://www.w3.org/2005/xpath-functions" exclude-result-prefixes="fn xs xsi xsl">
	<xsl:output method="text" encoding="UTF-8" media-type="text/plain"/>
	<xsl:variable name="sep">
		<xsl:text>!!!!</xsl:text>
	</xsl:variable>
	<xsl:variable name="zero">
		<xsl:text>0</xsl:text>
	</xsl:variable>
	<xsl:variable name="hyphen">
		<xsl:text>-</xsl:text>
	</xsl:variable>
	<xsl:variable name="crlf">
		<xsl:text>&#xd;&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="carr">
		<xsl:text>&#xa;</xsl:text>
	</xsl:variable>
	<xsl:variable name="IsToC">
		<xsl:text>IsToC</xsl:text>
	</xsl:variable>
	<xsl:variable name="NoneToC">
		<xsl:text>NoneToC</xsl:text>
	</xsl:variable>

	<xsl:template match="/BillList">
		<xsl:variable name="v_ProvCode" select="ProvCode"/>
		<xsl:variable name="v_IsToC" select="IsToC"/>
		<xsl:for-each select="ECList">
			<xsl:for-each select="ECInfo">
				<xsl:variable name="v_CustomerProvinceNumber" select="CustomerProvinceNumber"/>
				<xsl:variable name="v_CustomerName" select="CustomerName"/>
				<xsl:variable name="v_CityCode" select="CityCode"/>
				<xsl:variable name="v_CreatorName" select="CreatorName"/>
				<xsl:variable name="v_ECid" select="ECid"/>
				<xsl:variable name="v_InnerCustomerFlag" select="InnerCustomerFlag"/>
				<xsl:variable name="v_YnInnerCustomerFlag" select="YnInnerCustomerFlag"/>
				<xsl:variable name="v_SettlementPartyOutName" select="SettlementPartyOutName"/>
				<xsl:for-each select="BizList">
					<xsl:for-each select="BizInfo">
						<xsl:for-each select="ProductSubsList">
							<xsl:for-each select="SubProductInfo">
								<xsl:variable name="v_ProductSubsID" select="ProductSubsID"/>
								<xsl:variable name="v_ProductID" select="ProductID"/>
								<xsl:variable name="v_ProductName" select="ProductName"/>
								<xsl:variable name="v_DbProductID" select="DbProductID"/>
								<xsl:variable name="v_DbProductName" select="DbProductName"/>
								<xsl:variable name="v_CoProductId" select="CoProductId"/>
								<xsl:variable name="v_BlProductID" select="BlProductID"/>
								<xsl:variable name="v_OneProductID" select="OneProductID"/>
								<xsl:variable name="v_BillingTerm" select="BillingTerm"/>
								<xsl:variable name="v_PayTerm" select="PayTerm"/>
								<xsl:variable name="v_OnProductCode" select="OnProductCode"/>
								<xsl:variable name="v_OnProductName" select="OnProductName"/>
								<xsl:variable name="v_EjProductCode" select="EjProductCode"/>
								<xsl:variable name="v_EjProductName" select="EjProductName"/>
								<xsl:variable name="v_SjProductCode" select="SjProductCode"/>
								<xsl:variable name="v_SjProductName" select="SjProductName"/>
								<xsl:variable name="v_GhCode" select="GhCode"/>
								<xsl:variable name="v_ProductClass" select="ProductClass"/>
								<xsl:variable name="v_ProductReportName" select="ProductReportName"/>
								<xsl:variable name="v_ProductReportItemName" select="ProductReportItemName"/>
								<xsl:variable name="v_IssueTime" select="IssueTime"/>
								<xsl:variable name="v_ExpireTime" select="ExpireTime"/>
								<xsl:variable name="v_ProductType" select="ProductType"/>
								<xsl:variable name="v_BusiType" select="BusiType"/>
								<xsl:variable name="v_ContractMain" select="ContractMain"/>
								<xsl:variable name="v_PVProductClass" select="PVProductClass"/>
								<xsl:variable name="v_JointFlag" select="JointFlag"/>
								<xsl:for-each select="FeeList">
									<xsl:for-each select="FeeInfo">
										<xsl:variable name="v_ProdChargeCode" select="ProdChargeCode"/>
										<xsl:variable name="v_PoChargeCodeName" select="PoChargeCodeName"/>
										<xsl:variable name="v_OneProChargeCode" select="OneProChargeCode"/>
										<xsl:variable name="v_OneProChargeName" select="OneProChargeName"/>
										<xsl:variable name="v_DbProdChargeCode" select="DbProdChargeCode"/>
										<xsl:variable name="v_FeeVal" select="FeeVal"/>
										<xsl:variable name="v_TaxRate" select="TaxRate"/>
										<xsl:variable name="v_Tax" select="Tax"/>
										<xsl:variable name="v_FeeNoTax" select="FeeNoTax"/>
										<xsl:variable name="v_FeeFlag" select="FeeFlag"/>
										<xsl:variable name="v_DiscountAmount" select="DiscountAmount"/>
										<xsl:variable name="v_StandardFee" select="StandardFee"/>
										<xsl:variable name="v_SettleFee" select="SettleFee"/>
										<xsl:variable name="v_GhFeeType" select="GhFeeType"/>
										<xsl:variable name="v_RateplanID" select="RateplanID"/>
										<xsl:variable name="v_RateplanName" select="RateplanName"/>
										<xsl:variable name="v_StandardSalePrice" select="StandardSalePrice"/>
										<xsl:variable name="v_SettlePrice" select="SettlePrice"/>
										<xsl:variable name="v_OriginalBillMonth" select="OriginalBillMonth"/>
										<xsl:variable name="v_FeeSeq" select="FeeSeq"/>
										<xsl:variable name="v_SalesBaseDiscount" select="SalesBaseDiscount"/>
										<xsl:variable name="v_PVsettleRate" select="PVsettleRate"/>
										<xsl:variable name="v_PVsettleValue" select="PVsettleValue"/>
										<xsl:variable name="v_RatePlanGhCode" select="RatePlanGhCode"/>
										<xsl:variable name="v_RatePlanGhName" select="RatePlanGhName"/>
										<xsl:variable name="v_IfSupportPartnerSettle" select="IfSupportPartnerSettle"/>
										<xsl:variable name="v_SettlementClass" select="SettlementClass"/>

										<xsl:value-of select="$NoneToC"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProvCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_IsToC"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_CustomerProvinceNumber"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_CustomerName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductSubsID"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductID"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_DbProductID"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_DbProductName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_CoProductId"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_BlProductID"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_OneProductID"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_BillingTerm"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_PayTerm"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProdChargeCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_PoChargeCodeName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_OneProChargeCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_OneProChargeName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_DbProdChargeCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_FeeVal"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_TaxRate"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_Tax"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_FeeNoTax"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_FeeFlag"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_DiscountAmount"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_StandardFee"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SettleFee"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_GhFeeType"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_RateplanID"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_RateplanName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_StandardSalePrice"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SettlePrice"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_OriginalBillMonth"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_FeeSeq"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SalesBaseDiscount"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_PVsettleRate"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_PVsettleValue"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_RatePlanGhCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_RatePlanGhName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_IfSupportPartnerSettle"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SettlementClass"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_OnProductCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_OnProductName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_EjProductCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_EjProductName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SjProductCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SjProductName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_GhCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductClass"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductReportName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductReportItemName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_IssueTime"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ExpireTime"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ProductType"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_BusiType"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ContractMain"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_PVProductClass"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_JointFlag"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_CityCode"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_CreatorName"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_ECid"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_InnerCustomerFlag"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_YnInnerCustomerFlag"/>
										<xsl:value-of select="$sep"/>
										<xsl:value-of select="$v_SettlementPartyOutName"/>
										<xsl:value-of select="$crlf"/>
									</xsl:for-each>
								</xsl:for-each>
							</xsl:for-each>
						</xsl:for-each>
					</xsl:for-each>
				</xsl:for-each>
			</xsl:for-each>
		</xsl:for-each>
		<xsl:for-each select="ToCBizList">
			<xsl:for-each select="ToCBizInfo">
				<xsl:for-each select="ProductSubsList">
					<xsl:for-each select="SubProductInfo">
						<xsl:variable name="c_ProductSubsID" select="ProductSubsID"/>
						<xsl:variable name="c_ProductID" select="ProductID"/>
						<xsl:variable name="c_ProductName" select="ProductName"/>
						<xsl:variable name="c_DbProductID" select="DbProductID"/>
						<xsl:variable name="c_DbProductName" select="DbProductName"/>
						<xsl:variable name="c_PVProductClass" select="PVProductClass"/>
						<xsl:variable name="c_BillingTerm" select="BillingTerm"/>
						<xsl:variable name="c_PayTerm" select="PayTerm"/>
						<xsl:variable name="c_IssueTime" select="IssueTime"/>
						<xsl:variable name="c_ExpireTime" select="ExpireTime"/>
						<xsl:variable name="c_BzType" select="BzType"/>
						<xsl:for-each select="FeeList">
							<xsl:for-each select="FeeInfo">
								<xsl:variable name="c_FeeVal" select="FeeVal"/>
								<xsl:variable name="c_TaxRate" select="TaxRate"/>
								<xsl:variable name="c_Tax" select="Tax"/>
								<xsl:variable name="c_FeeNoTax" select="FeeNoTax"/>
								<xsl:variable name="c_PVsettleRate" select="PVsettleRate"/>
								<xsl:variable name="c_PVsettleValue" select="PVsettleValue"/>
								<xsl:variable name="c_RatePlanGhCode" select="RatePlanGhCode"/>
								<xsl:variable name="c_RatePlanGhName" select="RatePlanGhName"/>

								<xsl:value-of select="$IsToC"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$v_ProvCode"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$v_IsToC"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_ProductSubsID"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_ProductID"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_ProductName"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_DbProductID"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_DbProductName"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_PVProductClass"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_BillingTerm"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_PayTerm"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_FeeVal"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_TaxRate"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_Tax"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_FeeNoTax"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_PVsettleRate"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_PVsettleValue"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_RatePlanGhCode"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_RatePlanGhName"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_IssueTime"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_ExpireTime"/>
								<xsl:value-of select="$sep"/>
								<xsl:value-of select="$c_BzType"/>
								<xsl:value-of select="$crlf"/>
							</xsl:for-each>
						</xsl:for-each>
					</xsl:for-each>
				</xsl:for-each>
			</xsl:for-each>
		</xsl:for-each>
	</xsl:template>
</xsl:stylesheet>
