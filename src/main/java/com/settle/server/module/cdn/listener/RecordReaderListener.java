package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Record;

/**
 * 使实现类在读取记录之前/之后得到通知
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public interface RecordReaderListener<P> {

    /**
     * 在每个记录读取操作之前调用
     */
    default void beforeRecordReading() {
        // no-op
    }

    /**
     * 在每次记录读取操作后调用
     *
     * @param record 已读取的记录，如果读取器到达数据源的末尾，则可能为null
     */
    default void afterRecordReading(final Record<P> record) {
        // no-op
    }

    /**
     * 在记录读取过程中发生异常时调用
     *
     * @param throwable 在记录读取期间抛出的 throwable
     */
    default void onRecordReadingException(final Throwable throwable) {
        // no-op
    }

}
