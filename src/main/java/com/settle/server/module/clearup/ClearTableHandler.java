package com.settle.server.module.clearup;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.google.common.collect.Lists;
import com.settle.server.module.clearup.config.ClearTabBizCache;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.PartitionTypeEnum;
import com.settle.server.module.clearup.service.ClearTableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/23
 * @since 1.0.0
 */
@Service
@Slf4j
public class ClearTableHandler {

    private ThreadPoolExecutor EXECUTOR;
    @Autowired
    private Map<String, ClearTableService> clearTableServiceMap;


    public ClearTableHandler() {
        ThreadFactory threadFactory = ThreadFactoryBuilder.create().setNamePrefix("clear-table").build();
        EXECUTOR = new ThreadPoolExecutor(3,3, 10L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),threadFactory);
    }

    public void clearTable(ClearLogDTO dto) {
        String dbName = dto.getDbName();
        if (StringUtils.isNotBlank(dbName)) {
            this.doClearTable(DbNameEnum.getDbName(dbName.toUpperCase()), dto);
            return;
        }
        Set<DbNameEnum> dbNameEnums = ClearTabBizCache.table.rowKeySet();
        ArrayList<CompletableFuture<Void>> features = Lists.newArrayList();
        for (DbNameEnum dbNameEnum : dbNameEnums) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                this.doClearTable(dbNameEnum, dto);
            }, EXECUTOR);
            features.add(future);
        }
        CompletableFuture.allOf(features.toArray(new CompletableFuture[0])).join();
    }

    private void doClearTable(DbNameEnum dbNameEnum, ClearLogDTO dto) {
        Map<PartitionTypeEnum, Set<String>> row = ClearTabBizCache.table.row(dbNameEnum);
        String tableName = dto.getTableName();
        if (StringUtils.isNotBlank(tableName)) {
            //判断是天分区还是日分区
            ClearTableService clearTableService = null;
            PartitionTypeEnum partitionType = null;
            if (StringUtils.isNotBlank(dto.getPartitionType())) {
                 partitionType = PartitionTypeEnum.getByType(dto.getPartitionType().toLowerCase());
            }else {
                Set<Map.Entry<PartitionTypeEnum, Set<String>>> entries = row.entrySet();
                for (Map.Entry<PartitionTypeEnum, Set<String>> entry : entries) {
                    Set<String> tables = entry.getValue();
                    if (tables.contains(tableName)) {
                        partitionType = entry.getKey();
                        break;
                    }
                }
            }
            if (partitionType != null) {
                clearTableService = getByPartitionType(partitionType);
            }
            if (clearTableService != null) {
                clearTableService.doProcess(dto);
            }
            return;
        }
        Set<Map.Entry<PartitionTypeEnum, Set<String>>> entries = row.entrySet();
        for (Map.Entry<PartitionTypeEnum, Set<String>> entry : entries) {
            ClearLogDTO clearLogDTO = new ClearLogDTO();
            clearLogDTO.setDbName(dbNameEnum.getDbName());
            ClearTableService service = getByPartitionType(entry.getKey());
            assert service != null;
            for (String table : entry.getValue()) {
                clearLogDTO.setTableName(table);
                service.doProcess(clearLogDTO);
            }
        }
    }



    private ClearTableService getByPartitionType(PartitionTypeEnum partitionType) {
        switch (partitionType) {
            case DAILY:
                return clearTableServiceMap.get("dayPartitionClearService");
            case MONTHLY:
                return clearTableServiceMap.get("monthPartitionClearService");
            default:
                return null;
        }
    }
}