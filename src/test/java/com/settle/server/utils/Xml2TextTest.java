package com.settle.server.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/15
 * @since 1.0.0
 */
@Slf4j
public class Xml2TextTest {

    @Test
    public void eboss4_16() throws SAXException, ParserConfigurationException, IOException, TransformerException {
        log.info("eboss_4_16");

        SchemaFactory factory = SchemaFactory
                .newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

        InputStream stream = ResourceUtil.getStream("conf/xmlcfg/xsd/mc.xsd");
        StreamSource schemaFile = new StreamSource(stream);
        Schema schema = factory.newSchema(schemaFile);
        Validator validator = schema.newValidator();
//        stream.close();
        File srcFile = new File("D:\\billing\\settle\\workspace\\mc\\data\\EBOSS_BILL_BBOSS_M_DETAIL_202403.0012");
        DocumentBuilder parser = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        Document document = parser.parse(srcFile);
        validator.validate(new DOMSource(document));

        Source xmlSource = new StreamSource(srcFile);

        TransformerFactory tFactory = TransformerFactory.newInstance();
        InputStream xsltStream = ResourceUtil.getStream("conf/xmlcfg/xslt/mc.xslt");

        Transformer transformer = tFactory.newTransformer(new StreamSource(xsltStream));

        File file = new File("D:\\billing\\settle\\workspace\\mc\\data\\EBOSS_BILL_BBOSS_M_DETAIL_202403.0012.txt");
        //buffer流
        BufferedWriter writer = FileUtil.getWriter(file, "UTF-8", false);
        transformer.transform(xmlSource, new StreamResult(writer));
        log.info("eboss_4_16");
        writer.close();
    }


    @Test
    public void eboss4_29() throws Exception {
        log.info("eboss_4_29");
        SchemaFactory factory = SchemaFactory
                .newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

        InputStream stream = ResourceUtil.getStream("conf/xmlcfg/xsd/PVBill.xsd");
        StreamSource schemaFile = new StreamSource(stream);
        Schema schema = factory.newSchema(schemaFile);
        Validator validator = schema.newValidator();
//        stream.close();
        File srcFile = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.29\\EBOSS_PVBILL_BBOSS_M_DETAIL_202312.0000");
        DocumentBuilder parser = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        Document document = parser.parse(srcFile);
        validator.validate(new DOMSource(document));

        Source xmlSource = new StreamSource(srcFile);

        TransformerFactory tFactory = TransformerFactory.newInstance();
        InputStream xsltStream = ResourceUtil.getStream("conf/xmlcfg/xslt/PVBill.xslt");

        Transformer transformer = tFactory.newTransformer(new StreamSource(xsltStream));

        File file = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.29\\EBOSS_PVBILL_BBOSS_M_DETAIL_202312.0000.txt");
        //buffer流
        BufferedWriter writer = FileUtil.getWriter(file, "UTF-8", false);
        transformer.transform(xmlSource, new StreamResult(writer));
        writer.close();
    }

    @Test
    public void eboss4_30() throws Exception {
        log.info("eboss_4_30");
        SchemaFactory factory = SchemaFactory
                .newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

        InputStream stream = ResourceUtil.getStream("conf/xmlcfg/xsd/PVSettle.xsd");
        StreamSource schemaFile = new StreamSource(stream);
        Schema schema = factory.newSchema(schemaFile);
        Validator validator = schema.newValidator();
//        stream.close();
        File srcFile = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.30\\EBOSS_PVSETTLE_BBOSS_M_DETAIL_202307.0001");
        DocumentBuilder parser = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        Document document = parser.parse(srcFile);
        validator.validate(new DOMSource(document));

        Source xmlSource = new StreamSource(srcFile);

        TransformerFactory tFactory = TransformerFactory.newInstance();
        InputStream xsltStream = ResourceUtil.getStream("conf/xmlcfg/xslt/PVSettle.xslt");

        Transformer transformer = tFactory.newTransformer(new StreamSource(xsltStream));

        File file = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.30\\EBOSS_PVSETTLE_BBOSS_M_DETAIL_202307.0001.txt");
        //buffer流
        BufferedWriter writer = FileUtil.getWriter(file, "UTF-8", false);
        transformer.transform(xmlSource, new StreamResult(writer));
        writer.close();
    }

    @Test
    public void eboss4_6() throws Exception {
        log.info("eboss_4_6");
        SchemaFactory factory = SchemaFactory
                .newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

        InputStream stream = ResourceUtil.getStream("conf/xmlcfg/xsd/esp.xsd");
        StreamSource schemaFile = new StreamSource(stream);
        Schema schema = factory.newSchema(schemaFile);
        Validator validator = schema.newValidator();
//        stream.close();
        File srcFile = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.6\\ESP_ACC_E_BILL_2_202306_ZQYW.0002");
        DocumentBuilder parser = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        Document document = parser.parse(srcFile);
        validator.validate(new DOMSource(document));

        Source xmlSource = new StreamSource(srcFile);

        TransformerFactory tFactory = TransformerFactory.newInstance();
        InputStream xsltStream = ResourceUtil.getStream("conf/xmlcfg/xslt/esp.xslt");

        Transformer transformer = tFactory.newTransformer(new StreamSource(xsltStream));

        File file = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.6\\ESP_ACC_E_BILL_2_202306_ZQYW.0002.txt");
        //buffer流
        BufferedWriter writer = FileUtil.getWriter(file, "UTF-8", false);
        transformer.transform(xmlSource, new StreamResult(writer));
        writer.close();
    }


    @Test
    public void testValidXml() throws SAXException, ParserConfigurationException, IOException {
        SchemaFactory factory = SchemaFactory
                .newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

        InputStream stream = ResourceUtil.getStream("conf/xmlcfg/xsd/mc.xsd");
        StreamSource schemaFile = new StreamSource(stream);
        Schema schema = factory.newSchema(schemaFile);
        stream.close();
        Validator validator = schema.newValidator();
//        stream.close();
        File srcFile = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.16\\EBOSS_BILL_BBOSS_M_DETAIL_202403.0012");
        DocumentBuilder parser = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        Document document = parser.parse(srcFile);
        validator.validate(new DOMSource(document));
    }

    @Test
    public void testTransformXml() throws Exception {
        Source xmlSource = new StreamSource(new File("D:\\000zy\\Desktop\\xslt\\报文\\4.16\\EBOSS_BILL_BBOSS_M_DETAIL_202403.0012"));

        TransformerFactory tFactory = TransformerFactory.newInstance();
        InputStream xsltStream = ResourceUtil.getStream("conf/xmlcfg/xslt/mc.xslt");

        Transformer transformer = tFactory.newTransformer(new StreamSource(xsltStream));
        xsltStream.close();
        File file = new File("D:\\000zy\\Desktop\\xslt\\报文\\4.16\\EBOSS_BILL_BBOSS_M_DETAIL_202403.0012.txt");
        //buffer流
        BufferedWriter writer = FileUtil.getWriter(file, "UTF-8", false);
        transformer.transform(xmlSource, new StreamResult(writer));
        writer.close();
    }
}