package com.settle.server.module.cdn.writer;


import com.settle.server.module.cdn.record.Batch;

/**
 * 所有 RecordWriter 的接口
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public interface RecordWriter<P> {

    /**
     * 打开写入器
     *
     * @throws Exception 如果在打开写入器过程中发生错误
     */
    default void open() throws Exception {
        // no-op
    }

    /**
     * 将一批记录写入数据接收器
     *
     * @param batch 要写入的记录
     * @throws Exception 如果在记录写入过程中发生错误
     */
    void writeRecords(Batch<P> batch) throws Exception;

    /**
     * 关闭写入器
     *
     * @throws Exception 如果在关闭写入器期间发生错误
     */
    default void close() throws Exception {
        // no-op
    }
}
