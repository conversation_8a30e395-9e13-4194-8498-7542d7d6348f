package com.settle.server.module.cdn.writer;

import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.retry.RetryPolicy;
import com.settle.server.module.cdn.retry.RetryTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

/**
 * 当数据接收器暂时不可用时，装饰器使 {@link RecordWriter} 可重试
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class RetryableRecordWriter<P> implements RecordWriter<P> {

    private RecordWriter<P> delegate;
    private RecordWritingTemplate recordWritingTemplate;

    /**
     * 创建一个新的 {@link RetryableRecordWriter}
     *
     * @param delegate    记录写入器
     * @param retryPolicy 重试策略
     */
    public RetryableRecordWriter(RecordWriter<P> delegate, RetryPolicy retryPolicy) {
        this.delegate = delegate;
        this.recordWritingTemplate = new RecordWritingTemplate(retryPolicy);
    }

    @Override
    public void open() throws Exception {
        delegate.open();
    }

    @Override
    public void writeRecords(Batch<P> batch) throws Exception {
        recordWritingTemplate.execute(new RecordWritingCallable(delegate, batch));
    }

    @Override
    public void close() throws Exception {
        delegate.close();
    }

    private static class RecordWritingCallable implements Callable<Void> {

        private RecordWriter recordWriter;

        private Batch batch;

        RecordWritingCallable(RecordWriter recordWriter, Batch batch) {
            this.recordWriter = recordWriter;
            this.batch = batch;
        }

        @Override
        public Void call() throws Exception {
            recordWriter.writeRecords(batch);
            return null;
        }

    }

    private static class RecordWritingTemplate extends RetryTemplate {

        private final Logger LOGGER = LoggerFactory.getLogger(RecordWritingTemplate.class.getName());

        RecordWritingTemplate(RetryPolicy retryPolicy) {
            super(retryPolicy);
        }

        @Override
        protected void beforeCall() {
            // no op
        }

        @Override
        protected void afterCall(Object result) {
            // no op
        }

        @Override
        protected void onException(Exception e) {
            LOGGER.error("Unable to write records", e);
        }

        @Override
        protected void onMaxAttempts(Exception e) {
            LOGGER.error("Unable to write records after {} attempt(s)", retryPolicy.getMaxAttempts());
        }

        @Override
        protected void beforeWait() {
            LOGGER.debug("Waiting for {} {} before retrying to write records", retryPolicy.getDelay(), retryPolicy.getTimeUnit());
        }

        @Override
        protected void afterWait() {
            // no op
        }

    }
}
