package com.settle.server.module.cdn.worker;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.LineSeparator;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.setting.Setting;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.constant.RuleConst;
import com.settle.server.module.cdn.jdbc.JdbcDefaultRecordMapper;
import com.settle.server.module.cdn.jdbc.JdbcRecordReader;
import com.settle.server.module.cdn.job.Job;
import com.settle.server.module.cdn.job.JobBuilder;
import com.settle.server.module.cdn.listener.Db2CsvFileLister;
import com.settle.server.module.cdn.listener.FileListener;
import com.settle.server.module.cdn.processor.SpecRecordProcessor;
import com.settle.server.module.cdn.writer.FileRecordWriter;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库 -> CSV文件
 *
 * <AUTHOR>
 */
@Service
public class DbToCsvWorkerJob<I, O> implements WorkerJob {

    private DataSource dataSource;
    private String jobName;
    private String ruleGroup;
    private String delimiter;

    private String lineSeparator;
    private Setting setting;
    private Path outGoingPath;

    private JdbcRecordReader jdbcRecordReader;
    private FileListener fileListener;

    private FileRecordWriter fileRecordWriter;

    public DbToCsvWorkerJob() {
        //no op
    }

    public DbToCsvWorkerJob(final String ruleGroup, final Setting setting, DataSource stludrDataSource) {
        this.ruleGroup = ruleGroup;
        this.setting = setting;
        this.dataSource = stludrDataSource;
    }

    @Override
    public void beforeBuildWorkerJob() {
        //把年月日拆开了,{mm:4,dd:30,yyyyMMdd:20240430,yyyyMM:202404}
        Map<String, String> variableMap = buildVariableMap();
        String exportSql = setting.getByGroup(RuleConst.G_K_EXPORT_SQL, this.ruleGroup);
        exportSql = StrUtil.format(exportSql, variableMap);

        //数据源
//        DataSource dataSource = DSFactory.get(setting.getByGroup(RuleConst.G_K_DB_GROUP, this.ruleGroup));
        this.jdbcRecordReader = new JdbcRecordReader(dataSource, exportSql);

        //输出文件名
        String outFileName = this.setting.getStr(RuleConst.G_K_FILE_NAME_REG, this.ruleGroup, NormalConst.DEFAULT_OUT_FILE_NAME);
        outFileName = StrUtil.format(outFileName, variableMap)+".tmp";
        this.jobName = outFileName;

        //输出路径
        String outPath = this.setting.getStr(RuleConst.G_K_FILE_OUT_PATH, this.ruleGroup, NormalConst.DEFAULT_FILE_OUT_PATH);
        File outGoingFile = FileUtil.file(outPath, outFileName);
        //创建文件夹目录
        FileUtil.touch(outGoingFile);

        this.outGoingPath = Paths.get(outGoingFile.getAbsolutePath());
        this.delimiter = this.setting.getStr(RuleConst.G_K_DELIMITER, this.ruleGroup, StrUtil.COMMA);
        this.fileRecordWriter = new FileRecordWriter(outGoingPath);

        this.lineSeparator = this.setting.getStr(RuleConst.G_K_LINE_SEPARATOR, this.ruleGroup, System.getProperty("line.separator"));
        if (CharSequenceUtil.equalsIgnoreCase(lineSeparator, "CRLF")) {
            this.lineSeparator = LineSeparator.WINDOWS.getValue();
        }
        this.fileRecordWriter.setLineSeparator(lineSeparator);

        String logId = IdUtil.getSnowflake(1L, 1L).nextIdStr();
        this.fileListener = new Db2CsvFileLister(logId, outGoingFile, this.ruleGroup, this.setting,this.dataSource);
    }

    @Override
    public Job buildWorkerJob() {
        return new JobBuilder<ResultSet, String>()
                .named(this.jobName)
                .fileListener(this.fileListener)
                .reader(this.jdbcRecordReader)
                .mapper(new JdbcDefaultRecordMapper(this.delimiter))
                .processor(new SpecRecordProcessor())
                .writer(fileRecordWriter)
                .build();
    }

    private Map<String, String> buildVariableMap() {
        Map<String, String> variableMap = new HashMap<>(16);
        String acctMonth = setting.get(null, NormalConst.ARG_ACCT_MONTH);
        if (StrUtil.isNotEmpty(acctMonth)) {
            if (CharSequenceUtil.equalsIgnoreCase(acctMonth, DatePattern.SIMPLE_MONTH_PATTERN)) {
                acctMonth = DateUtil.format(DateUtil.lastMonth(), DatePattern.SIMPLE_MONTH_PATTERN);
            }
            variableMap.put(DatePattern.SIMPLE_MONTH_PATTERN, acctMonth);
            variableMap.put("mm", String.valueOf(Integer.parseInt(acctMonth.substring(4, 6))));
        } else {
            DateTime dateTimeNow = DateTime.now();
            //yyyyMM
            variableMap.putIfAbsent(DatePattern.SIMPLE_MONTH_PATTERN, dateTimeNow.toString(DatePattern.SIMPLE_MONTH_PATTERN));
            variableMap.putIfAbsent("mm", String.valueOf(Integer.parseInt(dateTimeNow.toString("mm"))));
        }

        //入参
        String acctMonthDay = setting.get(null, NormalConst.ARG_ACCT_MONTH_DAY);
        if (StrUtil.isNotEmpty(acctMonthDay)) {
            if (CharSequenceUtil.equalsIgnoreCase(acctMonthDay, DatePattern.PURE_DATE_PATTERN)) {
                acctMonthDay = DateUtil.format(DateUtil.yesterday(), DatePattern.PURE_DATE_PATTERN);
            }

            variableMap.put(DatePattern.PURE_DATE_PATTERN, acctMonthDay);
            variableMap.put(DatePattern.SIMPLE_MONTH_PATTERN, acctMonthDay.substring(0, 6));
            variableMap.put("mm", String.valueOf(Integer.parseInt(acctMonthDay.substring(4, 6))));
            variableMap.put("dd", String.valueOf(Integer.parseInt(acctMonthDay.substring(6, 8))));
        } else {
            DateTime dateTimeNow = DateTime.now();
            //yyyyMMdd
            variableMap.putIfAbsent(DatePattern.PURE_DATE_PATTERN, dateTimeNow.toString(DatePattern.PURE_DATE_PATTERN));
            variableMap.putIfAbsent(DatePattern.SIMPLE_MONTH_PATTERN, dateTimeNow.toString(DatePattern.SIMPLE_MONTH_PATTERN));
            variableMap.putIfAbsent("mm", String.valueOf(Integer.parseInt(dateTimeNow.toString("mm"))));
            variableMap.putIfAbsent("dd", String.valueOf(Integer.parseInt(dateTimeNow.toString("dd"))));
        }

        return variableMap;
    }
}
