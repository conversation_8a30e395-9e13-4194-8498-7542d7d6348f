package com.settle.server.module.cdn.converter;

import com.settle.server.utils.Utils;

import java.util.Date;
import java.util.GregorianCalendar;

/**
 * {@link java.util.Calendar} 类型转换器
 * <p>
 * 用于将原始字符串转换为公历实例
 *
 * <AUTHOR>
 */
public class GregorianCalendarTypeConverter implements TypeConverter<String, GregorianCalendar> {

    private DateTypeConverter dateTypeConverter;

    public GregorianCalendarTypeConverter() {
        this.dateTypeConverter = new DateTypeConverter();
    }

    public GregorianCalendarTypeConverter(String dateFormat) {
        this.dateTypeConverter = new DateTypeConverter(dateFormat);
    }

    @Override
    public GregorianCalendar convert(String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        Date date = dateTypeConverter.convert(value);
        GregorianCalendar gregorianCalendar = new GregorianCalendar();
        gregorianCalendar.setTime(date);
        return gregorianCalendar;
    }
}
