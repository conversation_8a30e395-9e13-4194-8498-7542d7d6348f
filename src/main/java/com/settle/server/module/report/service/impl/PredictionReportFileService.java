package com.settle.server.module.report.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.module.report.config.ReportConfig;
import com.settle.server.module.report.dto.ReportDTO;
import com.settle.server.module.report.service.ReportFileService;
import com.settle.server.utils.ExceptionCast;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/9
 * @since 1.0.0
 */
@Service("predictionReportFileService")
@Slf4j
public class PredictionReportFileService implements ReportFileService {

    @Autowired
    private ReportConfig reportConfig;

    @Override
    public String reportFile(List<ReportDTO> reportDTOList)  {
        if (StringUtils.isBlank(reportConfig.getRemoteUrl())) {
            ExceptionCast.cast("报表服务远程地址为空");
        }
        return doFile(reportDTOList.get(0));
    }

    //http://localhost:9232/exportRaq?settlemonth=202310&raqType=1
    private String doFile(ReportDTO reportDTO) {
        //1.请求report服务，生成报表
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("settlemonth", reportDTO.getSettleMonth());
        paramMap.put("raqType", reportDTO.getRaqType());
        paramMap.put("raqKey", reportDTO.getRaqKey());
        XxlJobHelper.log("请求地址：{},请求参数：{}", getUrl(),paramMap);
        String json = HttpUtil.get(getUrl(), paramMap, reportConfig.getTimeout() * 2);
        log.info("请求报表服务返回结果:{}", json);
        if (StrUtil.isBlank(json)) {
            return "";
        }
        JSONObject jsonObject = JSONUtil.parseObj(json);
        if (jsonObject.getStr("code") != null && !jsonObject.getStr("code").equals("200")) {
            ExceptionCast.cast("请求报表服务失败. %s", jsonObject.getStr("msg"));
        }
        return jsonObject.getStr("data");

    }

    private String getUrl() {
        String remoteUrl = reportConfig.getRemoteUrl();
        if (!remoteUrl.endsWith("/")) {
            remoteUrl+="/";
        }
        String predictionUri = reportConfig.getPredictionUri();
        if (StringUtils.isNotBlank(predictionUri) && predictionUri.startsWith("/")) {
            predictionUri = predictionUri.substring(1);
        }
        String s = remoteUrl + predictionUri;
        //去掉最后一个
        if (s.endsWith("/")) {
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }
}