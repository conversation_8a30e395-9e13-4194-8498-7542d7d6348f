package com.settle.server.module.report.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.module.report.config.ReportConfig;
import com.settle.server.module.report.dto.ReportDTO;
import com.settle.server.module.report.service.ReportFileService;
import com.settle.server.utils.ExceptionCast;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/23
 * @since 1.0.0
 */
@Service("evictReportFileService")
@Slf4j
public class EvictReportFileService implements ReportFileService {

    @Autowired
    private ReportConfig reportConfig;

    @Override
    public String reportFile(List<ReportDTO> reportDTOList)  {
        if (StringUtils.isBlank(reportConfig.getRemoteUrl())) {
            ExceptionCast.cast("报表服务远程地址为空");
        }
        return doFile(reportDTOList.get(0));
    }

    //http://127.0.0.1:9232/settleReport/api/evictReportBatch
    private String doFile(ReportDTO reportDTO) {
        //1.请求report服务，生成报表
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("settlemonth", reportDTO.getSettleMonth());
        paramMap.put("raqList", reportDTO.getRaqKey());
        String jsonStr = JSONUtil.toJsonStr(paramMap);
        XxlJobHelper.log("请求地址：{},请求参数：{}", getUrl(),jsonStr);
        //链式构建请求
        String result = HttpRequest.post(getUrl())
                .body(jsonStr)//表单内容
                .timeout(20000)//超时，毫秒
                .execute().body();

        log.info("请求报表服务返回结果:{}", result);
        if (StrUtil.isBlank(result)) {
            return "";
        }
        JSONObject jsonObject = JSONUtil.parseObj(result);

        if (jsonObject.getStr("code") != null && !jsonObject.getStr("code").equals("200")) {
            ExceptionCast.cast("请求报表服务失败. %s", jsonObject.getStr("msg"));
        }
        return jsonObject.getStr("data");

    }

    private String getUrl() {
        String remoteUrl = reportConfig.getRemoteUrl();
        if (!remoteUrl.endsWith("/")) {
            remoteUrl+="/";
        }
        String evictUri = reportConfig.getEvictUri();
        if (StringUtils.isNotBlank(evictUri) && evictUri.startsWith("/")) {
            evictUri = evictUri.substring(1);
        }
        String s = remoteUrl + evictUri;
        //去掉最后一个
        if (s.endsWith("/")) {
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }
}