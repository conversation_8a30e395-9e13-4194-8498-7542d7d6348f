package com.settle.server.module.cdn.worker;


import com.settle.server.module.cdn.job.Job;

/**
 * <AUTHOR>
 */
public interface WorkerJob {

    /**
     * 创建作业之前调用
     *
     * @throws Exception 异常
     */
    default void beforeBuildWorkerJob() {
        // no-op
    }

    /**
     * 缺省处理
     */
    default Job buildWorkerJob() {
        return null;
    }

    /**
     * 创建作业之后调用
     *
     * @throws Exception 异常
     */
    default void afterBuildWorkerJob() {
        // no-op
    }
}
