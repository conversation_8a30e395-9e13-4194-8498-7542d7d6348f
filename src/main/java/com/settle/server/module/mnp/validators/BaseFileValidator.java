package com.settle.server.module.mnp.validators;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.settle.server.module.mnp.dto.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public abstract class BaseFileValidator implements FileValidator {

    @Override
    public MessageResult validateFileContent(List<String> fieldList, String[] fieldValues) {
        //校验字段长度是否一致
        return this.executeValidationAndGetResult(fieldList, fieldValues);
    }

    private MessageResult executeValidationAndGetResult(List<String> fieldList, String[] fieldValues)  {
        MessageResult msgResult = this.validateFieldValueCnt(fieldList, fieldValues);
        if (msgResult.isSuccess()) {
            Map<String, String> fieldMap = this.convertToMap(fieldList, fieldValues);
            msgResult = this.validateFields(fieldMap);
        }

        return msgResult;
    }

    private MessageResult validateFieldValueCnt(List<String> fieldList, String[] fieldValues) {
        if (fieldList.size() - 1 == fieldValues.length) {
            return MessageResult.success();
        } else {
            return MessageResult.error(MessageResult.FAIL_001, "the number of fields is not equal to " + fieldList.size());
        }
    }

    protected MessageResult validateFields(Map fieldMap) {
        if (StrUtil.byteLength((CharSequence)fieldMap.get("START_PROV"), Charset.forName("GBK")) > 3
                || StrUtil.byteLength((CharSequence)fieldMap.get("END_PROV"), Charset.forName("GBK")) > 3) {
            return MessageResult.error(MessageResult.FAIL_002, "province code length is greater than 3");
        }
        return MessageResult.success();
    }

    private Map<String, String> convertToMap(List<String> fieldList, String[] fieldValues) {
        Map<String, String> fieldMap = Maps.newHashMap();

        for(int i = 0; i < fieldValues.length; ++i) {
            fieldMap.put(fieldList.get(i), fieldValues[i]);
        }
        return fieldMap;
    }
}

