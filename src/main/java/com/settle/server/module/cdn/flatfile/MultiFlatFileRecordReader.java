package com.settle.server.module.cdn.flatfile;


import com.settle.server.module.cdn.reader.AbstractFileRecordReader;
import com.settle.server.module.cdn.reader.AbstractMultiFileRecordReader;

import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.List;

/**
 * 一次读取多个平面文件。
 * 文件必须具有相同的格式。
 *
 * <AUTHOR>
 */
public class MultiFlatFileRecordReader extends AbstractMultiFileRecordReader<String> {

    /**
     * 创建一个新的 {@link MultiFlatFileRecordReader}
     *
     * @param files 要读取的文件
     */
    public MultiFlatFileRecordReader(final List<Path> files) {
        this(files, Charset.defaultCharset());
    }

    /**
     * 创建一个新的 {@link MultiFlatFileRecordReader}
     *
     * @param files   要读取的文件
     * @param charset 文件的字符集
     */
    public MultiFlatFileRecordReader(final List<Path> files, final Charset charset) {
        super(files, charset);
    }

    @Override
    protected AbstractFileRecordReader<String> createReader() {
        return new FlatFileRecordReader(currentFile, charset);
    }
}
