package com.settle.server.module.cdn.processor;


import com.settle.server.module.cdn.record.Record;
import com.settle.server.module.cdn.record.StringRecord;

/**
 * IDD处理
 *
 * <AUTHOR>
 */
public class SpecRecordProcessor implements RecordProcessor<String, String> {

    public SpecRecordProcessor() {
        //no op
    }


    @Override
    public Record<String> processRecord(Record<String> vRecord) throws Exception {
        return new StringRecord(vRecord.getHeader(), vRecord.getPayload().replace("null", ""));
    }
}
