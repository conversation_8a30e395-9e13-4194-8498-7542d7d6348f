package com.settle.server.service.impl;

import com.settle.server.dao.stludr.RptP2cLimitedMapper;
import com.settle.server.dao.stludr.RvlLimitFeeMapper;
import com.settle.server.dao.stludr.RvlLimitUsageMapper;
import com.settle.server.entity.RptP2cLimited;
import com.settle.server.entity.RvlLimitFee;
import com.settle.server.entity.RvlLimitUsage;
import com.settle.server.service.RptP2cLimitedSpecialService;
import com.xxl.job.core.context.XxlJobHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Service
public class RptP2cLimitedSpecialServiceImpl implements RptP2cLimitedSpecialService {
    @Autowired
    private RvlLimitUsageMapper rvlLimitUsageMapper;
    @Autowired
    private RptP2cLimitedMapper p2cLimitedMapper;
    @Autowired
    private RvlLimitFeeMapper rvlLimitFeeMapper;

    @Override
    public void processData() {
        List<RptP2cLimited> rptP2cLimiteds = p2cLimitedMapper.selectAll();
        XxlJobHelper.log("所有出账数据条数：{}", rptP2cLimiteds.size());

        rptP2cLimiteds.forEach(l -> {
            long bdFee = rvlLimitFeeMapper.selectLimitFee(l.getProvCd(), l.getFloorRule());
            long sxFee = rvlLimitFeeMapper.selectLimitFee(l.getProvCd(), l.getCeilingRule());
            long reportFee = l.getReportFee();
            long accuFee = p2cLimitedMapper.selectAccuFee(l.getProvCd(), l.getRepNum(), l.getColName());
            BigDecimal finalFee = new BigDecimal(reportFee).subtract(new BigDecimal(accuFee).multiply(new BigDecimal("0.03")));
            long roundedFinalFee = finalFee.setScale(0, RoundingMode.HALF_UP).longValue();
            if (accuFee > bdFee && accuFee < sxFee) {
                XxlJobHelper.log(" ProvCd:{}, RepNum:{}, ColName:{}, type:{}",roundedFinalFee, l.getProvCd(), l.getRepNum(), l.getColName(),"11");
                p2cLimitedMapper.updateType(l.getProvCd(), l.getRepNum(), l.getColName(), "11");

            }
            XxlJobHelper.log("roundedFinalFee:{}, ProvCd:{}, RepNum:{}, ColName:{}",roundedFinalFee, l.getProvCd(), l.getRepNum(), l.getColName());
            p2cLimitedMapper.updateLimited(roundedFinalFee, l.getProvCd(), l.getRepNum(), l.getColName());
        });

    }
}
