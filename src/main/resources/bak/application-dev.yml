server:
  port: 9131

log:
  config: classpath:logback-spring.xml
  path: ./apps/boss/logs/${spring.application.name}

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

jdbc:
  mnpserver:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************
    username: stlusers
    password: 'EiQFlH%xu&'
  sttleserver:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************
    username: stlusers
    password: 'EiQFlH%xu&'
  stludrserver:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************************
    username: stludr
    password: 'EiQFlH%xu&'
  ds1:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: boss_billing
    password: 'EiQFlH%xu&'
    maxActive: 300
    initialSize: 1
    minIdle: 0
    maxWait: 30000
    timeBetweenEvictionRunsMillis: 6000
    minEvictableIdleTimeMillis: 30000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false


mnpserver:
  dataPath: /home/<USER>/bl1/data/MNPSerer/data
  backupPath: /home/<USER>/bl1/data/MNPSerer/backupData
  errorPath: /home/<USER>/bl1/data/MNPSerer/error
  tablePath: /home/<USER>/bl1/data/MNPSerer/conf/table
settleserver:
  COMMIT_COUNT: 1000
  dataPath: /home/<USER>/bl1/data/SettleServer/data
  backupPath: /home/<USER>/bl1/data/SettleServer/backupData
  ex_orig_call_center: settle_orig_ccdc_YYYYMM.txt
  ex_orig_inter_line: settle_orig_apdl_internet_YYYYMM.txt
  ex_orig_line: settle_orig_apdl_data_YYYYMM.txt
  ex_inter_ent_tv: settle_inter_ent_tv_YYYYMM.txt
  confTable: /home/<USER>/bl1/data/SettleServer/tablePath
#测试环境地址
ftp:
  os:
    host: ************
    port: 12002
    username: BBSS
    password: ENC(iwK9YFLen0Yb5bRNBt8s3G7TFDdLUdKd)
    remoteUploadPath:
      CARD: /incoming/bbssdcsy/CARD
    remoteDownloadPath:
      CARD: /outgoing/bbssdcsy/CARD
    tempDir: /home/<USER>/bl1/data/Cmiot/cardTemp
    bakDir: /home/<USER>/bl1/data/Cmiot/cardTemp_bak
    feedBackDir: /home/<USER>/bl1/data/Cmiot/cardTemp_feedBack
  ht:
    tempDir: /home/<USER>/bl1/data/Cmiot/bigDataTemp
    gzBakDir: /home/<USER>/bl1/data/Cmiot/bigDataGzTemp_bak
    bakDir: /home/<USER>/bl1/data/Cmiot/bigDataTemp_bak
    feedBackDir: /home/<USER>/bl1/data/Cmiot/bigDataTemp_feedBack