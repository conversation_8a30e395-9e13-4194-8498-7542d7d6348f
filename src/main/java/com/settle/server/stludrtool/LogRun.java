package com.settle.server.stludrtool;

import java.sql.SQLException;
import java.util.Date;
import java.util.Locale;

import com.settle.server.pojo.StludrLogModel;
import com.settle.server.utils.ConfUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LogRun {
	private static final String LOG_SQL = "INSERT INTO SYNC_INTERFACE_STLUDR_LOG(LOG_ID, STL_MONTH, STATUS, FILENAME, LOG_INFORMATION, LOG_TIME)VALUES(SEQ_SYNC_INTERFACE_STLUDR_LOG.NEXTVAL,?, ?, ?, ?, ?)";
	private static final String[] LOG4J = { "ERROR", "INFO", "DEBUG" };

	private static String getStr(String msg) {
		int count = msg.length();
		count = (count < 2000) ? count : 2000;
		return msg.substring(0, count);
	}

	private static boolean flag(String logger) {
		String log4j = ConfUtil.LOG4J_LOGGER[0].trim();
		int x = 0, y = 0;
		for (int i = 0; i < LOG4J.length; i++) {
			if (logger.equalsIgnoreCase(LOG4J[i])) {
				x = i;
			}
			if (log4j.equalsIgnoreCase(LOG4J[i])) {
				y = i;
			}
		}
		return (x <= y) ? true : false;
	}

	public static void logError(String month, String fileName, String msg) {
		if (flag(LOG4J[0])) {
			StludrLogModel log = new StludrLogModel(month, fileName, getStr(msg));
			log.setStatus("ERROR");
			log(log);
		}
	}

	public static void logDebug(String month, String fileName, String msg) {
		if (flag(LOG4J[2])) {
			StludrLogModel log = new StludrLogModel(month, fileName, getStr(msg));
			log.setStatus("DEBUG");
			log(log);
		}
	}

	public static void logInfo(String month, String fileName, String msg) {
		if (flag(LOG4J[1])) {
			StludrLogModel log = new StludrLogModel(month, fileName, getStr(msg));
			log.setStatus("INFO");
			log(log);
		}
	}

	public static void log(StludrLogModel log) {
		LogRun run = new LogRun();
		Date date = new Date();
		String time = String.format(Locale.US, "%tF %tT.%tL", date, date, date);
		log.setLogTime(time);
		run.logToDB(log);
	}

	public void logToDB(StludrLogModel logger) {
		StludrToolDBUtil ju = new StludrToolDBUtil();
		try {
			ju.setPre(ju.getConn().prepareStatement(LOG_SQL));
			ju.getPre().setString(1, logger.getStlMonth());
			ju.getPre().setString(2, logger.getStatus());
			ju.getPre().setString(3, logger.getFileName());
			ju.getPre().setString(4, logger.getLogInformation());
			ju.getPre().setString(5, logger.getLogTime());
			ju.getPre().execute();
		} catch (SQLException e) {
			log.error(e.getMessage());
		} finally {
			ju.close();
		}
	}

	public static void main(String[] args) {
		LogRun.logInfo("201712", "info.dat", "info Log message ");
		LogRun.logDebug("201712", "debug.dat", "debug Log message ");
		LogRun.logError("201712", "error.dat", "error Log message ");
	}
}
