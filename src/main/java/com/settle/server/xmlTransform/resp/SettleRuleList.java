/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 1.2</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.resp;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;

/**
 * Comment describing your root element
 * 
 * @version $Revision$ $Date$
 */
public class SettleRuleList implements java.io.Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _org_FileName.
     */
    private String _org_FileName;

    /**
     * Field _resp_Date.
     */
    private String _resp_Date;

    /**
     * Field _fileStatus.
     */
    private String _fileStatus;

    /**
     * Field _errorCode.
     */
    private String _errorCode;

    /**
     * Field _errorNode.
     */
    private String _errorNode;

    /**
     * Field _errorRecords.
     */
    private ErrorRecords _errorRecords;


      //----------------/
     //- Constructors -/
    //----------------/

    public SettleRuleList() {
        super();
    }


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * Returns the value of field 'errorCode'.
     * 
     * @return the value of field 'ErrorCode'.
     */
    public String getErrorCode(
    ) {
        return this._errorCode;
    }

    /**
     * Returns the value of field 'errorNode'.
     * 
     * @return the value of field 'ErrorNode'.
     */
    public String getErrorNode(
    ) {
        return this._errorNode;
    }

    /**
     * Returns the value of field 'errorRecords'.
     * 
     * @return the value of field 'ErrorRecords'.
     */
    public ErrorRecords getErrorRecords(
    ) {
        return this._errorRecords;
    }

    /**
     * Returns the value of field 'fileStatus'.
     * 
     * @return the value of field 'FileStatus'.
     */
    public String getFileStatus(
    ) {
        return this._fileStatus;
    }

    /**
     * Returns the value of field 'org_FileName'.
     * 
     * @return the value of field 'Org_FileName'.
     */
    public String getOrg_FileName(
    ) {
        return this._org_FileName;
    }

    /**
     * Returns the value of field 'resp_Date'.
     * 
     * @return the value of field 'Resp_Date'.
     */
    public String getResp_Date(
    ) {
        return this._resp_Date;
    }

    /**
     * Method isValid.
     * 
     * @return true if this object is valid according to the schema
     */
    public boolean isValid(
    ) {
        try {
            validate();
        } catch (org.exolab.castor.xml.ValidationException vex) {
            return false;
        }
        return true;
    }

    /**
     * 
     * 
     * @param out
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void marshal(
            final java.io.Writer out)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, out);
    }

    /**
     * 
     * 
     * @param handler
     * @throws java.io.IOException if an IOException occurs during
     * marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     */
    public void marshal(
            final org.xml.sax.ContentHandler handler)
    throws java.io.IOException, org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, handler);
    }

    /**
     * Sets the value of field 'errorCode'.
     * 
     * @param errorCode the value of field 'errorCode'.
     */
    public void setErrorCode(
            final String errorCode) {
        this._errorCode = errorCode;
    }

    /**
     * Sets the value of field 'errorNode'.
     * 
     * @param errorNode the value of field 'errorNode'.
     */
    public void setErrorNode(
            final String errorNode) {
        this._errorNode = errorNode;
    }

    /**
     * Sets the value of field 'errorRecords'.
     * 
     * @param errorRecords the value of field 'errorRecords'.
     */
    public void setErrorRecords(
            final ErrorRecords errorRecords) {
        this._errorRecords = errorRecords;
    }

    /**
     * Sets the value of field 'fileStatus'.
     * 
     * @param fileStatus the value of field 'fileStatus'.
     */
    public void setFileStatus(
            final String fileStatus) {
        this._fileStatus = fileStatus;
    }

    /**
     * Sets the value of field 'org_FileName'.
     * 
     * @param org_FileName the value of field 'org_FileName'.
     */
    public void setOrg_FileName(
            final String org_FileName) {
        this._org_FileName = org_FileName;
    }

    /**
     * Sets the value of field 'resp_Date'.
     * 
     * @param resp_Date the value of field 'resp_Date'.
     */
    public void setResp_Date(
            final String resp_Date) {
        this._resp_Date = resp_Date;
    }

    /**
     * Method unmarshal.
     * 
     * @param reader
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @return the unmarshaled com.hp.xmlusage.resp.SettleRuleList
     */
    public static SettleRuleList unmarshal(
            final java.io.Reader reader)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        return (SettleRuleList) Unmarshaller.unmarshal(SettleRuleList.class, reader);
    }

    /**
     * 
     * 
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void validate(
    )
    throws org.exolab.castor.xml.ValidationException {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    }

}
