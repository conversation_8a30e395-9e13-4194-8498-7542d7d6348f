package com.settle.server.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.service.MembershipService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/7
 * @since 1.0.0
 */
@Slf4j
@Component
public class MemberShipSettleJob {

    @Autowired
    private MembershipService membershipService;

    @XxlJob("memberShipHandler")
    public ReturnT<String> memberShipHandler() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("成员权益领取文件,[MembershipInterests]处理开始，接受参数：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            log.info("当前账期：{}，开始执行定时任务，定时任务执行时间: {}", acctMonth, LocalDateTime.now());
            XxlJobHelper.log("当前账期：{}", acctMonth);
            membershipService.save(acctMonth);
            XxlJobHelper.log("成员权益领取文件,[MembershipInterests]文件处理结束");
        } catch (Exception re) {
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("成员权益领取文件,[MembershipInterests]文件处理失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }
}