/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 0.9.6</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.xml2text.config;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import java.io.IOException;
import java.io.Reader;
import java.io.Serializable;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Enumeration;
import org.exolab.castor.xml.MarshalException;
import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;
import org.exolab.castor.xml.ValidationException;
import org.xml.sax.ContentHandler;

/**
 * Class Category.
 * 
 * @version $Revision$ $Date$
 */
public class Category implements Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _entryList
     */
    private ArrayList _entryList;


      //----------------/
     //- Constructors -/
    //----------------/

    public Category() {
        super();
        _entryList = new ArrayList();
    } //-- com.hp.xmlusage.xml2text.config.Category()


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * Method addEntry
     * 
     * 
     * 
     * @param vEntry
     */
    public void addEntry(Entry vEntry)
        throws IndexOutOfBoundsException
    {
        _entryList.add(vEntry);
    } //-- void addEntry(com.hp.xmlusage.xml2text.config.Entry) 

    /**
     * Method addEntry
     * 
     * 
     * 
     * @param index
     * @param vEntry
     */
    public void addEntry(int index, Entry vEntry)
        throws IndexOutOfBoundsException
    {
        _entryList.add(index, vEntry);
    } //-- void addEntry(int, com.hp.xmlusage.xml2text.config.Entry) 

    /**
     * Method clearEntry
     * 
     */
    public void clearEntry()
    {
        _entryList.clear();
    } //-- void clearEntry() 

    /**
     * Method enumerateEntry
     * 
     * 
     * 
     * @return Enumeration
     */
    public Enumeration enumerateEntry()
    {
        return new org.exolab.castor.util.IteratorEnumeration(_entryList.iterator());
    } //-- java.util.Enumeration enumerateEntry() 

    /**
     * Method getEntry
     * 
     * 
     * 
     * @param index
     * @return Entry
     */
    public Entry getEntry(int index)
        throws IndexOutOfBoundsException
    {
        //-- check bounds for index
        if ((index < 0) || (index > _entryList.size())) {
            throw new IndexOutOfBoundsException();
        }
        
        return (Entry) _entryList.get(index);
    } //-- com.hp.xmlusage.xml2text.config.Entry getEntry(int) 

    /**
     * Method getEntry
     * 
     * 
     * 
     * @return Entry
     */
    public Entry[] getEntry()
    {
        int size = _entryList.size();
        Entry[] mArray = new Entry[size];
        for (int index = 0; index < size; index++) {
            mArray[index] = (Entry) _entryList.get(index);
        }
        return mArray;
    } //-- Entry[] getEntry()

    /**
     * Method getEntryCount
     * 
     * 
     * 
     * @return int
     */
    public int getEntryCount()
    {
        return _entryList.size();
    } //-- int getEntryCount() 

    /**
     * Method isValid
     * 
     * 
     * 
     * @return boolean
     */
    public boolean isValid()
    {
        try {
            validate();
        }
        catch (ValidationException vex) {
            return false;
        }
        return true;
    } //-- boolean isValid() 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param out
     */
    public void marshal(Writer out)
        throws MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, out);
    } //-- void marshal(java.io.Writer) 

    /**
     * Method marshal
     * 
     * 
     * 
     * @param handler
     */
    public void marshal(ContentHandler handler)
        throws IOException, MarshalException, ValidationException
    {
        
        Marshaller.marshal(this, handler);
    } //-- void marshal(org.xml.sax.ContentHandler) 

    /**
     * Method removeEntry
     * 
     * 
     * 
     * @param vEntry
     * @return boolean
     */
    public boolean removeEntry(Entry vEntry)
    {
        boolean removed = _entryList.remove(vEntry);
        return removed;
    } //-- boolean removeEntry(com.hp.xmlusage.xml2text.config.Entry) 

    /**
     * Method setEntry
     * 
     * 
     * 
     * @param index
     * @param vEntry
     */
    public void setEntry(int index, Entry vEntry)
        throws IndexOutOfBoundsException
    {
        //-- check bounds for index
        if ((index < 0) || (index > _entryList.size())) {
            throw new IndexOutOfBoundsException();
        }
        _entryList.set(index, vEntry);
    } //-- void setEntry(int, com.hp.xmlusage.xml2text.config.Entry) 

    /**
     * Method setEntry
     * 
     * 
     * 
     * @param entryArray
     */
    public void setEntry(Entry[] entryArray)
    {
        //-- copy array
        _entryList.clear();
        for (int i = 0; i < entryArray.length; i++) {
            _entryList.add(entryArray[i]);
        }
    } //-- void setEntry(com.hp.xmlusage.xml2text.config.Entry) 

    /**
     * Method unmarshal
     * 
     * 
     * 
     * @param reader
     * @return Object
     */
    public static Object unmarshal(Reader reader)
        throws MarshalException, ValidationException
    {
        return (Category) Unmarshaller.unmarshal(Category.class, reader);
    } //-- java.lang.Object unmarshal(java.io.Reader) 

    /**
     * Method validate
     * 
     */
    public void validate()
        throws ValidationException
    {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    } //-- void validate() 

}
