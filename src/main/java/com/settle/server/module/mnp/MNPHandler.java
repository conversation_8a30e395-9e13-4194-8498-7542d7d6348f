package com.settle.server.module.mnp;

import cn.hutool.core.io.FileUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.sql.SqlExecutor;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.settle.server.module.esp.CustomerFileFilter;
import com.settle.server.module.mnp.config.MNPConfig;
import com.settle.server.module.mnp.dto.DataFmDTO;
import com.settle.server.module.mnp.dto.FileType;
import com.settle.server.module.mnp.dto.MessageResult;
import com.settle.server.module.mnp.service.StlBaseService;
import com.settle.server.module.mnp.validators.FileContentValidationHandler;
import com.settle.server.utils.DateTimeUtil;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
public class MNPHandler {
    @Autowired
    private MNPConfig mnpConfig;
    @Autowired
    private FileContentValidationHandler fileContentValidationHandler;
    @Autowired
    @Qualifier("stludrDataSource")
    private DataSource stludrDataSource;
    @Autowired
    private Map<String, StlBaseService> stlBaseServiceMap;

    private ThreadPoolExecutor executor;

    @PostConstruct
    public void init() {
        ThreadFactoryBuilder threadFactoryBuilder = new ThreadFactoryBuilder();
        threadFactoryBuilder.setNameFormat("mnp-handler-%d");
        executor = new ThreadPoolExecutor(9, 9, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), threadFactoryBuilder.build());
    }

    public ReturnT<String> prepare(String acctMonth) {
        File dir = new File(mnpConfig.getDataPath());
        if (!FileUtil.exist(dir)) {
            FileUtil.mkdir(dir);
        }
        List<File> listFiles = FileUtil.loopFiles(dir, 1, null);
        if (listFiles.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "当前账期:[" + acctMonth + "],文件目录:[" + dir.getPath() + "],没数据文件.");
        }
        return ReturnT.SUCCESS;
    }

    public ReturnT<String> start(String acctMonth)  {
        List<DataFmDTO> tables = mnpConfig.getTables();
        if (CollectionUtils.isEmpty(tables)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "mnpserver.tables 配置信息不能为空");
        }
        List<CompletableFuture<Void>> futures = tables.stream()
                .map(dataFmDTO -> CompletableFuture.runAsync(() -> this.process(dataFmDTO, acctMonth), executor)).collect(Collectors.toList());
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return ReturnT.SUCCESS;
    }

    private void process(DataFmDTO dataFmDTO, String acctMonth) {
        log.info("Beginning to load files into table {}.", dataFmDTO.getTableName());
        // 1  读取文件
        FileUtil.mkdir(mnpConfig.getDataPath());
        List<File> files = FileUtil.loopFiles(new File(mnpConfig.getDataPath()), 1, new CustomerFileFilter(dataFmDTO.getFileRegex()));
        if (CollectionUtils.isEmpty(files)) {
            log.warn("No files found for table " + dataFmDTO.getTableName() + ".");
            return;
        }
        // 2  删除历史表信息
        this.clearData(dataFmDTO,acctMonth,true);
        // 3  备份表数据到历史表
        boolean result = this.backupDataToHis(dataFmDTO.getTableName());
        if (!result) {
            return;
        }
        // 4  删除表数据
        this.clearData(dataFmDTO,acctMonth,false);
        // 5 删除上个月数据
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            Calendar calendar = Calendar.getInstance();
            Date date = sdf.parse(acctMonth);
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, -1);
            String previousMonth = new SimpleDateFormat("yyyyMM").format(calendar.getTime());
            this.clearData(dataFmDTO,previousMonth,false);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        for (File f : files) {
            boolean insertResult = false;
            if (f.isFile() && f.exists()) {
                log.info("process file: " + f.getName());
                // 5  插入数据到表
                insertResult = this.procOnceFile(f, dataFmDTO, acctMonth);
            }
            // 6 备份文件
            this.backup( f, insertResult);
        }
    }


    private void clearData(String tableName, String acctMonth) {
        Connection connection = null;
        String delSQL = "DELETE FROM " + tableName + " WHERE SETTLEMONTH = ?";
        try {
            log.info("delete " + acctMonth + " data from table " + tableName + ".");
            connection = stludrDataSource.getConnection();

            int execute = SqlExecutor.execute(connection, delSQL, acctMonth);
            log.info("delete {} done. " + execute+ " lines of data were deleted.",tableName);
        } catch (SQLException sqlException) {
            log.error("SQL Exception occurred! Table cannot be deleted.", sqlException);
            this.rollBack(connection);
        } finally {
            DbUtil.close(connection);
        }

    }

    private void clearData(DataFmDTO dataFmDTO,String acctMonth,boolean isHis) {
        StlBaseService stlBaseService = getStlBaseService(dataFmDTO);
        if (isHis)
            stlBaseService.deleteHis(acctMonth);
        else
            stlBaseService.delete(acctMonth);
    }

    private StlBaseService getStlBaseService(DataFmDTO dataFmDTO) {
        FileType type = FileType.getByType(dataFmDTO.getFileType());
        if (type==null) {
            return stlBaseServiceMap.get("stlBaseDefaultService");
        }
        String serviceName = type.getServiceName();
        return stlBaseServiceMap.get(serviceName);
    }
    private boolean procOnceFile(File file, DataFmDTO dataFmDTO, String acctMonth) {
        String fileType = dataFmDTO.getFileType();
        List<String> errLines = Lists.newArrayList();
        List<String[]> sucLines = Lists.newArrayList();
        List<String> fieldList = Stream.of(dataFmDTO.getColumnName().split(",")).map(String::trim).collect(Collectors.toList());
        try {
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                String[] fields = line.split("#\\|#", -1);
                MessageResult messageResult = fileContentValidationHandler
                        .handleFileContentValidation(fileType, fieldList, fields);
                if (!messageResult.isSuccess()) {
                    errLines.add(line);
                    continue;
                }
                sucLines.add(fields);
            }
            processContent(sucLines, dataFmDTO, acctMonth);
            log.info("total num:{}, {} lines of data were inserted.",lines.size(),sucLines.size());
            return true;
        } catch (FileNotFoundException var19) {
            log.error("The file " + file.getName() + " is missing!", var19);
        } catch (IOException var21) {
            log.error("I/O Exception occurred while closing the reader!", var21);
        } catch (Exception var25) {
            log.error("未知异常", var25);
        }finally {
            if (!errLines.isEmpty()) {
                String filePath = mnpConfig.getErrorPath() + File.separator + DateTimeUtil.today() + File.separator;
                FileUtil.mkdir(filePath);
                filePath = filePath + File.separator + "ERR-" + file.getName();
                FileUtil.writeLines(errLines, filePath, StandardCharsets.UTF_8);
            }
        }
        return false;
    }

    private void processContent(List<String[]> sucLines, DataFmDTO dataFmDTO, String acctMonth) {
        if (CollectionUtils.isEmpty(sucLines)) {
            return;
        }
        StlBaseService stlBaseService = getStlBaseService(dataFmDTO);
        stlBaseService.insertBatch(sucLines, acctMonth);
    }


    private void backup( File file, boolean result) {
        String backDir = mnpConfig.getBackupPath() + File.separator + DateTimeUtil.today() ;
        if (!result) {
            backDir = mnpConfig.getErrorPath() + File.separator + DateTimeUtil.today() ;
        }
        log.info("移动文件：" + file.getName() + " into [" + backDir + "] folder.");
        FileUtil.mkdir(backDir);
        FileUtil.move(file, new File(backDir), true);
    }


    private boolean backupDataToHis(String tableName) {
        Connection conn = null;
        String backupSQL = "INSERT INTO " + tableName + "_his" + " SELECT * FROM " + tableName;
        try {
            log.info("Backing up data from table " + tableName + " to table " + tableName + "_his.");
            conn = stludrDataSource.getConnection();

            int execute = SqlExecutor.execute(conn, backupSQL);
            log.info("Backing up done. " + execute+ " lines of data were inserted.");
            return true;
        } catch (SQLException var6) {
            log.error("SQL Exception occurred! Table cannot be inserted.", var6);
        } finally {
            DbUtil.close(conn);
        }
        return false;
    }


    /*回滚*/
    public void rollBack(Connection connection) {
        if (connection != null) {
            try {
                connection.rollback();
            } catch (SQLException e) {
                log.error("Rollback failed! There may be problem with the network connection. The program will exit. Please try later!", e);
            }
        }
    }
}

