package com.settle.server.module.mnp.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FileType {
    LINE("lineFileValidator","LINE","stlBaseinfoLineService"),
    VPN("vpnFileValidator","VPN","stlBaseinfoVpnService"),
    INTERNET("internetFileValidator","INTERNET","stlBaseinfoInternetService"),
    SRV6("srv6FileValidator","SRV6","stlBaseinfoSrv6Service"),
    ;

    private String beanName;
    private String type;
    private String serviceName;


    public static FileType getByType(String type) {
        FileType[] values = FileType.values();
        for (FileType fileType : values) {
            if (fileType.getType().equals(type)) {
                return fileType;
            }
        }
        return null;
    }
}

