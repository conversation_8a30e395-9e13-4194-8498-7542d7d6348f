<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.pvbill.SyncInterfacePvbMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.pvbill.SyncInterfacePvb">
        <result column="BILL_MONTH" property="billMonth" />
        <result column="ACCUMULATED_FEE_VAL" property="accumulatedFeeVal" />
        <result column="RESERVED1" property="reserved1" />
        <result column="RESERVED2" property="reserved2" />
        <result column="STATUS" property="status" />
        <result column="FILE_NAME" property="fileName" />
        <result column="PROV_CODE" property="provCode" />
        <result column="DIRECT_REC_AMOUNT" property="directRecAmount" />
        <result column="JOINT_REC_AMOUNT" property="jointRecAmount" />
    </resultMap>
    <insert id="insertBatch">
        insert into SYNC_INTERFACE_PVB
        (BILL_MONTH, ACCUMULATED_FEE_VAL, RESERVED1, RESERVED2, STATUS, FILE_NAME, PROV_CODE, DIRECT_REC_AMOUNT, JOINT_REC_AMOUNT)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.billMonth,jdbcType=VARCHAR},
            #{item.accumulatedFeeVal,jdbcType=BIGINT},
            #{item.reserved1,jdbcType=VARCHAR},
            #{item.reserved2,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.fileName,jdbcType=VARCHAR},
            #{item.provCode,jdbcType=VARCHAR},
            #{item.directRecAmount,jdbcType=BIGINT},
            #{item.jointRecAmount,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

</mapper>
