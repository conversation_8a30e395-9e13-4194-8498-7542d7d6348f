package com.settle.server.module.cdn.writer;


import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 将记录写入 {@link BlockingQueue}
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class BlockingQueueRecordWriter<P> implements RecordWriter<P> {

    public static final long DEFAULT_TIMEOUT = 60000;

    private BlockingQueue<Record<P>> blockingQueue;
    private long timeout;

    /**
     * 创建一个新的 {@link BlockingQueueRecordWriter}
     *
     * @param blockingQueue 将记录写入
     */
    public BlockingQueueRecordWriter(final BlockingQueue<Record<P>> blockingQueue) {
        this(blockingQueue, DEFAULT_TIMEOUT);
    }

    /**
     * 创建一个新的 {@link BlockingQueueRecordWriter}
     *
     * @param blockingQueue 将记录写入
     * @param timeout       以毫秒为单位，超时将抛出异常
     */
    public BlockingQueueRecordWriter(final BlockingQueue<Record<P>> blockingQueue, final long timeout) {
        this.blockingQueue = blockingQueue;
        this.timeout = timeout;
    }

    @Override
    public void writeRecords(Batch<P> batch) throws Exception {
        for (Record<P> record : batch) {
            blockingQueue.offer(record, timeout, TimeUnit.MILLISECONDS);
        }
    }

}
