package com.settle.server.module.bigdata.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.settle.server.dao.stludr.bigdata.BigdataBroadbandPriceCfgMapper;
import com.settle.server.entity.bigdata.BigdataBroadbandPriceCfg;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Service
public class BigdataBroadbandPriceCfgService extends ServiceImpl<BigdataBroadbandPriceCfgMapper, BigdataBroadbandPriceCfg>  {

    public List<BigdataBroadbandPriceCfg> selectAll() {
        return getBaseMapper().selectList(new LambdaQueryWrapper<>(BigdataBroadbandPriceCfg.class)
                .eq(BigdataBroadbandPriceCfg::getEnable, "1"));
    }
}
