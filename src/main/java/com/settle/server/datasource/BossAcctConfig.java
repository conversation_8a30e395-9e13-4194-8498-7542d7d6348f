package com.settle.server.datasource;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.io.IOException;

/**
 * @Classname StludrConfig
 * @Description TODO
 * @Date 2023/4/13 20:19
 * <AUTHOR>
 * @Version 1.0.0
 */
@Configuration
@MapperScan(basePackages = "com.settle.server.dao.bossAcct", sqlSessionTemplateRef = "bossAcctSqlSessionTemplate")
public class BossAcctConfig {

    private static String MYBATIS_CONFIG = "mybatis-config.xml";

    private static final String MAPPER_LOCATION = "classpath:/mapper/**/*.xml";
    @Value("${datasource.connection-timeout}")
    private int connectionTimeout;
    @Value("${datasource.max-pool-size}")
    private int maxPoolSize;
    @Value("${datasource.idle-timeout}")
    private int idleTimeout;
    @Value("${datasource.max-life-time}")
    private int maxLifeTime;

    /**
     * 主数据源配置stludr数据源
     *
     * @return
     */
    @Bean(name = "bossAcctDataSourceProperties")
    @ConfigurationProperties(prefix = "jdbc.bossacctserver")
    public DataSourceProperties bossAcctDataSourceProperties() {
        return new DataSourceProperties();
    }
    /**
     * 主数据源 stludr数据源
     * @param dataSourceProperties
     * @return
     */
    @Bean(name = "bossAcctDataSource")
    public DataSource bossAcctDataSource(@Qualifier("bossAcctDataSourceProperties") DataSourceProperties dataSourceProperties) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setPoolName("bossAcctPool");
        hikariConfig.setJdbcUrl(dataSourceProperties.getUrl());
        hikariConfig.setDriverClassName(dataSourceProperties.getDriverClassName());
        hikariConfig.setUsername(dataSourceProperties.getUsername());
        hikariConfig.setPassword(dataSourceProperties.getPassword());
        // 设置连接池参数
        hikariConfig.setMaximumPoolSize(maxPoolSize);
        hikariConfig.setMinimumIdle(maxPoolSize);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifeTime);
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setConnectionInitSql("select 1");
        hikariConfig.setConnectionTestQuery("select 'x'");

        return new HikariDataSource(hikariConfig);
    }
    @Bean("bossAcctSqlSessionFactory")
    public MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean(@Qualifier("bossAcctDataSource") DataSource dataSource) throws IOException {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setConfigLocation(new ClassPathResource(MYBATIS_CONFIG));
        mybatisPlus.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(BossAcctConfig.MAPPER_LOCATION));
        return mybatisPlus;
    }

    @Bean(name = "bossAcctTransactionManager")
    public DataSourceTransactionManager bossAcctTransactionManager(@Qualifier("bossAcctDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "bossAcctSqlSessionTemplate")
    public SqlSessionTemplate bossAcctSqlSessionTemplate(@Qualifier("bossAcctSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
