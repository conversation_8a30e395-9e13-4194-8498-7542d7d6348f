<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.SyncInterfaceBc2cPARTDao">


  <insert id="insertBatch" useGeneratedKeys="false"
          parameterType="java.util.List">
      INSERT INTO SYNC_INTERFACE_BC2C_PART (ID, ACCT_MONTH, PROV_CODE, PRODUCT_SUBS_ID, CLOUD_INSTANCE_ID,
      PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID, DB_PRODUCT_NAME, GH_CODE,
      PV_PRODUCT_CLASS, ISSUE_TIME, EXPIRE_TIME, BILLING_TERM, FEE_VAL,
      TAX_RATE, TAX, FEE_NO_TAX, PARTNE<PERSON>_CODE, PARTNER_NAME,
      PAR_SETTLE_RATE, SETTLEMENT_TYPE, PAR_SETTL_AMOUNT, PAR_RESSETTL_RATE,STATUS,FILE_NAME)
      values
      <foreach collection="list" item="item" index="index" separator="," >
           (#{item.id}, #{item.acctMonth}, #{item.provCode}, #{item.productSubsId}, #{item.cloudInstanceId},
          #{item.productId}, #{item.productName}, #{item.dbProductId}, #{item.dbProductName}, #{item.ghCode},
          #{item.pvProductClass}, #{item.issueTime}, #{item.expireTime}, #{item.billingTerm}, #{item.feeVal},
          #{item.taxRate}, #{item.tax}, #{item.feeNoTax}, #{item.partnerCode}, #{item.partnerName},
          #{item.parSettleRate}, #{item.settlementType}, #{item.parSettlAmount}, #{item.parResSettlRate},
          #{item.status}, #{item.fileName})
      </foreach>
  </insert>

  <update id="updateStausByAcctMonth">
      update stludr.SYNC_INTERFACE_BC2C_PART
      set status='23'
      where ACCT_MONTH = #{acctMonth}
        and prov_code not in (select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1'))
        and (status ='' or status is null)
  </update>

  <update id="updateStatusByMcStatus">
    update stludr.SYNC_INTERFACE_BC2C_PART
    set status='99'
    where (file_name, id) in (select file_name, id
                              from stludr.SYNC_INTERFACE_BC2C_PS
                              where status = '99'
                                AND ACCT_MONTH = #{acctMonth, jdbcType = VARCHAR})
      and status is null
  </update>

  <update id="updateStausByStatus">
      update stludr.SYNC_INTERFACE_BC2C_PART
      set status='0'
      where ACCT_MONTH = #{acctMonth, jdbcType = VARCHAR}
        and (status = '' or status is null)
  </update>

  <delete id="deleteByMonth" parameterType="java.lang.String">
    delete from stludr.SYNC_INTERFACE_BC2C_PART
    <where>
      <if test="acctMonth != null and acctMonth != ''">
        AND ACCT_MONTH = #{acctMonth, jdbcType = VARCHAR}
      </if>
    </where>
  </delete>
  <select id="queryErrorByFileName" resultType="com.settle.server.entity.SyncInterfaceBc2cPart">
      select * from stludr.SYNC_INTERFACE_BC2C_PART
      <where>
          <if test="acctMonth != null and acctMonth != ''">
              and ACCT_MONTH = #{acctMonth}
          </if>
          <if test="fileName != null and fileName != ''">
              and FILE_NAME = #{fileName}
          </if>
      </where>
      and status!='0'
  </select>
</mapper>