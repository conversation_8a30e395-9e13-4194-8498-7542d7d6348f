package com.settle.server.controller;


import com.settle.server.service.BigDataSettService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@Api(tags = "大数据平台文件接口")
public class BigDataController {
    @Autowired
    private BigDataSettService bigDataSettService;

    @ApiOperation(value = "宽带业务省间结算S201", notes = "宽带业务省间结算S201")
    @GetMapping(value = "/broadband")
    public String broadband(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            bigDataSettService.loadUpload(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("宽带业务错误失败。 error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }

    @ApiOperation(value = "宽带业务省间结算数据预出账", notes = "宽带业务省间结算数据预出账")
    @GetMapping("/broadbandPreHandler")
    public String broadbandPreHandler(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            bigDataSettService.broadbandPreProcess(acctMonth);
        } catch (Exception e) {
            log.error("带宽型业务省间结算数据预出账。 error", e);
            return e.getMessage();
        }
        return "success";
    }

}
