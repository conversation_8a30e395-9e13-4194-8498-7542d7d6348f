package com.settle.server.module.cdn.constant;

/**
 * 规则常量
 *
 * <AUTHOR>
 */
public class RuleConst {

    /**
     * 规则配置文件路径
     */
    public static final String RULE_CONF_FILE_PATH = "../conf/rules/";

    /**
     * 规则配置文件名后缀
     */
    public static final String RULE_CONF_FILE_NAME_SUFFIX = ".rule";

    /**
     * 表名
     */
    public static final String G_K_TABLE_NAME = "tableName";

    /**
     * 表字段
     */
    public static final String G_K_TABLE_FIELDS = "tableFields";

    /**
     * 文件备份路径
     */
    public static final String G_K_FILE_BAK_PATH = "fileBakPath";

    /**
     * 工作目录
     */
    public static final String G_K_WORK_PATH = "workPath";

    /**
     * 错误目录
     */
    public static final String G_K_ERROR_PATH = "errorPath";

    /**
     * 记录分隔符(如未指定则为英文逗号)
     */
    public static final String G_K_DELIMITER = "delimiter";

    /**
     * 换行符（如未指定则使用系统默认换行符）
     */
    public static final String G_K_LINE_SEPARATOR = "lineSeparator";

    /**
     * 数据库源
     */
    public static final String G_K_DB_GROUP = "dbGroup";

    /**
     * 文件入口目录
     */
    public static final String G_K_FILE_IN_PATH = "fileInPath";

    /**
     * 文件出口目录
     */
    public static final String G_K_FILE_OUT_PATH = "fileOutPath";

    /**
     * 文件名正则(如未指定则匹配所有)
     */
    public static final String G_K_FILE_NAME_REG = "fileNameReg";

    /**
     * 空文件名正则(如未指定则匹配所有)
     */
    public static final String G_K_EMPTY_FILE_NAME_REG = "emptyFileNameReg";

    /**
     * 批量大小
     */
    public static final String G_K_BATCH_SIZE = "batchSize";

    /**
     * 导出SQL
     */
    public static final String G_K_EXPORT_SQL = "exportSql";

    /**
     * 操作类型
     */
    public static final String G_K_ACTION_TYPE = "action";

    /**
     * 分隔文件行数
     */
    public static final String G_K_SPLIT_LINES = "splitFileLines";

}
