<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.StlCmiotSettDao">

  <resultMap id="BaseResultMap" type="com.settle.server.entity.StlCmiotSett">
    <result column="SETTLE_MONTH" jdbcType="VARCHAR" property="settleMonth" />
    <result column="PRODUCT_SPEC_NUM" jdbcType="VARCHAR" property="productSpecNum" />
    <result column="SETTLE_TYPE" jdbcType="VARCHAR" property="settleType" />
    <result column="SETTLE_OUT" jdbcType="VARCHAR" property="settleOut" />
    <result column="SETTLE_IN" jdbcType="VARCHAR" property="settleIn" />
    <result column="SETTLE_AMOUNT" jdbcType="VARCHAR" property="settleAmount" />
    <result column="TAX_RATE" jdbcType="VARCHAR" property="taxRate" />
  </resultMap>

  <sql id="Base_Column_List">
    SETTLE_MONTH, PRODUCT_SPEC_NUM, SETTLE_TYPE, SETTLE_OUT, SETTLE_IN, SETTLE_AMOUNT, TAX_RATE
  </sql>

  <insert id="batchInsert" useGeneratedKeys="false" parameterType="java.util.List">
    insert into STLUDR.STL_CMIOT_SETT (SETTLE_MONTH, PRODUCT_SPEC_NUM, SETTLE_TYPE, SETTLE_OUT, SETTLE_IN, SETTLE_AMOUNT, TAX_RATE)
    <foreach collection="list" item="stlCmiotSett" index="index" separator="UNION ALL">
      ( select
      #{stlCmiotSett.settleMonth,jdbcType=VARCHAR},
      #{stlCmiotSett.productSpecNum,jdbcType=VARCHAR},
      #{stlCmiotSett.settleType,jdbcType=VARCHAR},
      #{stlCmiotSett.settleOut,jdbcType=VARCHAR},
      #{stlCmiotSett.settleIn,jdbcType=VARCHAR},
      #{stlCmiotSett.settleAmount,jdbcType=VARCHAR},
      #{stlCmiotSett.taxRate,jdbcType=VARCHAR} from dual)
    </foreach>
  </insert>

  <delete id="deleteBySettleMonth" parameterType="java.lang.String">
    delete from STLUDR.STL_CMIOT_SETT where SETTLE_MONTH = #{acctMonth, jdbcType = VARCHAR}
  </delete>

</mapper>