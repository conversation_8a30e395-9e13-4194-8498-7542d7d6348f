package com.settle.server.module.cdn.converter;

import cn.hutool.core.convert.Convert;
import cn.hutool.db.DbUtil;
import cn.hutool.log.level.Level;
import cn.hutool.setting.Setting;
import com.settle.server.module.cdn.config.CDNToEBossConfig;
import com.settle.server.module.cdn.config.EBossToSettleConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;

@Configuration
@Slf4j
public class CdnConfigToSettingConverter {

    @Autowired
    private CDNToEBossConfig cdnToEBossConfig;

    @Autowired
    private EBossToSettleConfig eBossToSettleConfig;

    @PostConstruct
    public void initShowSqlSetting(){
        String sqlLevel = cdnToEBossConfig.getSqlLevel();
        if (null != sqlLevel) {
            sqlLevel = sqlLevel.toUpperCase();
        }
        Level level = Convert.toEnum(Level.class, sqlLevel, Level.DEBUG);
        DbUtil.setShowSqlGlobal(cdnToEBossConfig.getShowSql(),cdnToEBossConfig.getFormatSql(),cdnToEBossConfig.getShowParams(), level);
    }

    /**
     * CDN收入结算同步EBOSS
     * @return
     */
    @Bean(name = "CDNToEBossRule")
    public Setting CDNToEBossRule() {
        Setting setting = Setting.create();
        HashMap<String, String> settingMap = new HashMap<>();
        settingMap.put("action", cdnToEBossConfig.getAction());
        settingMap.put("delimiter", cdnToEBossConfig.getDelimiter());
        settingMap.put("batchSize", cdnToEBossConfig.getBatchSize());
        settingMap.put("fileOutPath", cdnToEBossConfig.getFileOutPath());
        settingMap.put("fileNameReg", cdnToEBossConfig.getFileNameReg());
        settingMap.put("exportSql", cdnToEBossConfig.getExportSql());
        setting.putAll(cdnToEBossConfig.getGroup(),settingMap);
        return setting;
    }

    /**
     * EBOSS客户产品应收信息同步结算
     * @return
     */
    @Bean(name = "EBossToSettleRule")
    public Setting EBossToSettleRule() {
        Setting setting = Setting.create();
        HashMap<String, String> settingMap = new HashMap<>();
        settingMap.put("action", eBossToSettleConfig.getAction());
        settingMap.put("delimiter", eBossToSettleConfig.getDelimiter());
        settingMap.put("batchSize", eBossToSettleConfig.getBatchSize());
        settingMap.put("fileInPath", eBossToSettleConfig.getFileInPath());
        settingMap.put("workPath", eBossToSettleConfig.getWorkPath());
        settingMap.put("errorPath", eBossToSettleConfig.getErrorPath());
        settingMap.put("fileBakPath", eBossToSettleConfig.getFileBakPath());
        settingMap.put("fileNameReg", eBossToSettleConfig.getFileNameReg());
        settingMap.put("tableName", eBossToSettleConfig.getTableName());
        settingMap.put("tableFields", eBossToSettleConfig.getTableFields());
        setting.putAll(eBossToSettleConfig.getGroup(),settingMap);
        return setting;
    }
}
