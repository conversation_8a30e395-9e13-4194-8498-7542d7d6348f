package com.settle.server.xmlTransform.xml2text;

import com.settle.server.utils.CommandUtil;
import com.settle.server.utils.FileUtil;
import com.settle.server.xmlTransform.XMLTransformDBUtil;
import com.settle.server.xmlTransform.resp.BillList;
import com.settle.server.xmlTransform.resp.SettleRuleList;
import com.settle.server.xmlTransform.xml2text.config.Entry;
import lombok.extern.slf4j.Slf4j;
import org.exolab.castor.xml.MarshalException;
import org.exolab.castor.xml.ValidationException;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.*;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.Calendar;

@Slf4j
public class X2TEngine implements Runnable {
	
	final String REGEXP_MC = "EBOSS_BILL_BBOSS_M_DETAIL_[0-9]{6}\\.[0-9]{4}";
	final String REGEXP_DICT = "ESP_PROD_SETTLE_RULE_[0-9]{6}\\.[0-9]{4}";
	final String REGEXP_ESP = "ESP_ACC_E_BILL_[2-3]_[0-9]{6}_.{3,4}\\.[0-9]{4}";

	private Transformer _transformer;

	private Entry _entry;

	DocumentBuilder _parser;

	Validator _validator;

	public X2TEngine(Entry entry) throws TransformerConfigurationException,
			TransformerFactoryConfigurationError, FactoryConfigurationError,
			ParserConfigurationException, IllegalArgumentException,
			SAXException {
		_entry = entry;
		
		// create a transformer
		TransformerFactory tFactory = TransformerFactory.newInstance();
		_transformer = tFactory.newTransformer(new StreamSource(_entry
				.getXslt()));

		// create an XML document parser
		_parser = DocumentBuilderFactory.newInstance().newDocumentBuilder();

		// create a SchemaFactory capable of understanding WXS schemas
		SchemaFactory factory = SchemaFactory
				.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

		// load a WXS schema, represented by a Schema instance
		Source schemaFile = new StreamSource(new File(_entry.getXsd()));
		Schema schema = factory.newSchema(schemaFile);

		// create a Validator instance, which can be used to validate an
		// instance document
		_validator = schema.newValidator();
	}

	public void run() {

		while (true) {
			try {
				File[] files = FileUtil.getFilesInFolder(_entry.getIn(), _entry
						.getRegexp());

				if (files != null && files.length > 0) {
					for (int i = 0; i < files.length; i++) {
						File srcFile = files[i];
						String errorCode = FileUtil.checkBillListFileName(
								srcFile.getName(), _entry.getName());
						if (!errorCode.equals("")) {
							XMLTransformDBUtil jdbcUtil = XMLTransformDBUtil.getInstance();
							jdbcUtil.saveX2TResult(_entry.getName()
									.toUpperCase(), srcFile.getName(),
									Long.valueOf(-1), errorCode, null, 1, srcFile.length());
							genRespFile(srcFile, 0, "");
						} else {
							if (srcFile.length() == 0) {
								XMLTransformDBUtil jdbcUtil = XMLTransformDBUtil.getInstance();
								jdbcUtil.saveX2TResult(_entry.getName()
										.toUpperCase(), srcFile.getName(),
										Long.valueOf(-1), null, null, 1, srcFile.length());
								File des = new File(FileUtil.addEndSep(_entry
										.getOut())
										+ srcFile.getName());
								if (!des.createNewFile()) {
									log.error(
											"Empty File copy to OUT folder Error: "
													+ _entry.getOut()
													+ srcFile.getName());
								}
							} else {

								File destFile = new File(_entry.getOut()
										+ srcFile.getName());
								File tmpFile = FileUtil
										.createTempFile(destFile);
								if (tmpFile == null)
									continue;

								BufferedWriter bufferedWriter = null;
//								FileOutputStream outfile = null;
								try {
									// validate source xml
									//try {
										Document document = _parser
												.parse(srcFile);
										_validator.validate(new DOMSource(
												document));

										// transform xml to csv
										Source xmlSource = new StreamSource(
												srcFile);
										try {
											FileOutputStream fos = new FileOutputStream(tmpFile);
											OutputStreamWriter out = new OutputStreamWriter(fos, "UTF-8");
											bufferedWriter = new BufferedWriter(out);
											
//											outfile = new FileOutputStream(
//													tmpFile);
											Result outputTarget = new StreamResult(
													bufferedWriter);
											_transformer.transform(xmlSource,
													outputTarget);
											
										} finally {
											if(bufferedWriter != null)
												bufferedWriter.close();
										}
										Long rawCount = CommandUtil.runCommand(tmpFile.getPath());
										Long minus = new Long((long) -1);
										if (rawCount == minus) {
											log
													.error(
															_entry.getName()
																	+ " : Raw Counting Error occured, "
																	+ srcFile
																			.getName());
											continue;
										}
									XMLTransformDBUtil jdbcUtil = XMLTransformDBUtil
												.getInstance();
										jdbcUtil.saveX2TResult(_entry.getName()
												.toUpperCase(), srcFile
												.getName(), rawCount, null,
												null, 1, srcFile.length());

									    
									    FileUtil.moveFile(tmpFile, destFile);
//									    tmpFile.delete();
										
								} catch (SAXException ex) {
									// while parsing and validating
									log.error(
											_entry.getName() + " : "
													+ srcFile.getName() + " "
													+ ex.toString());
									XMLTransformDBUtil jdbcUtil = XMLTransformDBUtil.getInstance();
									jdbcUtil.saveX2TResult(_entry.getName()
											.toUpperCase(), srcFile.getName(),
											new Long(-1), "1", ex
													.getMessage(), 1, srcFile.length());
									genRespFile(srcFile, 1, ex
											.getMessage());
									// ex.printStackTrace();
								} catch (TransformerException ex) {
									// while transforming
									/*
									 * try { outfile.close(); tmpFile.delete(); }
									 * catch (Exception ex1) {
									 * ex1.printStackTrace(); }
									 */
									log.error(
											_entry.getName() + " : "
													+ srcFile.getName() + " "
													+ ex.toString());
									XMLTransformDBUtil jdbcUtil = XMLTransformDBUtil.getInstance();
									jdbcUtil.saveX2TResult(_entry.getName()
											.toUpperCase(), srcFile.getName(),
											new Long(-1), "2", ex
													.getMessage(), 1, srcFile.length());
									genRespFile(srcFile, 2, ex
											.getMessage());
								} catch (FileNotFoundException ex) {
									// while new FileOutputStream
									log.error(_entry.getName()
															+ " : FileNotFoundException occured, "
															+ srcFile.getName());
									continue;
								}catch (IOException ex) {
									// while parsing and validating
									
									String ERRORMESSAGE = "Invalid byte 1 of 1-byte UTF-8 sequence";
									
									log.error(_entry.getName()+ " : IOException occured, "+ srcFile.getName()+"["+ex.getMessage()+"]");
									
									
									if (ex.getMessage().indexOf(ERRORMESSAGE) <= 0){
										XMLTransformDBUtil jdbcUtil = XMLTransformDBUtil.getInstance();
										jdbcUtil.saveX2TResult(_entry.getName().toUpperCase(), srcFile.getName(),new Long(-1), "99", ex.getMessage(), 1, srcFile.length());
										genRespFile(srcFile, 99, ex.getMessage());
									}
								} 
							}
						}

						FileUtil.moveFile(srcFile, _entry.getBack());
						log.info(
								_entry.getName() + " : " + srcFile.getName()
										+ " is processed.");
					}
				}

				Thread.sleep(10 * 1000);
			} catch (Exception ex) {
				log.error("Sleep Error: " + ex.getMessage());
				try {
					Thread.sleep(10 * 1000); 
				} catch (InterruptedException ex1) {
					log.error("Sleep Error again: " + ex1.getMessage());
				}
			}
		}
	}

	private void genRespFile(File srcFile, int errorCode, String errorNode) {
		if (srcFile.getName().matches(REGEXP_DICT)) {
			respDict(srcFile, errorCode, errorNode);
		} else if (srcFile.getName().matches(REGEXP_MC)){
			respMC(srcFile, errorCode, errorNode);
		} else if (srcFile.getName().matches(REGEXP_ESP)){
			respESP(srcFile, errorCode, errorNode);
		}
	}
	
	private void respDict(File srcFile, int errorCode, String errorNode) {
		try {
			File destFile = new File(_entry.getResp() + "RESP_" + srcFile.getName());
			File tmpFile = FileUtil.createTempFile(destFile);
			if (tmpFile == null)
				return;
			
			SettleRuleList rspXml = new SettleRuleList();
			rspXml.setOrg_FileName(srcFile.getName());
			rspXml.setFileStatus("1");
			
			switch(errorCode) {
				case 1: case 2: rspXml.setErrorCode("F002");
				break;
				case 99: rspXml.setErrorCode("F999");
				break;
				default: rspXml.setErrorCode("");
			}

			SimpleDateFormat myFmt = new SimpleDateFormat("yyyyMMddhhmmss");
			Date date = new Date(Calendar.getInstance().getTimeInMillis());
			rspXml.setResp_Date(myFmt.format(date));

			if (errorNode.length() > 256) {
				rspXml.setErrorNode(errorNode.substring(0, 256));
			} else {
				rspXml.setErrorNode(errorNode);
			}

			FileWriter xmlfile = new FileWriter(tmpFile);
			rspXml.marshal(xmlfile);
			xmlfile.close();

			FileUtil.moveFile(tmpFile, destFile);

			XMLTransformDBUtil.getInstance().saveX2TResult(
					_entry.getName().toUpperCase(), destFile.getName(),
					new Long(-1), Integer.toString(errorCode), errorNode, 0, destFile.length());

			log.error(
					_entry.getName() + " : " + destFile + " is generated.");

		} catch (ValidationException ex) {
			log.error(ex.getMessage());
		} catch (MarshalException ex) {
			log.error(ex.getMessage());//info()
		} catch (FileNotFoundException ex) {
			log.error(ex.getMessage());
		} catch (IOException ex) {
			log.error(ex.getMessage());
		}
	}
	
	private void respMC(File srcFile, int errorCode, String errorNode) {
		try {
			File destFile = new File(_entry.getResp() + "Resp_" + srcFile.getName());
			File tmpFile = FileUtil.createTempFile(destFile);
			if (tmpFile == null)
				return;
			
			BillList rspXml = new BillList();
			rspXml.setOrg_FileName(srcFile.getName());
			rspXml.setFileStatus("1");
			
			switch(errorCode) {
				case 1: rspXml.setErrorCode("22");
				break;
				case 2: rspXml.setErrorCode("21");
				break;
				case 99: rspXml.setErrorCode("99");
				break;
				default: rspXml.setErrorCode("");
			}

			SimpleDateFormat myFmt = new SimpleDateFormat("yyyyMMddhhmmss");
			Date date = new Date(Calendar.getInstance().getTimeInMillis());
			rspXml.setResp_Date(myFmt.format(date));

			if (errorNode.length() > 256) {
				rspXml.setErrorNode(errorNode.substring(0, 256));
			} else {
				rspXml.setErrorNode(errorNode);
			}

			FileWriter xmlfile = new FileWriter(tmpFile);
			rspXml.marshal(xmlfile);
			xmlfile.close();

			FileUtil.moveFile(tmpFile, destFile);

			XMLTransformDBUtil.getInstance().saveX2TResult(
					_entry.getName().toUpperCase(), destFile.getName(),
					new Long(-1), Integer.toString(errorCode), errorNode, 0, destFile.length());

			log.error(
					_entry.getName() + " : " + destFile + " is generated.");

		} catch (ValidationException ex) {
			log.error(ex.getMessage());
		} catch (MarshalException ex) {
			log.error(ex.getMessage());//info()
		} catch (FileNotFoundException ex) {
			log.error(ex.getMessage());
		} catch (IOException ex) {
			log.error(ex.getMessage());
		}
	}
	
	private void respESP(File srcFile, int errorCode, String errorNode) {
		try {
			File destFile = new File(_entry.getResp() + "Resp_" + srcFile.getName());
			File tmpFile = FileUtil.createTempFile(destFile);
			if (tmpFile == null)
				return;
			
			BillList rspXml = new BillList();
			rspXml.setOrg_FileName(srcFile.getName());
			rspXml.setFileStatus("1");
			
			switch(errorCode) {
				case 1: case 2: rspXml.setErrorCode("F904");
				break;
				case 99: rspXml.setErrorCode("F904");
				break;
				default: rspXml.setErrorCode("");
			}

			SimpleDateFormat myFmt = new SimpleDateFormat("yyyyMMddhhmmss");
			Date date = new Date(Calendar.getInstance().getTimeInMillis());
			rspXml.setResp_Date(myFmt.format(date));

			if (errorNode.length() > 256) {
				rspXml.setErrorNode(errorNode.substring(0, 256));
			} else {
				rspXml.setErrorNode(errorNode);
			}

			FileWriter xmlfile = new FileWriter(tmpFile);
			rspXml.marshal(xmlfile);
			xmlfile.close();

			FileUtil.moveFile(tmpFile, destFile);

			XMLTransformDBUtil.getInstance().saveX2TResult(
					_entry.getName().toUpperCase(), destFile.getName(),
					new Long(-1), Integer.toString(errorCode), errorNode, 0, destFile.length());

			log.error(
					_entry.getName() + " : " + destFile + " is generated.");

		} catch (ValidationException ex) {
			log.error(ex.getMessage());
		} catch (MarshalException ex) {
			log.error(ex.getMessage());//info()
		} catch (FileNotFoundException ex) {
			log.error(ex.getMessage());
		} catch (IOException ex) {
			log.error(ex.getMessage());
		}
	}
}
