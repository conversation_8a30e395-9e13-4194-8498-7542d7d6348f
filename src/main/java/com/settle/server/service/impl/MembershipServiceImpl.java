package com.settle.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.settle.server.config.MembershipConfig;
import com.settle.server.dao.bboss.StlMemInterestsMapper;
import com.settle.server.entity.membership.StlMemInterests;
import com.settle.server.module.esp.CustomerFileFilter;
import com.settle.server.service.MembershipService;
import com.settle.server.utils.ExceptionCast;
import com.settle.server.utils.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Slf4j
@Service
public class MembershipServiceImpl implements MembershipService {

    public static final char VERTICAL_BAR = '|';

    @Autowired
    private MembershipConfig membershipConfig;

    @Autowired
    private StlMemInterestsMapper stlMemInterestsMapper;



    public void save(String acctMonth) {

        List<File> membershipFiles = scanFiles(membershipConfig.getDataPath(), membershipConfig.getValidName(), acctMonth);
        if (CollectionUtils.isEmpty(membershipFiles)) {
            log.warn("Membership No of input file not found under dir path:{}", membershipConfig.getDataPath());
            return;
        }
        for (File memberFile : membershipFiles) {

            this.procOnceFile(acctMonth, memberFile);

        }
    }

    private void procOnceFile(String acctMonth, File memberFile) {
        boolean process = true;
        String fileName = memberFile.getName();
        List<StlMemInterests> stlMemInterests = new ArrayList<>();
        String errMsg = "";
        try {
            //删除当前账期的数据
            stlMemInterestsMapper.deleteBySettleMonth(acctMonth, memberFile.getName(), null);
            //校验文件大小
            this.checkFileSize(memberFile);

            List<String> membershipLines = FileUtil.readUtf8Lines(memberFile);
            membershipLines.removeAll(Arrays.asList("", null));
            if (CollectionUtils.isEmpty(membershipLines)) {
                log.warn("Membership input file is empty dir {}. Exit now.", memberFile.getAbsolutePath());
                ExceptionCast.cast("Membership input file is empty dir");
            }
            log.info("Membership input file details path:{}, row:{}", memberFile.getAbsolutePath(), membershipLines.size());

            for (String memberLines : membershipLines) {
                List<String> result = StrUtil.split(memberLines, VERTICAL_BAR, false, false);
                StlMemInterests stlMemInterest = new StlMemInterests();
                String orderSource = result.get(0);
                String lineNo = result.get(1);
                String prodorderSkuNum = result.get(2);
                String prodistSkuNum = result.get(3);
                String customerNum = result.get(4);
                String memberNum = result.get(5);
                String resourceCode = result.get(6);
                String province = result.get(7);
                String collectTime = result.get(8);
                List<String> resourceCodeLst = Arrays.asList("3145", "3146", "3147", "3148", "3149", "3150", "3199", "3200", "3378", "3379", "3214", "3380", "3215", "3381");
                stlMemInterest.setAcctMonth(acctMonth);
                stlMemInterest.setFileName(fileName);
                stlMemInterest.setRowNo(lineNo);
                stlMemInterest.setOrderSource(orderSource);
                stlMemInterest.setProdordSkuNum(prodorderSkuNum);
                stlMemInterest.setProdistSkuNum(prodistSkuNum);
                stlMemInterest.setCustomerNum(customerNum);
                stlMemInterest.setMemberNum(memberNum);
                stlMemInterest.setResourceCode(resourceCode);
                stlMemInterest.setProvNm(province);
                stlMemInterest.setCollectTime(collectTime);
                stlMemInterest.setStatus("00");
                checkStlMemInterestsBaseInfo(result, stlMemInterest, orderSource, lineNo, prodorderSkuNum, prodistSkuNum, customerNum, memberNum, resourceCode, province, collectTime, resourceCodeLst);

                stlMemInterests.add(stlMemInterest);
            }

            this.saveBatch(stlMemInterests);
            log.info("membership loading_file_done fileName:{} acctMonth:{}", fileName, acctMonth);

            stlMemInterestsMapper.updateProvNumByAcctMonth(acctMonth, fileName);
            stlMemInterestsMapper.updateStatusAndErrMsgByAcctMonth(acctMonth, fileName);
            stlMemInterestsMapper.updateStatusByAcctMonthAndCustNum(acctMonth, fileName);
            stlMemInterestsMapper.updateStatusByAcctMonthAndMemberNum(acctMonth, fileName);
            stlMemInterestsMapper.updateStatusByProdisNum(acctMonth, fileName);
            stlMemInterestsMapper.updateStatusByAcctMonth(acctMonth, fileName);
            log.info("membership update data finish fileName:{} acctMonth:{}", fileName, acctMonth);

        }  catch (Exception e) {
            if (e instanceof ServiceException) {
                errMsg = e.getMessage();
            } else {
                errMsg = "文件处理失败";
            }
            process = false;
            log.error("membership 执行失败 fileName:{} acctMonth:{} error：{}", fileName, acctMonth, e.getMessage(), e);
            stlMemInterestsMapper.deleteBySettleMonth(acctMonth, fileName, null);
        } finally {
            this.genResp(acctMonth, fileName, process, errMsg);
            stlMemInterests.clear();
            moveFile(memberFile, process);
        }
    }

    private void moveFile(File memberFile, boolean process) {
        Path donePath = Paths.get(membershipConfig.getBackupPath());
        if (!process) {
            donePath = Paths.get(membershipConfig.getErrorPath());
        }
        FileUtil.mkdir(donePath);
        FileUtil.move(memberFile, donePath.resolve(memberFile.getName()).toFile(), true);
    }

    private void genResp(String acctMonth, String fileName,boolean process,String errDesc) {
        List<String> fileContents = new ArrayList<>();
        if (process) {
            int totalCount = stlMemInterestsMapper.selectCountByCondition(acctMonth, fileName);
            // 定义每页数据量
            int limit = 500;
            // 计算页数
            int pageCount = (int) Math.ceil((double) totalCount / limit);
            log.info("Page count: " + pageCount);
            //生成响应报文信息，每个文件生成一个响应的文件
            // 分页查询数据并写入文件
            for (int page = 1; page <= pageCount; page++) {
                int offset = (page - 1) * limit;
                List<StlMemInterests> stlMemInterestsInfo = stlMemInterestsMapper.selectInfoByCondition(acctMonth, fileName, offset, limit);
                for (StlMemInterests memInterests : stlMemInterestsInfo) {
                    String lineInfo = memInterests.getRowNo() + VERTICAL_BAR + memInterests.getStatus() + VERTICAL_BAR + memInterests.getErrMsg();
                    fileContents.add(lineInfo);
                }
            }
        }else {
            if (StringUtils.isBlank(errDesc)) {
                errDesc = "文件处理失败";
            }
            String errMsg = "0|99|" + errDesc;
            fileContents.add(errMsg);
        }
        Path respPath = Paths.get(membershipConfig.getRespPath());
        FileUtil.mkdir(respPath);
        String filePath = respPath + File.separator + "Resp_" + fileName;
        FileUtil.writeLines(fileContents, filePath, "UTF-8");

    }

    private void saveBatch(List<StlMemInterests> stlMemInterests) {
        Lists.partition(stlMemInterests, 1000).forEach(stlMemInterestsMapper::batchInsert);
    }

    private static void checkStlMemInterestsBaseInfo(List<String> result, StlMemInterests stlMemInterest, String orderSource, String lineNo, String prodorderSkuNum, String prodistSkuNum, String customerNum, String memberNum, String resourceCode, String province, String collectTime, List<String> resourceCodeLst) {
        if (result.size() != 9) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("本行字段数不符合要求！");
            return ;
        }
        if (StringUtils.isBlank(orderSource) || orderSource.length() != 1) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第一个字段orderSource为必填字段且长度应为1！");
            return ;
        }
        if (StringUtils.isBlank(lineNo) || lineNo.length() > 8) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第二个字段lineNo为必填字段且长度不应大于8！");
            return ;
        }
        if (StringUtils.isBlank(prodorderSkuNum) || prodorderSkuNum.length() > 19) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第三个字段prodorderSkuNum为必填字段且长度不应大于19！");
            return ;
        }
        if (StringUtils.isBlank(prodistSkuNum) || prodistSkuNum.length() > 20) {
            stlMemInterest.setStatus("99");

            stlMemInterest.setErrMsg("第四个字段prodistSkuNum为必填字段且长度不应大于20！");
            return ;
        }
        if (StringUtils.isBlank(customerNum) || customerNum.length() > 32) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第五个字段customerNum为必填字段且长度不应大于32！");
            return ;
        }
        if (StringUtils.isBlank(memberNum) || memberNum.length() > 16) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第六个字段memberNum为必填字段且长度不应大于16！");
            return ;
        }
        if (StringUtils.isBlank(resourceCode) || resourceCode.length() > 16) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第七个字段resourceCode的长度不应大于16！");
            return ;
        }
        if (!resourceCodeLst.contains(resourceCode)) {
            stlMemInterest.setStatus("04");
            stlMemInterest.setErrMsg("第七个字段resourceCode的不在规定的范围内！");
            return ;
        }
        if (StringUtils.isBlank(province) || province.length() > 3) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第八个字段province的长度不应大于3！");
            return ;
        }
        if (StringUtils.isBlank(collectTime) || collectTime.length() > 16) {
            stlMemInterest.setStatus("99");
            stlMemInterest.setErrMsg("第九个字段collectTime的长度不应超过16！");
        }
    }


    /**
     *
     * @param checkFile
     */
    private void checkFileSize(File checkFile) {
        if (checkFile.length() > 5 * 1024 * 1024) {
            log.warn("Iot checkSettleMonth Error checkFileName:{} length:{} is > 5M", checkFile.getName(), checkFile.length());
            if (checkFile.length() > 10 * 1024 * 1024) {
                ExceptionCast.cast("Iot checkSettleMonth Error checkFileName:" + checkFile.getName() + " length:" + checkFile.length() + " is > 10M，Please confirm manually");
            }

        }
    }

    private List<File> scanFiles(String path, String pattern, String acctMonth) {
        FileUtil.mkdir(path);
        Collection<File> listFiles = FileUtils.listFiles(new File(path), new CustomerFileFilter(pattern), null);
        if (listFiles.isEmpty()) {
            log.info("未扫描到Membership接口目录文件:{},{}", path, pattern);
            return null;
        }
        List<File> noAcctMonthFiles = new ArrayList<>();
        for (File file : listFiles) {
            String fileName = file.getName();
            if (!fileName.contains(acctMonth)) {
                noAcctMonthFiles.add(file);
            }
        }
        if (!noAcctMonthFiles.isEmpty()) {
            log.warn("Membership接口对应的账期文件名不等于当前账期, path:{},pattern:{}, 账期：{},文件名：{}", path, pattern, acctMonth, noAcctMonthFiles);
        }
        return Lists.newArrayList(listFiles);
    }

}
