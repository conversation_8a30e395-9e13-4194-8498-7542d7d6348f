<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="BillList">
		<xs:annotation>
			<xs:documentation>Comment describing your root element</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="ProvCode">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="3"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="IsToC">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:maxLength value="2"/>
							<xs:minLength value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="ECList" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ECInfo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="CustomerProvinceNumber">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
													<xs:minLength value="1"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="CustomerName">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="256"/>
													<xs:minLength value="1"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="BizList">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="BizInfo" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="ProductSubsList" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="SubProductInfo" minOccurs="0" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="ProductSubsID">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductID">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductName">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="DbProductID" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="DbProductName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="CoProductId" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="BlProductID" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="OneProductID" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="BillingTerm">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="6"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="PayTerm" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="6"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="FeeList" minOccurs="0">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="FeeInfo" maxOccurs="unbounded">
																										<xs:complexType>
																											<xs:sequence>
																												<xs:element name="ProdChargeCode">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="PoChargeCodeName">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="256"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="OneProChargeCode">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="OneProChargeName">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="256"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="DbProdChargeCode">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:maxLength value="20"/>
																															<xs:minLength value="1"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeVal">
																													<xs:simpleType>
																														<xs:restriction base="xs:integer">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="TaxRate" type="xs:float"/>
																												<xs:element name="Tax">
																													<xs:simpleType>
																														<xs:restriction base="xs:integer">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeNoTax">
																													<xs:simpleType>
																														<xs:restriction base="xs:integer">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeFlag" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="1"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="DiscountAmount">
																													<xs:simpleType>
																														<xs:restriction base="xs:integer">
																															<xs:totalDigits value="16"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="StandardFee" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="17"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="SettleFee" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="17"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="GhFeeType">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="1"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="RateplanID" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="22"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="RateplanName" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="256"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="StandardSalePrice" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="4000"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="SettlePrice" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="4000"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="OriginalBillMonth" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="6"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="FeeSeq" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="50"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="SalesBaseDiscount" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="PVsettleRate" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="10"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="PVsettleValue" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="17"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="RatePlanGhCode" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="RatePlanGhName" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="256"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="IfSupportPartnerSettle" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="256"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																												<xs:element name="SettlementClass" minOccurs="0">
																													<xs:simpleType>
																														<xs:restriction base="xs:string">
																															<xs:minLength value="0"/>
																															<xs:maxLength value="20"/>
																														</xs:restriction>
																													</xs:simpleType>
																												</xs:element>
																											</xs:sequence>
																										</xs:complexType>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																						<xs:element name="OnProductCode" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="OnProductName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="EjProductCode" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="EjProductName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SjProductCode" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SjProductName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="GhCode" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="22"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductClass" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductReportName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductReportItemName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="IssueTime">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="14"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ExpireTime">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:length value="14"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ProductType" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="BusiType">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ContractMain" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="PVProductClass">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="JointFlag">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="2"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="CityCode" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="0"/>
													<xs:maxLength value="20"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="CreatorName" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="0"/>
													<xs:maxLength value="64"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="ECid" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="20"/>
													<xs:minLength value="0"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="InnerCustomerFlag">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="1"/>
													<xs:maxLength value="2"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="YnInnerCustomerFlag">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="1"/>
													<xs:maxLength value="2"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="SettlementPartyOutName" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:minLength value="0"/>
													<xs:maxLength value="64"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="ToCBizList" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ToCBizInfo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="ProductSubsList" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="SubProductInfo" minOccurs="0" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="ProductSubsID">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="22"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductID">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="22"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductName">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="DbProductID">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="22"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="DbProductName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="PVProductClass">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="BillingTerm">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="PayTerm" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="0"/>
																			<xs:maxLength value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="FeeList" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="FeeInfo" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="FeeVal">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="TaxRate" type="xs:float"/>
																						<xs:element name="Tax">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="FeeNoTax">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="PVsettleRate">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="PVsettleValue">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="RatePlanGhCode" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="RatePlanGhName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:minLength value="0"/>
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="IssueTime">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="14"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ExpireTime">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="14"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="BzType" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="0"/>
																			<xs:maxLength value="32"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
