#!/bin/bash
export TXT2DB_HOME=/home/<USER>/workspace/txt2db
export BIN_PATH=${TXT2DB_HOME}/bin
export DATA_PATH=${TXT2DB_HOME}/data
export BACKUP_PATH=${TXT2DB_HOME}/backupData
export ERROR_PATH=${TXT2DB_HOME}/error
export CONF_PATH=${TXT2DB_HOME}/conf
export TABLE_PATH=${TXT2DB_HOME}/conf/table
export RESP_PATH=${TXT2DB_HOME}/resp
export LOG_PATH=${TXT2DB_HOME}/log
export WORK_PATH=${TXT2DB_HOME}/work
export SQL_PATH=${TXT2DB_HOME}/sql

####4.6
log_file=${LOG_PATH}/txt2db_`date +%Y%m%d`.log
echo $log_file
response_file=${WORK_PATH}/txt2db_response.txt

function f_Print
{
    cur_ts=`date +%Y%m%d%H%M%S`
    echo "${cur_ts} - $1" >> $log_file
}

function f_init_config
{
    f_Print "f_init_config starts"

    if [ ! -f "${CONF_PATH}/txt2db_esp.conf" ]; then
        f_Print "f_init_config - config file not found - ${CONF_PATH}/txt2db_esp.conf"
        exit
    fi 

    . ${CONF_PATH}/txt2db_esp.conf

    f_Print "f_init_config finishes"
}  ## f_init_config

function f_truncate_table
{

    f_Print "f_truncate_table starts"

    echo "use stludr; truncate table stludr.SYNC_INTERFACE_ESP_${acct_month};
    truncate table stludr.SYNC_INTERFACE_ESP_P2P_${acct_month};
    truncate table stludr.SYNC_INTERFACE_ESP_P2C_${acct_month};
    truncate table stludr.SYNC_INTERFACE_ESP_PART_${acct_month};" | mysql -u$db_user -p$db_passwd -h$db_host -P$db_port

    f_Print "f_truncate_table finishes"
    
}  ## f_truncate_table

function f_load_data
{

    f_Print "f_load_data starts"

    ####信息梳理：

    ####    1.SYNC_INTERFACE_ESP.ctl
    ####ID,PROV_CODE,PAY_TAG,GROUP_CUSTOMER_NUMBER,GROUP_CUSTOMER_NAME,EBOSS_CUSTOMER_NUMBER,EBOSS_CUSTOMER_NAME,ADDRESS_PROV_CODE,INNER_EC_FLAG,EC_DEPARTMENT_NAME,EC_CREATOR_NAME,EC_CREATOR_TEL,ACCOUNT_ID,ACCOUNT_NAME,SUBS_ID,PRODUCT_CLASS_NAME,PRODUCT_DETAIL_NAME,PRODUCT_ID,PRODUCT_NAME,MAIN_CONTRACT,RUN_DEPARTMENT_NAME,ICT_FLAG,RATEPLAN_ID,RATEPLAN_NAME,FEE_VAL,TAX_RATE,TAX,FEE_NO_TAX,FEE_FLAG							,ORIGINAL_BILL_MONTH,DISCOUNT_AMOUNT,STANDARD_FEE,SETTLE_FEE,BILLING_TERM,PAY_TERM,SETTLE_ITEM,BUSI_MODE,CHARGE_CODE,CHARGE_CODE_NAME,CITY_CODE,INI_PRICE,SETTLE_PRICE,DBPRODUCTCODE,DBPRODCHARGECODE,FILE_NAME
    ####替换表名 stludr.SYNC_INTERFACE_ESP_####ACCT_MONTH####  -> stludr.SYNC_INTERFACE_ESP_202304
    ####数据文件 SYNC_INTERFACE_ESP.dat

    ####    2.SYNC_INTERFACE_ESP_P2P.ctl
    ####ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,REMARK,FILE_NAME
    ####替换表名 stludr.SYNC_INTERFACE_ESP_P2P_####ACCT_MONTH####  -> stludr.SYNC_INTERFACE_ESP_P2P_202304
    ####数据文件 SYNC_INTERFACE_ESP_P2P.dat

    ####    3.SYNC_INTERFACE_ESP_P2C.ctl
    ####ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,SETTLE_CLASS,FILE_NAME
    ####替换表名 stludr.SYNC_INTERFACE_ESP_P2C_####ACCT_MONTH####  -> stludr.SYNC_INTERFACE_ESP_P2C_202304
    ####数据文件 SYNC_INTERFACE_ESP_P2C.dat

    ####    4.SYNC_INTERFACE_ESP_PART.ctl
    ####ID,PARTNER_CODE,PARTNER_NAME,PAR_SETTLE_RATE,PAR_RES_SETTL_RATE,SETTLEMENT_TYPE,PAR_SETTLE_PAY_TYPE,PAR_SETTL_AMOUNT,FILE_NAME
    ####替换表名 stludr.SYNC_INTERFACE_ESP_PART_####ACCT_MONTH####  -> stludr.SYNC_INTERFACE_ESP_PART_202304
    ####数据文件 SYNC_INTERFACE_ESP_PART.dat


    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_ESP_tmpl.ctl  > ${CONF_PATH}/SYNC_INTERFACE_ESP.ctl
    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_ESP_P2P_tmpl.ctl  > ${CONF_PATH}/SYNC_INTERFACE_ESP_P2P.ctl
    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_ESP_P2C_tmpl.ctl  > ${CONF_PATH}/SYNC_INTERFACE_ESP_P2C.ctl
    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_ESP_PART_tmpl.ctl  > ${CONF_PATH}/SYNC_INTERFACE_ESP_PART.ctl

    for filename in `find ${DATA_PATH} -type f -name "${naming_convention}"`
    do
        filename_only=`basename $filename`
        f_Print "f_load_data - ${filename_only} - Starting loading file"

        tr -d '\r' < $filename > ${WORK_PATH}/${filename_only}.mod

        grep -v '!!!!P2P!!!!' ${WORK_PATH}/${filename_only}.mod | grep -v '!!!!P2C!!!!' | grep -v '!!!!PARTNER!!!!' > ${WORK_PATH}/${filename_only}.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $0"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.tmp > ${WORK_PATH}/${filename_only}

        grep '!!!!P2P!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.P2P.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $1"!!!!"$3"!!!!"$4"!!!!"$7"!!!!"$6"!!!!"$5"!!!!"$8"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.P2P.tmp > ${WORK_PATH}/${filename_only}.P2P

        grep '!!!!P2C!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.P2C.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $1"!!!!"$3"!!!!"$4"!!!!"$5"!!!!"$6"!!!!"$7"!!!!"$8"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.P2C.tmp > ${WORK_PATH}/${filename_only}.P2C
				
				grep '!!!!PARTNER!!!!' ${WORK_PATH}/${filename_only}.mod > ${WORK_PATH}/${filename_only}.PART.tmp
        awk -F '!!!!' -v filename_only="filename_only"  '{print $1"!!!!"$3"!!!!"$4"!!!!"$5"!!!!"$8"!!!!"$6"!!!!"$9"!!!!"$7"!!!!""'"$filename_only"'"}' ${WORK_PATH}/${filename_only}.PART.tmp > ${WORK_PATH}/${filename_only}.PART
        
        cd ${WORK_PATH}

        cp ${WORK_PATH}/${filename_only} ${WORK_PATH}/SYNC_INTERFACE_ESP.dat
        ####sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_ESP.ctl log=${LOG_PATH}/sqlldr_log_${filename_only} bad=${WORK_PATH}/bad.${filename_only}
        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_ESP.dat' into table stludr.SYNC_INTERFACE_ESP_${acct_month}  fields terminated by '!!!!' lines terminated by '\n' (ID,PROV_CODE,PAY_TAG,GROUP_CUSTOMER_NUMBER,GROUP_CUSTOMER_NAME,EBOSS_CUSTOMER_NUMBER,EBOSS_CUSTOMER_NAME,ADDRESS_PROV_CODE,INNER_EC_FLAG,EC_DEPARTMENT_NAME,EC_CREATOR_NAME,EC_CREATOR_TEL,ACCOUNT_ID,ACCOUNT_NAME,SUBS_ID,PRODUCT_CLASS_NAME,PRODUCT_DETAIL_NAME,PRODUCT_ID,PRODUCT_NAME,MAIN_CONTRACT,RUN_DEPARTMENT_NAME,ICT_FLAG,RATEPLAN_ID,RATEPLAN_NAME,FEE_VAL,TAX_RATE,TAX,FEE_NO_TAX,FEE_FLAG							,ORIGINAL_BILL_MONTH,DISCOUNT_AMOUNT,STANDARD_FEE,SETTLE_FEE,BILLING_TERM,PAY_TERM,SETTLE_ITEM,BUSI_MODE,CHARGE_CODE,CHARGE_CODE_NAME,CITY_CODE,INI_PRICE,SETTLE_PRICE,DBPRODUCTCODE,DBPRODCHARGECODE,FILE_NAME);" 

        cp ${WORK_PATH}/${filename_only}.P2P ${WORK_PATH}/SYNC_INTERFACE_ESP_P2P.dat
        ####sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_ESP_P2P.ctl log=${LOG_PATH}/sqlldr_log_${filename_only}.P2P bad=${WORK_PATH}/bad.${filename_only}.P2P
        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_ESP_P2P.dat' into table stludr.SYNC_INTERFACE_ESP_P2P_${acct_month}  fields terminated by '!!!!' lines terminated by '\n' (ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,REMARK,FILE_NAME);" 

        cp ${WORK_PATH}/${filename_only}.P2C ${WORK_PATH}/SYNC_INTERFACE_ESP_P2C.dat
        ####sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_ESP_P2C.ctl log=${LOG_PATH}/sqlldr_log_${filename_only}.P2C bad=${WORK_PATH}/bad.${filename_only}.P2C
        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_ESP_P2C.dat' into table stludr.SYNC_INTERFACE_ESP_P2C_${acct_month}  fields terminated by '!!!!' lines terminated by '\n' (ID,SETTLEMENT_PARTY_IN,SETTLEMENT_PARTY_OUT,SETTLEMENT_RATE,SETTLEMENT_TYPE,SETTLEMENT_AMOUNT,SETTLE_CLASS,FILE_NAME);" 

		cp ${WORK_PATH}/${filename_only}.PART ${WORK_PATH}/SYNC_INTERFACE_ESP_PART.dat
        ####sqlldr ${a_db_str} control=${CONF_PATH}/SYNC_INTERFACE_ESP_PART.ctl log=${LOG_PATH}/sqlldr_log_${filename_only}.PART bad=${WORK_PATH}/bad.${filename_only}.PART
        mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -e "use stludr; load data local infile '${WORK_PATH}/SYNC_INTERFACE_ESP_PART.dat' into table stludr.SYNC_INTERFACE_ESP_PART_${acct_month}  fields terminated by '!!!!' lines terminated by '\n' (ID,PARTNER_CODE,PARTNER_NAME,PAR_SETTLE_RATE,PAR_RES_SETTL_RATE,SETTLEMENT_TYPE,PAR_SETTLE_PAY_TYPE,PAR_SETTL_AMOUNT,FILE_NAME);" 

        cd -

        ##original data file move to backup dir
        mv  $filename ${BACKUP_PATH}/${acct_month}/

        ##delete file from work dir
        rm ${WORK_PATH}/${filename_only} ${WORK_PATH}/${filename_only}.tmp
        rm ${WORK_PATH}/${filename_only}.P2P*
        rm ${WORK_PATH}/${filename_only}.P2C*
        rm ${WORK_PATH}/${filename_only}.PART*
        rm ${WORK_PATH}/SYNC_INTERFACE_ESP*.dat

        f_Print "f_load_data - ${filename_only} - loading file - done"
    done

    f_Print "f_load_data finishes" 

}  ## f_load_data

function f_validation
{

    f_Print "f_validation starts"

    ####将所有需要执行的sql命令写入一个sql文件中依次执行
    sql_file="${SQL_PATH}/run_esp.sql"
    : > ${sql_file}

    echo "use stludr; " >>  ${sql_file}

    #### code F023
    echo "update stludr.SYNC_INTERFACE_ESP_${acct_month} set status='F023'  where (prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) or address_prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1'))) and status is null; " >>  ${sql_file}
    echo "update stludr.SYNC_INTERFACE_ESP_P2P_${acct_month} set status='F023' where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acct_month} where status='F023') and status is null; " >>  ${sql_file}
    echo "update stludr.SYNC_INTERFACE_ESP_P2C_${acct_month} set status='F023' where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acct_month} where status='F023') and status is null; " >> ${sql_file}
    echo "update stludr.SYNC_INTERFACE_ESP_PART_${acct_month} set status='F023' where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acct_month} where status='F023') and status is null; " >> ${sql_file}

    echo "update stludr.SYNC_INTERFACE_ESP_${acct_month} a set status = '0' where status is null; " >>  ${sql_file}
    echo "update stludr.SYNC_INTERFACE_ESP_P2P_${acct_month} set status='0' where status is null; " >>  ${sql_file}
    echo "update stludr.SYNC_INTERFACE_ESP_P2C_${acct_month} set status='0' where status is null; " >>  ${sql_file}
    echo "update stludr.SYNC_INTERFACE_ESP_PART_${acct_month} set status='0' where status is null;  " >>  ${sql_file}
    echo "commit; " >>  ${sql_file}

    f_Print "f_validation finishes"
    
}  ## f_validation

function f_get_response_error
{

    f_Print "f_get_response_error starts"

    ##将spool模板文件中的内容进行替换，生成sql文件，然后执行该sql
    sed "s/####ACCT_MONTH####/$acct_month/g" ${CONF_PATH}/SYNC_INTERFACE_ESP_SPOOL.tmpl  > ${CONF_PATH}/SYNC_INTERFACE_ESP_SPOOL.sql
    cat  ${CONF_PATH}/SYNC_INTERFACE_ESP_SPOOL.sql | mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -N -s  > ${response_file};

    f_Print "f_get_response_error finishes"
    
}  ## f_get_response_error

function f_get_response_file_error 
{
    f_Print "f_get_response_file_error starts"

    ##Remove empty lines and spaces
    sed '/^  *$/d' ${response_file} | sed '/^ *$/d' | sed 's/ //g' > ${response_file}.tmp
    mv ${response_file}.tmp ${response_file}

    response_header_1="<BillList>\n\t<Org_FileName>"
    response_header_2="</Org_FileName>\n\t<Resp_Date>"
    response_header_3="</Resp_Date>\n\t<FileStatus>2</FileStatus>\n\t<ErrorRecords>"
    response_tail="\t</ErrorRecords>\n</BillList>"

    ##Create file-level response files
    for filename in `awk -F ":DR:" '{print $1}' ${response_file} | sort | uniq`
    do
        f_Print "f_get_response_file_error - ${filename} - generating response file"
        current_ts=`date +%Y%m%d%H%M%S`
        echo "filename=$filename"
        echo -e "${response_header_1}${filename}${response_header_2}${current_ts}${response_header_3}" > ${WORK_PATH}/Resp_${filename}
        grep "^$filename"  ${response_file} | awk -F ":DR:" '{print "\t\t"$2}'   >> ${WORK_PATH}/Resp_${filename}
        if [ -f "${WORK_PATH}/bad.$filename" ]; then
            awk -F ","  '{for (i=2;i<=NF;i++)printf("%s,", $i);print ""}'  ${WORK_PATH}/bad.$filename | cut -c1-256 | while read line
            do
                echo -e "\t\t<ErrorRecord><ErrorCode>25</ErrorCode><ErrorNode>$line</ErrorNode></ErrorRecord>" >> ${WORK_PATH}/Resp_${filename}
            done
        fi 
        echo -e "${response_tail}" >> ${WORK_PATH}/Resp_${filename}
        mv ${WORK_PATH}/Resp_${filename} ${RESP_PATH}/Resp_${filename}
        f_Print "f_get_response_file_error - ${filename} - response file created - ${RESP_PATH}/Resp_${filename}"
    done

    rm ${response_file}

    f_Print "f_get_response_file_error finishes"

} ## f_get_response_file_error

function f_get_response
{

    f_Print "f_get_response starts"

    mysql -u$db_user -p$db_passwd -h$db_host -P$db_port -N -s -e "  select *  from ( select distinct file_name from stludr.sync_interface_esp_${acct_month}  minus select distinct file_name from  stludr.sync_interface_esp_${acct_month} where status <> '0' ) order by file_name; " >  ${response_file};

    f_Print "f_get_response finishes"
    
}  ## f_get_response

function f_get_response_file 
{
    f_Print "f_get_response_file starts"

    ##Remove empty lines and spaces
    sed '/^  *$/d' ${response_file} | sed '/^ *$/d' | sed 's/ //g' > ${response_file}.tmp
    mv ${response_file}.tmp ${response_file}

    response_header_1="<BillList>\n\t<Org_FileName>"
    response_header_2="</Org_FileName>\n\t<Resp_Date>"
    response_header_3="</Resp_Date>\n\t<FileStatus>0</FileStatus>\n</BillList>"


    ##Create file-level response files
    for filename in `awk -F "," '{print $1}' ${response_file} | sort | uniq`
    do
        f_Print "f_get_response_file - ${filename} - generating response file"
        current_ts=`date +%Y%m%d%H%M%S`
        echo "filename=$filename"
        echo -e "${response_header_1}${filename}${response_header_2}${current_ts}${response_header_3}" > ${WORK_PATH}/Resp_${filename}
        mv ${WORK_PATH}/Resp_${filename} ${RESP_PATH}/Resp_${filename}
        f_Print "f_get_response_file - ${filename} - response file created - ${RESP_PATH}/Resp_${filename}"
    done

    rm ${response_file}

    f_Print "f_get_response_file finishes"

} ## f_get_response_file

##main starts here
if (( $# < 1 )); then
    echo "usage: txt2db_esp.sh <acct_month>"
    exit
fi

f_Print "*****************************************************"
f_Print "                txt2db_esp.sh starts                    "
f_Print "*****************************************************"

##Read input param acct_month
acct_month=$1
f_Print "acct_month=$acct_month"

##Create backup data path
mkdir -p ${BACKUP_PATH}/${acct_month}
f_Print "backup data path ${BACKUP_PATH}/${acct_month} created"


f_init_config
if (( $? != 0)); then
   f_Print "f_init_config failure. Exit now." 
fi

##Check input dir, exit when no input
input_file_no=`find ${DATA_PATH} -type f -name "${naming_convention}" | wc -l`
echo input_file_no=$input_file_no
if (( input_file_no < 1 )); then
    f_Print "No input file found under dir ${DATA_PATH}. Exit now."
    exit
else
    f_Print "No of input file found under dir ${DATA_PATH}: ${input_file_no}"
fi

##Truncate data table before loading data files into DB
f_truncate_table
if (( $? != 0)); then
   f_Print "f_truncate_table failure. Exit now." 
fi

f_load_data
if (( $? != 0)); then
   f_Print "f_load_data failure. Exit now." 
fi

f_validation
if (( $? != 0)); then
   f_Print "f_validation failure. Exit now." 
fi

f_get_response_error
if (( $? != 0)); then
   f_Print "f_get_response_error failure. Exit now." 
fi

f_get_response_file_error
if (( $? != 0)); then
   f_Print "f_get_response_file_error failure. Exit now." 
fi

f_get_response
if (( $? != 0)); then
   f_Print "f_get_response failure. Exit now." 
fi

f_get_response_file
if (( $? != 0)); then
   f_Print "f_get_response_file failure. Exit now." 
fi

f_Print "*****************************************************"
f_Print "                txt2db_esp.sh finishes                   "
f_Print "*****************************************************"
f_Print ""
f_Print ""

