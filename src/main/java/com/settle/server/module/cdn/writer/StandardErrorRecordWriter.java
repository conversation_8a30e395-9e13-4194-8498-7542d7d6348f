package com.settle.server.module.cdn.writer;

import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;

/**
 * 通过调用其 <code>toString</code> 方法将 {@link Record} 的 <strong>payload</strong> 写入标准错误的记录写入器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class StandardErrorRecordWriter<P> implements RecordWriter<P> {

    @Override
    public void writeRecords(Batch<P> batch) {
        for (Record<P> record : batch) {
            System.err.println(record.getPayload().toString());
        }
    }

}
