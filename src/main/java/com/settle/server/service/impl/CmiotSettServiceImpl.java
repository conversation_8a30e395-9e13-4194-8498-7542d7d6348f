package com.settle.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.settle.server.config.CmiotConfig;
import com.settle.server.dao.stludr.StlCmiotSettDao;
import com.settle.server.entity.StlCmiotSett;
import com.settle.server.service.CmiotSettService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FilenameFilter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Classname CmiotSettServiceImpl
 * @Description TODO
 * @Date 2023/10/17 15:07
 * <AUTHOR>
 * @Version 1.0.0
 */
@Slf4j
@Service
public class CmiotSettServiceImpl implements CmiotSettService {

    @Autowired
    private StlCmiotSettDao cmiotSettDao;

    public static final char VERTICAL_BAR = '|';

    @Autowired
    private CmiotConfig cmiotConfig;

    @Override
    public void save(String acctMonth) throws Exception {
        Path errorPath = Paths.get(cmiotConfig.getErrorPath(), acctMonth);
        Path backupPath = Paths.get(cmiotConfig.getBackupPath(), acctMonth);
        if (FileUtil.exist(cmiotConfig.getDataPath())) {
            List<Boolean> flags = new ArrayList();
            File dataFile = FileUtil.file(cmiotConfig.getDataPath());
            File[] cmiotFiles = dataFile.listFiles(new FilenameFilter() {
                // 将被选出来，其余被过滤掉
                @Override
                public boolean accept(File dir, String fileName) {
                    Pattern pattern = Pattern.compile(cmiotConfig.getValidName());
                    boolean isValid = pattern.matcher(fileName).matches();
                    if (isValid) {
                        flags.add(true);
                    } else {
                        flags.add(false);
                    }
                    return isValid;
                }
            });
            if (flags.contains(true)) {
                cmiotSettDao.deleteBySettleMonth(acctMonth);
            }
            if (cmiotFiles.length == 0) {
                log.warn("cmIot No of input file not found under dir path:{}", cmiotConfig.getDataPath());
            }
            for (File cmiotFile : cmiotFiles) {
                String fileName = cmiotFile.getName();
                List<StlCmiotSett> stlCmiotSetts = new ArrayList<>();
                List<String> cmIotLines = FileUtil.readUtf8Lines(cmiotFile);
                cmIotLines.removeAll(Arrays.asList("", null));
                if (cmIotLines.size() > 0) {
                    log.info("Iot input file details path:{}, row:{}", cmiotFile.getAbsolutePath(), cmIotLines.size());
                    try {
                        stlCmiotSetts.addAll(cmIotLines.stream()
                                .map(cmIotLine -> {
                                    List<String> result = StrUtil.split(cmIotLine, VERTICAL_BAR, false, false);
                                    StlCmiotSett stlCmiotSett = new StlCmiotSett();
                                    if (result.size() != 7) {
                                        throw new RuntimeException(String.format("cmIot Verification Error fileName: %s, 该行不是7个参数, is %s, iotLine is %s", fileName, result.size(), cmIotLine));
                                    }
                                    String settleMonth = result.get(0);
                                    String productSpecNum = result.get(1);
                                    String settleType = result.get(2);
                                    String settleOut = result.get(3);
                                    String settleIn = result.get(4);
                                    String settleAmount = result.get(5);
                                    String taxRate = result.get(6);
                                    validSettleMonth(settleMonth, acctMonth, fileName, cmIotLine);
                                    validProductSpecNum(productSpecNum, fileName, cmIotLine);
                                    validSettleType(settleType, fileName, cmIotLine);
                                    validSettleOut(settleOut, fileName, cmIotLine);
                                    validSettleIn(settleIn, fileName, cmIotLine);
                                    validSettleAmount(settleAmount, fileName, cmIotLine);
                                    validTaxRate(taxRate, fileName, cmIotLine);
                                    stlCmiotSett.setSettleMonth(settleMonth);
                                    stlCmiotSett.setProductSpecNum(productSpecNum);
                                    stlCmiotSett.setSettleType(settleType);
                                    stlCmiotSett.setSettleOut(settleOut);
                                    stlCmiotSett.setSettleIn(settleIn);
                                    stlCmiotSett.setSettleAmount(settleAmount);
                                    stlCmiotSett.setTaxRate(taxRate);
                                    return stlCmiotSett;
                                })
                                .collect(Collectors.toList()));
                        log.info("cmIot loading_file_starts fileName:{} acctMonth:{}", fileName, acctMonth);
                        cmiotSettDao.batchInsert(stlCmiotSetts);
                        log.info("cmIot loading_file_done fileName:{} acctMonth:{}", fileName, acctMonth);
                        Files.createDirectories(backupPath);
                        Files.move(cmiotFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                    } catch (Exception e) {
                        log.error("cmIot 执行失败 fileName:{} acctMonth:{} error：{}", fileName, acctMonth, e.getMessage(), e);
                        Files.createDirectories(errorPath);
                        Files.move(cmiotFile.toPath(), errorPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                        cmiotSettDao.deleteBySettleMonth(acctMonth);
                        throw e;
                    }
                } else {
                    log.warn("cmIot input file is empty dir {}. Exit now.", cmiotFile.getAbsolutePath());
                    Files.createDirectories(backupPath);
                    Files.move(cmiotFile.toPath(), backupPath.resolve(fileName), StandardCopyOption.REPLACE_EXISTING);
                }
            }
        } else {
            log.error("cmIot checkFile data path:{} does not exist", cmiotConfig.getDataPath());
        }
    }

    private void validSettleMonth(String settleMonth, String acctMonth, String fileName, String cmIotLine) {
        if (settleMonth.length() != 6 || !settleMonth.equals(acctMonth)) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 账期校验error, 文件账期长度不是6或者文件账期不等于输入账期: cmIotLine:%s, acctMonth:%s", fileName, cmIotLine, acctMonth));
        }
    }

    private void validProductSpecNum(String productSpecNum, String fileName, String cmIotLine) {
        if (productSpecNum.length() == 0 || productSpecNum.length() > 6) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 业务编码校验error, 业务编码不为空或者长度不大于6: cmIotLine:%s, productSpecNum:%s", fileName, cmIotLine, productSpecNum));
        }
    }

    private void validSettleType(String settleType, String fileName, String cmIotLine) {
        if (settleType.length() != 2) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 结算类型校验error, 结算类型长度不是2: cmIotLine:%s, settleType:%s", fileName, cmIotLine, settleType));
        }
    }

    private void validSettleOut(String settleOut, String fileName, String cmIotLine) {
        if (settleOut.length() == 0 || settleOut.length() > 4) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 结出方校验error, 结出方不为空或者长度不大于4: cmIotLine:%s, settleOut:%s", fileName, cmIotLine, settleOut));
        }
    }

    private void validSettleIn(String settleIn, String fileName, String cmIotLine) {
        if (settleIn.length() == 0 || settleIn.length() > 4) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 结入方校验error, 结入方不为空或者长度不大于4: cmIotLine:%s, settleIn:%s", fileName, cmIotLine, settleIn));
        }
    }

    private void validSettleAmount(String settleAmount, String fileName, String cmIotLine) {
        if (settleAmount.length() == 0 || settleAmount.length() > 20) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 结算含税金额校验error, 结算含税金额不为空或者长度不大于20: cmIotLine:%s, settleAmount:%s", fileName, cmIotLine, settleAmount));
        }
    }

    private void validTaxRate(String taxRate, String fileName, String cmIotLine) {
        if (taxRate.length() == 0 || taxRate.length() > 2) {
            throw new RuntimeException(String.format("cmIot Verification Error fileName: %s 税率校验error, 税率不为空或者长度不大于2: cmIotLine:%s, taxRate:%s", fileName, cmIotLine, taxRate));
        }
    }

}
