<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.bigdata.SyncInterfaceBigdataFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.bigdata.SyncInterfaceBigdataFlow">
        <id column="ID" property="id"/>
        <result column="IN_PROVINCE_NAME" property="inProvinceName"/>
        <result column="IN_PROVINCE_CODE" property="inProvinceCode"/>
        <result column="OUT_PROVINCE_NAME" property="outProvinceName"/>
        <result column="OUT_PROVINCE_CODE" property="outProvinceCode"/>
        <result column="BUS_TYPE" property="busType"/>
        <result column="DOWN_DATA_TRAFFIC" property="downDataTraffic"/>
        <result column="SETTLE_MONTH" property="settleMonth"/>
        <result column="FILE_NAME" property="fileName"/>
    </resultMap>
    <insert id="saveBatch" parameterType="java.util.List">
        insert into sync_interface_bigdata_flow values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id},
            #{item.inProvinceName},
            #{item.inProvinceCode},
            #{item.outProvinceName},
            #{item.outProvinceCode},
            #{item.busType},
            #{item.downDataTraffic},
            #{item.settleMonth},
            #{item.fileName}
            )
        </foreach>
    </insert>

</mapper>
