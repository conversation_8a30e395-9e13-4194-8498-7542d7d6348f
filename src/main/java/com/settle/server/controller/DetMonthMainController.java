package com.settle.server.controller;

import com.settle.server.proc.StlProcUpdateDetMonth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class DetMonthMainController {
    @PostMapping("/testDetMonth")
    public void detMonthMain(HttpServletRequest req, @RequestParam Map<String,Object> request) {
        String arg= (String) request.get("request");
        List<String> args= Arrays.asList(arg.split(","));
        log.info("***设置页面"
                + ((args.get(1).equalsIgnoreCase("2")) ? "实收报表" : "应收报表")
                + "默认最大账期:" + args.get(0) + "***");
        StlProcUpdateDetMonth spudm = new StlProcUpdateDetMonth();
        boolean res = spudm.startProc(args.get(0), args.get(1));
        log.info("***默认页面查询最大账期" + (res ? "成功" : "失败") + "!***");
    }
}
