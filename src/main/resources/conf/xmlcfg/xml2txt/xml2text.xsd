<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- edited with XMLSpy v2008 sp1 (http://www.altova.com) by <PERSON><PERSON><PERSON><PERSON> (HP) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="Configure">
		<xs:annotation>
			<xs:documentation>Comment describing your root element</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Paths">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Log4j" type="xs:string"/>
							<xs:element name="DBConf" type="xs:string"/>
							<xs:element name="Basepath" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="In" type="xs:string"/>
										<xs:element name="Out" type="xs:string"/>
										<xs:element name="Back" type="xs:string"/>
										<xs:element name="Resp" type="xs:string"/>
										<xs:element name="Xslt" type="xs:string"/>
										<xs:element name="Xsd" type="xs:string"/>
									</xs:sequence>
									<xs:attribute name="name" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:string"/>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Category">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Entry" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="Basepath" type="xs:string"/>
										<xs:element name="In" type="xs:string"/>
										<xs:element name="Out" type="xs:string"/>
										<xs:element name="Back" type="xs:string"/>
										<xs:element name="Resp" type="xs:string"/>
										<xs:element name="Xslt" type="xs:string"/>
										<xs:element name="Xsd" type="xs:string"/>
										<xs:element name="Regexp" type="xs:string"/>
									</xs:sequence>
									<xs:attribute name="name" type="xs:string" use="required"/>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
