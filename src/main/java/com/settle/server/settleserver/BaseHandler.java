package com.settle.server.settleserver;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import com.settle.server.utils.SelfFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;

@Slf4j
public abstract class BaseHandler implements Handler {
  @Value("${settleserver.dataPath}")
  public String dataPath;

  @Value("${settleserver.backupPath}")
  public String backupPath;

  @Value("${settleserver.ex_orig_call_center}")
  public String ex_orig_call_center;

  @Value("${settleserver.ex_orig_inter_line}")
  public String ex_orig_inter_line;

  @Value("${settleserver.ex_orig_line}")
  public String ex_orig_line;

  @Value("${settleserver.ex_inter_ent_tv}")
  public String ex_inter_ent_tv;

  @Value("${settleserver.COMMIT_COUNT}")
  public String COMMIT_COUNT;

  @Value("${settleserver.confTable}")
  public String confTable;


  public String getStringValue(String key){
    if("ex_orig_call_center".equals(key)){
      return ex_orig_call_center;
    }else if("ex_orig_inter_line".equals(key)){
      return ex_orig_inter_line;
    }else if("ex_orig_line".equals(key)){
      return ex_orig_line;
    }else if("ex_inter_ent_tv".equals(key)){
      return ex_inter_ent_tv;
    }
    return null;
  }

  protected String lastMonth = "";

  protected String bak = "";

  protected File[] tempList = null;
  protected File file = null;
  protected String fileName = null;

  protected File dataFile = null;
  protected String dataFileName = null;
  protected Connection conn;
  protected PreparedStatement prep;

  public void init()
  {
    Calendar c = Calendar.getInstance();
    SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
    this.bak = df.format(c.getTime());
    c.add(2, -1);
    SimpleDateFormat dfs = new SimpleDateFormat("yyyyMM");
    this.lastMonth = dfs.format(c.getTime());
    log.info("The current account is ready to import the database of " +
      this.lastMonth + " month data source...");
  }

  public void init(String date) {
    Calendar c = Calendar.getInstance();
    SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
    this.bak = df.format(c.getTime());
    this.lastMonth = date;
    log.info("The current account is ready to import the database of " +
      this.lastMonth + " month data source...");
  }

  @Async
  public boolean start() {
    File data_path = new File(dataPath + File.separator +
      this.lastMonth);
    if ((!(data_path.exists())) && (!(data_path.isDirectory()))) {
      log.debug("Temporarily Unavailable " + this.lastMonth +
        " monthly account of the File Interface...");
      return false;
    }
    File tablePath = new File(confTable);
    this.tempList = tablePath.listFiles();
    for (int i = 0; i < this.tempList.length; ++i) {
      this.file = this.tempList[i];
      if (this.file.isFile()) {
        this.fileName = this.file.getName();
        if (!("PROPERTIES".equals(this.fileName
          .substring(this.fileName.lastIndexOf(".") + 1)
          .toUpperCase()))) continue;
        intoDB();
        backup();
      }

    }

    SelfFile del = new SelfFile();
    del.delFolder(dataPath + File.separator + this.lastMonth);
    return true;
  }

  private void backup() {
    log.info("Start backup interface file:" + this.fileName + "...");

    File file1 = new File(backupPath);
    if ((!(file1.exists())) && (!(file1.isDirectory()))) {
      file1.mkdir();
    }
    file1 = new File(backupPath + File.separator +
      this.lastMonth);
    if ((!(file1.exists())) && (!(file1.isDirectory()))) {
      file1.mkdir();
    }
    String path = backupPath + File.separator + this.lastMonth +
      File.separator + this.bak;
    file1 = new File(path);
    if ((!(file1.exists())) && (!(file1.isDirectory()))) {
      file1.mkdir();
    }
    file1 = new File(path + File.separator + this.dataFileName);
    FileInputStream fi = null;
    FileOutputStream fo = null;
    FileChannel in = null;
    FileChannel out = null;
    try {
      fi = new FileInputStream(this.dataFile);
      fo = new FileOutputStream(file1);
      in = fi.getChannel();
      out = fo.getChannel();
      in.transferTo(0L, in.size(), out);
    } catch (IOException e) {
      e.printStackTrace();
    } finally {
      try {
        fi.close();
        in.close();
        fo.close();
        out.close();
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    log.info("backup end...");
  }

  protected abstract String spliceSQL();

  protected abstract void delTable();

  protected abstract String intoDB();
}
