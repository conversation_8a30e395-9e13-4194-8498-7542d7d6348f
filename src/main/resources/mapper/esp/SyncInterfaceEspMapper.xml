<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.esp.SyncInterfaceEspMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.esp.SyncInterfaceEsp">
        <result column="ID" property="id" />
        <result column="PROV_CODE" property="provCode" />
        <result column="PAY_TAG" property="payTag" />
        <result column="GROUP_CUSTOMER_NUMBER" property="groupCustomerNumber" />
        <result column="GROUP_CUSTOMER_NAME" property="groupCustomerName" />
        <result column="EBOSS_CUSTOMER_NUMBER" property="ebossCustomerNumber" />
        <result column="EBOSS_CUSTOMER_NAME" property="ebossCustomerName" />
        <result column="ADDRESS_PROV_CODE" property="addressProvCode" />
        <result column="INNER_EC_FLAG" property="innerEcFlag" />
        <result column="EC_DEPARTMENT_NAME" property="ecDepartmentName" />
        <result column="EC_CREATOR_NAME" property="ecCreatorName" />
        <result column="EC_CREATOR_TEL" property="ecCreatorTel" />
        <result column="ACCOUNT_ID" property="accountId" />
        <result column="ACCOUNT_NAME" property="accountName" />
        <result column="SUBS_ID" property="subsId" />
        <result column="PRODUCT_CLASS_NAME" property="productClassName" />
        <result column="PRODUCT_DETAIL_NAME" property="productDetailName" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="PRODUCT_NAME" property="productName" />
        <result column="MAIN_CONTRACT" property="mainContract" />
        <result column="RUN_DEPARTMENT_NAME" property="runDepartmentName" />
        <result column="ICT_FLAG" property="ictFlag" />
        <result column="RATEPLAN_ID" property="rateplanId" />
        <result column="RATEPLAN_NAME" property="rateplanName" />
        <result column="FEE_VAL" property="feeVal" />
        <result column="TAX_RATE" property="taxRate" />
        <result column="TAX" property="tax" />
        <result column="FEE_NO_TAX" property="feeNoTax" />
        <result column="FEE_FLAG" property="feeFlag" />
        <result column="ORIGINAL_BILL_MONTH" property="originalBillMonth" />
        <result column="DISCOUNT_AMOUNT" property="discountAmount" />
        <result column="STANDARD_FEE" property="standardFee" />
        <result column="SETTLE_FEE" property="settleFee" />
        <result column="BILLING_TERM" property="billingTerm" />
        <result column="PAY_TERM" property="payTerm" />
        <result column="SETTLE_ITEM" property="settleItem" />
        <result column="BUSI_MODE" property="busiMode" />
        <result column="CHARGE_CODE" property="chargeCode" />
        <result column="CHARGE_CODE_NAME" property="chargeCodeName" />
        <result column="CITY_CODE" property="cityCode" />
        <result column="INI_PRICE" property="iniPrice" />
        <result column="SETTLE_PRICE" property="settlePrice" />
        <result column="STATUS" property="status" />
        <result column="FILE_NAME" property="fileName" />
        <result column="ORIG_NODE" property="origNode" />
        <result column="DBPRODUCTCODE" property="dbproductcode" />
        <result column="DBPRODCHARGECODE" property="dbprodchargecode" />
    </resultMap>
    <insert id="saveEsp">
        INSERT INTO SYNC_INTERFACE_ESP_${acctMonth}
            (
             ID,
             PROV_CODE,
             PAY_TAG,
             GROUP_CUSTOMER_NUMBER,
             GROUP_CUSTOMER_NAME,
             EBOSS_CUSTOMER_NUMBER,
             EBOSS_CUSTOMER_NAME,
             ADDRESS_PROV_CODE,
             INNER_EC_FLAG,
             EC_DEPARTMENT_NAME,
             EC_CREATOR_NAME,
             EC_CREATOR_TEL,
             ACCOUNT_ID,
             ACCOUNT_NAME,
             SUBS_ID,
             PRODUCT_CLASS_NAME,
             PRODUCT_DETAIL_NAME,
             PRODUCT_ID,
             PRODUCT_NAME,
             MAIN_CONTRACT,
             RUN_DEPARTMENT_NAME,
             ICT_FLAG,
             RATEPLAN_ID,
             RATEPLAN_NAME,
             FEE_VAL,
             TAX_RATE,
             TAX,
             FEE_NO_TAX,
             FEE_FLAG,
             ORIGINAL_BILL_MONTH,
             DISCOUNT_AMOUNT,
             STANDARD_FEE,
             SETTLE_FEE,
             BILLING_TERM,
             PAY_TERM,
             SETTLE_ITEM,
             BUSI_MODE,
             CHARGE_CODE,
             CHARGE_CODE_NAME,
             CITY_CODE,
             INI_PRICE,
             SETTLE_PRICE,
             STATUS,
             FILE_NAME,
             ORIG_NODE,
             DBPRODUCTCODE,
             DBPRODCHARGECODE) VALUES
            <foreach collection="list" item="item" index="index" separator="," >
                (
                #{item.id},
                #{item.provCode},
                #{item.payTag},
                #{item.groupCustomerNumber},
                #{item.groupCustomerName},
                #{item.ebossCustomerNumber},
                #{item.ebossCustomerName},
                #{item.addressProvCode},
                #{item.innerEcFlag},
                #{item.ecDepartmentName},
                #{item.ecCreatorName},
                #{item.ecCreatorTel},
                #{item.accountId},
                #{item.accountName},
                #{item.subsId},
                #{item.productClassName},
                #{item.productDetailName},
                #{item.productId},
                #{item.productName},
                #{item.mainContract},
                #{item.runDepartmentName},
                #{item.ictFlag},
                #{item.rateplanId},
                #{item.rateplanName},
                #{item.feeVal},
                #{item.taxRate},
                #{item.tax},
                #{item.feeNoTax},
                #{item.feeFlag},
                #{item.originalBillMonth},
                #{item.discountAmount},
                #{item.standardFee},
                #{item.settleFee},
                #{item.billingTerm},
                #{item.payTerm},
                #{item.settleItem},
                #{item.busiMode},
                #{item.chargeCode},
                #{item.chargeCodeName},
                #{item.cityCode},
                #{item.iniPrice},
                #{item.settlePrice},
                #{item.status},
                #{item.fileName},
                #{item.origNode},
                #{item.dbproductcode},
                #{item.dbprodchargecode}
                )
            </foreach>
    </insert>
    <update id="truncate" parameterType="string">
        TRUNCATE TABLE SYNC_INTERFACE_ESP_${acctMonth};
    </update>
    <update id="updateStatus23">
        UPDATE SYNC_INTERFACE_ESP_${acctMonth}
        set `status`='F023'
        where (prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')) or address_prov_code not in(select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1')))
          and `status` is null
          and file_name = #{fileName}
    </update>
    <select id="queryErrData" resultMap="BaseResultMap">
        SELECT *
        FROM SYNC_INTERFACE_ESP_${acctMonth}
        where
            <![CDATA[`status` <> '0'
        ]]>
        and file_name = #{fileName}
    </select>

</mapper>
