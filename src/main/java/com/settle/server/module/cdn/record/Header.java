package com.settle.server.module.cdn.record;

import java.time.LocalDateTime;

/**
 * 记录头包含有关记录的元数据
 *
 * <AUTHOR>
 */
public class Header {

    private Long number;
    private String source;
    private LocalDateTime creationDate;
    private boolean scanned;

    /**
     * @param number       数据源中的物理记录号（如果已定义）
     * @param source       从中读取此记录的数据源名称
     * @param creationDate 读取记录的日期
     */
    public Header(Long number, String source, LocalDateTime creationDate) {
        this.number = number;
        this.source = source;
        this.creationDate = creationDate;
    }

    /**
     * @return 数据源中的物理记录号（如果已定义）
     */
    public Long getNumber() {
        return number;
    }

    /**
     * @return 从中读取此记录的数据源名称
     */
    public String getSource() {
        return source;
    }

    /**
     * @return 读取记录的日期
     */
    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    /**
     * 如果记录是批量扫描操作的一部分，则返回 true
     *
     * @return 如果记录是批量扫描操作的一部分，则为 true
     */
    public boolean isScanned() {
        return scanned;
    }

    public void setScanned(boolean scanned) {
        this.scanned = scanned;
    }

    @Override
    public String toString() {
        return "number=" + number +
                ", source=\"" + source + '\"' +
                ", creationDate=\"" + creationDate + '\"' +
                ", scanned=\"" + scanned + '\"';
    }
}
