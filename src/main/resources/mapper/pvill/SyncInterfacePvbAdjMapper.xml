<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.pvbill.SyncInterfacePvbAdjMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.pvbill.SyncInterfacePvbAdj">
        <result column="BILL_MONTH" property="billMonth" />
        <result column="HIS_BILL_MONTH" property="hisBillMonth" />
        <result column="ADJUSTMENT_FEE_VAL" property="adjustmentFeeVal" />
        <result column="RESERVED1" property="reserved1" />
        <result column="RESERVED2" property="reserved2" />
        <result column="STATUS" property="status" />
        <result column="FILE_NAME" property="fileName" />
    </resultMap>
    <insert id="insertBatch">
        insert into SYNC_INTERFACE_PVB_ADJ
        (BILL_MONTH, HIS_BILL_MONTH, ADJUSTMENT_FEE_VAL, RESERVED1, RESERVED2, STATUS, FILE_NAME)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.billMonth,jdbcType=VARCHAR},
            #{item.hisBillMonth,jdbcType=VARCHAR},
            #{item.adjustmentFeeVal,jdbcType=BIGINT},
            #{item.reserved1,jdbcType=VARCHAR},
            #{item.reserved2,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.fileName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>
