package com.settle.server.module.esp.enums;

import lombok.Getter;

@Getter
public enum RespCode {
    ERR_21("21", "文件无法解析错误(文件级错误)"),
    ERR_22("22", "文件格式错误(文件级错误)"),
    ERR_23_N("23", "省代码错误"),
    ERR_24_N("24", "商品实例ID错误"),
    ERR_25_N("25", "基础口令实例ID错误"),
    ERR_99("99", "其它错误，由落地方自行解释"),
    ERR_99_F("99", "其它错误，由落地方自行解释"),

    ERR_Line("11", "记录级错误"),

    ERR_F904("F904", "文件无法解析错误(文件级错误)"),
    ERR_F904_2("F904", "文件格式错误(文件级错误)"),
    ERR_F009("F009", "重复返回码"),
    ERR_F022("F022", "PayTag不等于0应收时报错"),
    ERR_F023("F023", "省代码错误"),
    ERR_F024("F024", "产品信息错误"),
    ERR_F025("F025", "费用错误"),
    ERR_F026("F026", "费项错误"),
    ERR_F099("F099", "其它错误，由落地方自行解释。"),
    ERR_F099_F("F099", "其它错误，由落地方自行解释。"),

    ;

    private String code;

    private String desc;


    RespCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
