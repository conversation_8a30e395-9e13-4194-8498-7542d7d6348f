package com.settle.server;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableAsync
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class SettleServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SettleServerApplication.class, args);};

}
