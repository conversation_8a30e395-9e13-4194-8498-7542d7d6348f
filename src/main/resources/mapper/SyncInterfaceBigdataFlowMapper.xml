<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.SettleDataDao">
    <resultMap id="BaseResultMap" type="com.settle.server.entity.SettleDataStatistics">
        <result column="settleMonth" jdbcType="VARCHAR" property="settleMonth" />
        <result column="busName" jdbcType="VARCHAR" property="busName" />
        <result column="settleCount" jdbcType="VARCHAR" property="settleCount" />
        <result column="settleAmount" jdbcType="VARCHAR" property="settleAmount" />
    </resultMap>

    <resultMap id="SettleResultMap" type="com.settle.server.entity.SettleDataProvince">
        <result column="busType" jdbcType="VARCHAR" property="busType" />
        <result column="busName" jdbcType="VARCHAR" property="busName" />
        <result column="settleType" jdbcType="VARCHAR" property="settleType" />
        <result column="taxRate" jdbcType="VARCHAR" property="taxRate" />
        <result column="settleOutFee" jdbcType="VARCHAR" property="settleOutFee" />
        <result column="settleInFee" jdbcType="VARCHAR" property="settleInFee" />
        <result column="settleFee" jdbcType="VARCHAR" property="settleFee" />
        <result column="description" jdbcType="VARCHAR" property="description" />
    </resultMap>

    <select id="getProvinceDedicatedLineData" resultMap="BaseResultMap">
        select t.settlemonth as settleMonth,
               '省公司主办专线' as busName,
               count(distinct t.product_order_id) as settleCount,
               sum(t.settnotaxfee + t.setttaxfee) / 100 as settleAmount
        from stludr.rpt_bl_interprov t
        where t.settlemonth >= DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{acctMonth}, '01'), '%Y%m%d'), INTERVAL 12 MONTH), '%Y%m')
          and t.VERSION IN
              (SELECT MAX(VER.VERSION)
               FROM stludr.RVL_CONF_VERSION VER
               WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV'
                 AND VER.SETTLEMONTH = #{acctMonth})
          and t.pospec_code in ('01011301', '01011306')
        group by t.settlemonth
        order by t.settlemonth;
    </select>
    <select id="getProvincePayData" resultMap="BaseResultMap">
        select t.settlemonth as settleMonth,
               '省公司主办代付类' as busName,
               sum(t.settnotaxfee + setttaxfee) / 100 as settleAmount
        from stludr.rpt_bl_interprov t
        where t.settlemonth >= DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{acctMonth}, '01'), '%Y%m%d'), INTERVAL 12 MONTH), '%Y%m') 
          and t.VERSION IN
              (SELECT MAX(VER.VERSION)
               FROM stludr.RVL_CONF_VERSION VER
               WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV'
                 AND VER.SETTLEMONTH = #{acctMonth})
          and t.pospec_code in ('*********', '*********', '*********')
        group by t.settlemonth
        order by t.settlemonth;

    </select>
    <select id="getD208ProvinceSettleData" resultMap="SettleResultMap">
        with
            a as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            b as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov),
            ---省公司主办集团客户业务实收结算单（XX%）
            c as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            d as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov) ,
            ---省公司主办集团客户业务应收结算单（XX%）调账
            a1 as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            b1 as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov),
            ---省公司主办集团客户业务实收结算单（XX%）调账
            c1 as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            d1 as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='1'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov)
            (select '省公司主办集团客户业务' as busName,'应收' as settleType,a.taxrate as taxRate,sum(nvl(b.settlefee,0)) as settleOutFee,sum(nvl(a.settlefee,0)) as settleInFee,
                    '结算额（结入 - 结出）= '||round(sum(nvl(a.settlefee,0)-nvl(b.settlefee,0)),2) ||'（元）' as settleFee
             from a,b where a.inprov=b.outprov(+) and a.taxrate=b.taxrate(+)
                        and nvl(a.settlefee,0)-nvl(b.settlefee,0)>0 group by a.taxrate
             union all
             select '省公司主办集团客户业务' as busName,'调账应收' as settleType,a1.taxrate as taxRate,sum(nvl(b1.settlefee,0)) as settleOutFee,sum(nvl(a1.settlefee,0)) as settleInFee,
                    '调账结算额（结入 - 结出）= '||round(sum(nvl(a1.settlefee,0)-nvl(b1.settlefee,0)),2) ||'（元）' as settleFee
             from a1,b1 where a1.inprov=b1.outprov(+) and a1.taxrate=b1.taxrate(+)
             /*and nvl(a1.settlefee,0)-nvl(b1.settlefee,0)>0*/ group by a1.taxrate
             union all
             select '省公司主办集团客户业务' as busName,'实收' as settleType,c.taxrate as taxRate,sum(nvl(d.settlefee,0))as settleOutFee,sum(nvl(c.settlefee,0))  as settleInFee,
                    '结算额（结入 - 结出）= '||round(sum(nvl(c.settlefee,0)-nvl(d.settlefee,0)),2)||'（元）' as settleFee
             from c,d where c.inprov=d.outprov(+) and c.taxrate=d.taxrate(+)
                        and nvl(c.settlefee,0)-nvl(d.settlefee,0)>0 group by c.taxrate
             union all
             select '省公司主办集团客户业务' as busName,'调账实收' as settleType,c1.taxrate as taxRate,sum(nvl(d1.settlefee,0))as settleOutFee,sum(nvl(c1.settlefee,0)) as settleInFee,
                    '调账结算额（结入 - 结出）= '||round(sum(nvl(c1.settlefee,0)-nvl(d1.settlefee,0)),2) ||'（元）' as settleFee
             from c1,d1 where c1.inprov=d1.outprov(+) and c1.taxrate=d1.taxrate(+)
             /*and nvl(c1.settlefee,0)-nvl(d1.settlefee,0)>0*/ group by c1.taxrate);
    </select>

    <select id="getZqCmccArBLSettleData" resultMap="SettleResultMap">
        ---实收收入(XX%税率)_签约主体_201803
        select '3 政企公司财务报表，3.1 Mas' as busType,'实收收入' as settleType,t.signame as busName ,t.taxrate as taxRate,'本结算报表数据为'||t.signame||'签约主体数据，其中销账EC：' ||count(distinct t.customernumber)||'家，销账笔数：'||count(1)||'笔，涉及的业务：'||count(distinct t.pospec_code)||'个，销账的费项：' ||to_char(group_concat(distinct(t.charge_name)))||'
金额：'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' 元
后台数据核对结果一致。' as description from (
        select a.signame signame,a.taxrate taxrate, a.customernumber,a.pospec_code, a.charge_name,a.setttaxfee,a.settnotaxfee
        from stludr.rpt_cmcc_ar a where a.settlemonth=#{acctMonth}  and a.feetype= '0'
                                    and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_AR' AND VER.SETTLEMONTH = #{acctMonth})
        union all
        select a.signame,a.taxrate, a.customernumber,a.pospec_code, b.description charge_name,a.setttaxfee,a.settnotaxfee
        from stludr.rpt_cmcc_arowned a ,stludr.stl_charge_item_def b  where a.settlemonth=#{acctMonth}
                                                                        and a.feetype= '0'
                                                                        and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                                                          WHERE VER.TABLE_NAME = 'RPT_CMCC_AROWNED' AND VER.SETTLEMONTH = #{acctMonth})
                                                                        and a.charge_code = b.charge_item_ref and b.acct_month = #{acctMonth}) t
        group by t.signame,t.taxrate
        union all
        ---调帐实收收入(XX%税率)_签约主体_201803
        select '3 政企公司财务报表，3.1 Mas' as busType,'调账实收收入' as settleType,t.signame as busName,t.taxrate as taxRate,'本结算报表数据为'||t.signame||'签约主体调账数据，其中销账EC：' ||count(distinct t.customernumber)||'家，销账笔数：'||count(1)||'笔，涉及的业务：'||count(distinct t.pospec_code)||'个，销账的费项：' ||to_char(group_concat(distinct(t.charge_name)))||'
调账金额：'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' 元
后台数据核对结果一致。' as description from (
        select a.signame signame,a.taxrate taxrate, a.customernumber,a.pospec_code, a.charge_name,a.setttaxfee,a.settnotaxfee
        from stludr.rpt_cmcc_ar a where a.settlemonth=#{acctMonth}   and a.feetype != '0'
                                    and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_AR' AND VER.SETTLEMONTH = #{acctMonth})
        union all
        select a.signame,a.taxrate, a.customernumber,a.pospec_code, b.description charge_name,a.setttaxfee,a.settnotaxfee
        from stludr.rpt_cmcc_arowned a ,stludr.stl_charge_item_def b  where a.settlemonth=#{acctMonth}
                                                                        and a.feetype != '0'
                                                                        and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                                                          WHERE VER.TABLE_NAME = 'RPT_CMCC_AROWNED' AND VER.SETTLEMONTH = #{acctMonth})
                                                                        and a.charge_code = b.charge_item_ref and b.acct_month = #{acctMonth}) t
        group by t.signame,t.taxrate
        union all
        ---应收收入(XX%税率)_签约主体_201803
        select '3 政企公司财务报表，3.1 Mas' as busType,'应收收入'  as settleType,t.signame as busName,t.taxrate as taxRate,'本结算报表数据为'||t.signame||'签约主体数据，其中出账EC：' ||count(distinct t.customernumber)||'家，订购笔数：'||count(1)||'笔，涉及的业务：'||count(distinct t.pospec_code)||'个，出账费项：' ||to_char(group_concat(distinct(t.charge_name)))||'
金额：'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' 元
后台数据核对结果一致。' as description
        from (select a.signame,
                     a.taxrate,
                     a.customernumber,
                     a.pospec_code,
                     a.charge_name,
                     a.setttaxfee,
                     a.settnotaxfee
              from stludr.rpt_cmcc_bl a
              where a.settlemonth = #{acctMonth}  and a.feetype = '0'
                and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                  WHERE VER.TABLE_NAME = 'RPT_CMCC_BL' AND VER.SETTLEMONTH = #{acctMonth})
              union all
              select a.signame,
                     a.taxrate,
                     a.customernumber,
                     a.pospec_code,
                     b.description charge_name,
                     a.setttaxfee,
                     a.settnotaxfee from  stludr.RPT_CMCC_BLOWNED a,
                                          stludr.stl_charge_item_def b
              where a.settlemonth = #{acctMonth}  and a.feetype = '0'
                and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                  WHERE VER.TABLE_NAME = 'RPT_CMCC_BLOWNED' AND VER.SETTLEMONTH = #{acctMonth})
                and a.charge_code = b.charge_item_ref and b.acct_month=#{acctMonth}) t
        group by t.signame,t.taxrate
        union all
        ---调账应收收入(XX%税率)_签约主体_201803
        select '3 政企公司财务报表，3.1 Mas' as busType,'调账应收收入' as settleType,t.signame as busName,t.taxrate as taxRate,'本结算报表数据为'||t.signame||'签约主体调账数据，其中出账EC：' ||count(distinct t.customernumber)||'家，订购笔数：'||count(1)||'笔，涉及的业务：'||count(distinct t.pospec_code)||'个，出账费项：' ||to_char(group_concat(distinct(t.charge_name)))||'
调账金额：'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' 元
后台数据核对结果一致。' as description
        from (select a.signame,
                     a.taxrate,
                     a.customernumber,
                     a.pospec_code,
                     a.charge_name,
                     a.setttaxfee,
                     a.settnotaxfee
              from stludr.rpt_cmcc_bl a
              where a.settlemonth = #{acctMonth}  and a.feetype != '0'
                and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                  WHERE VER.TABLE_NAME = 'RPT_CMCC_BL' AND VER.SETTLEMONTH = #{acctMonth})
              union all
              select a.signame,
                     a.taxrate,
                     a.customernumber,
                     a.pospec_code,
                     b.description charge_name,
                     a.setttaxfee,
                     a.settnotaxfee from  stludr.RPT_CMCC_BLOWNED a,
                                          stludr.stl_charge_item_def b
              where a.settlemonth = #{acctMonth} and a.feetype != '0'
                and a.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                  WHERE VER.TABLE_NAME = 'RPT_CMCC_BLOWNED' AND VER.SETTLEMONTH = #{acctMonth})
                and a.charge_code = b.charge_item_ref and b.acct_month=#{acctMonth}) t
        group by t.signame,t.taxrate;
    </select>
    <select id="getCwCmccArBLSettleData" resultType="com.settle.server.entity.SettleDataProvince">
        ---政企分公司主办集团客户业务实收结算单（XX%）
        select '1 财务公司报表，1.1 Mas' as busType,'实收' as settleType,'政企分公司主办集团客户业务' as busName,t.taxrate,'本结算报表数据为集团公司与集团公司签约主体数据，其中销账EC：' ||count(distinct t.customernumber)||'家，销账笔数：'||count(1)||'笔，涉及的业务：'||count(distinct t.pospec_code)||'个，销账的费项：' ||to_char(group_concat(distinct(t.charge_name)))||'
本结算报表结算费用为：'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' 元' as description
        from stludr.rpt_cmcc_ar t where t.settlemonth=#{acctMonth}
                                    and t.inprov_code != '000'  and t.feetype = '0'
                                    and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_AR' AND VER.SETTLEMONTH = #{acctMonth})
        group by t.taxrate
        union all
---（调账）政企分公司主办集团客户业务实收结算单（XX%）
        select '1 财务公司报表，1.1 Mas' as busType,'调账实收' as settleType,'政企分公司主办集团客户业务' as busName,t.taxrate,'本结算报表数据为集团公司与集团公司签约主体调账数据，其中销账EC：' ||count(distinct t.customernumber)||'家，销账笔数：'||count(1)||'笔，涉及的业务：'||count(distinct t.pospec_code)||'个，销账的费项：' ||to_char(group_concat(distinct(t.charge_name)))||'
本结算报表结算调账费用为：'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' 元' as description
        from stludr.rpt_cmcc_ar t where t.settlemonth=#{acctMonth}
                                    and t.inprov_code != '000'   and t.feetype != '0'
                                    and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_AR' AND VER.SETTLEMONTH = #{acctMonth})
        group by t.taxrate
        union all
---政企分公司主办集团客户业务应收结算单(*)
        select '1 财务公司报表，1.1 Mas' as busType,'应收'  as settleType,'政企分公司主办集团客户业务' as busName,t.taxrate,'本结算报表数据为省结算数据，其中出账EC：' ||count(distinct t.customernumber)||'家，涉及'||count(distinct t.pospec_code)||'大类业务，结算对象为政企公司、省公司，报表通过结入、结出金额为：0；
其中政企四个签约主体结出费用=31个省公司结入费用'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' = '||round(sum(t.setttaxfee+t.settnotaxfee)/100,2) as description
        from stludr.rpt_cmcc_bl t where t.settlemonth=#{acctMonth}
                                    and t.inprov_code != '000'  and t.feetype = '0'
                                    and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_BL' AND VER.SETTLEMONTH = #{acctMonth})
        group by t.taxrate
        union all
---（调账）政企分公司主办集团客户业务应收结算单(*)
        select '1 财务公司报表，1.1 Mas' as busType,'调账应收'  as settleType,'政企分公司主办集团客户业务' as busName,t.taxrate,'本结算报表数据为省结算调账数据，其中出账EC：' ||count(distinct t.customernumber)||'家，涉及'||count(distinct t.pospec_code)||
'大类业务，结算对象为政企公司、省公司，报表通过结入、结出金额为：0；其中政企四个签约主体结出调账费用=31个省公司结入调账费用'||round(sum(t.setttaxfee+t.settnotaxfee)/100,2)||' = '||round(sum(t.setttaxfee+t.settnotaxfee)/100,2) as description
        from stludr.rpt_cmcc_bl t where t.settlemonth=#{acctMonth}
                                    and t.inprov_code != '000'    and t.feetype != '0'
                                    and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_BL' AND VER.SETTLEMONTH = #{acctMonth})
        group by t.taxrate;
    </select>
    <select id="getZqCmccBLSettleTotalData" resultType="java.lang.String">
        select '出账费用总额明细：
        '|| round(sum(b.settnotaxfee)/100,2)||' = ' ||round(sum(decode(signame,'有限公司',b.settnotaxfee/100,0)),2)||'(有限公司签约主体费用) + ' ||round(sum(decode(signame,'有限政企分公司',b.settnotaxfee/100,0)),2)||'(有限政企分公司签约主体费用) + ' ||round(sum(decode(signame,'集团公司',b.settnotaxfee/100,0)),2)||'(集团公司签约主体费用) + ' ||round(sum(decode(signame,'集团政企分公司',b.settnotaxfee/100,0)),2)||'(集团政企分公司签约主体费用)后台数核对结果一致。' ||
               '详单累计数据 = 账单累计数据 = 结算报表业务总收入数据
        '||round(sum(b.settnotaxfee)/100,2)||' = '||round(sum(b.settnotaxfee)/100,2)||' = '||round(sum(b.settnotaxfee)/100,2)
        from stludr.rpt_cmcc_bl b where b.settlemonth=#{acctMonth}
                                    and b.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                                      WHERE VER.TABLE_NAME = 'RPT_CMCC_BL' AND VER.SETTLEMONTH = #{acctMonth});
    </select>
    <select id="getSettleBLData" resultType="com.settle.server.entity.SettleDataStatistics">
        select t.settlemonth as settleMonth,sum(t.settnotaxfee+t.setttaxfee)/100 as settleAmount from stludr.rpt_cmcc_bl t
        where t.settlemonth>=DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{acctMonth}, '01'), '%Y%m%d'), INTERVAL 12 MONTH), '%Y%m')
          and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                            WHERE VER.TABLE_NAME = 'RPT_CMCC_BL' AND VER.SETTLEMONTH = #{acctMonth})
        group by t.settlemonth;
    </select>
    <select id="getSettleArData" resultType="com.settle.server.entity.SettleDataStatistics">
        select t.settlemonth  as settleMonth,sum(t.settnotaxfee+t.setttaxfee)/100 as settleAmount from stludr.rpt_cmcc_ar t
        where t.settlemonth>=DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{acctMonth}, '01'), '%Y%m%d'), INTERVAL 12 MONTH), '%Y%m')
          and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                            WHERE VER.TABLE_NAME = 'RPT_CMCC_AR' AND VER.SETTLEMONTH = #{acctMonth})
        group by t.settlemonth;
    </select>
    <select id="getSiarCarsvs" resultType="com.settle.server.entity.SettleDataStatistics">
        SELECT t.settlemonth,round(sum(t.SETTNOTAXFEE)/100,2) as settleAmount
        FROM stludr.RPT_SIAR_CARSVS t WHERE t.SOSPEC_CODE IN ('110901', '110903') and t.OUTPROV_CODE != '000'
        and t.settlemonth >=DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{acctMonth}, '01'), '%Y%m%d'), INTERVAL 12 MONTH), '%Y%m')
        and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
        WHERE VER.TABLE_NAME = 'RPT_SIAR_CARSVS' AND VER.SETTLEMONTH = #{acctMonth})
        GROUP BY t.settlemonth order by t.settlemonth;
    </select>
    <select id="getV101ProvinceSettleData" resultType="com.settle.server.entity.SettleDataProvince">
        with
            a as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            b as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov),
            ---省公司主办集团客户业务实收结算单（XX%）
            c as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            d as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype = '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov) ,
            ---省公司主办集团客户业务应收结算单（XX%）调账
            a1 as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            b1 as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_bl_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_bl_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_BL_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov),
            ---省公司主办集团客户业务实收结算单（XX%）调账
            c1 as (select taxrate,inprov,sum(settlefee) settlefee from
                (select t.taxrate,t.inprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.inprov
                 union all
                 select t.taxrate,t.inprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by taxrate,t.inprov) group by taxrate,inprov) ,
            d1 as (select taxrate,outprov,sum(settlefee) settlefee from
                (select t.taxrate,t.outprov,sum(t.setttaxfee+t.settnotaxfee)/100 settlefee from stludr.rpt_ar_interprov t
                 where t.settlemonth=#{acctMonth} and (t.pospec_code != '50051' or (t.pospec_code = '50051' and t.settle_mode='2'))
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_INTERPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                   and t.inprov != t.outprov group by t.taxrate,t.outprov
                 union all
                 select t.taxrate,t.outprov,sum(t.cpnottaxfee+t.cptaxfee)/100 from stludr.rpt_ar_cpprov t
                 where t.settlemonth=#{acctMonth}
                   and t.VERSION IN (SELECT MAX(VER.VERSION) FROM stludr.RVL_CONF_VERSION VER
                                     WHERE VER.TABLE_NAME = 'RPT_AR_CPPROV' AND VER.SETTLEMONTH = #{acctMonth})
                   and t.feetype != '0'
                 group by t.taxrate,t.outprov) group by taxrate,outprov)
            (select '省公司主办集团客户业务' as busName,'应收' as settleType,a.taxrate as taxRate,sum(nvl(b.settlefee,0)) as settleOutFee,sum(nvl(a.settlefee,0)) as settleInFee,
                    '结算额（结入 - 结出）= '||round(sum(nvl(a.settlefee,0)-nvl(b.settlefee,0)),2) ||'（元）' as settleFee
             from a,b where a.inprov=b.outprov(+) and a.taxrate=b.taxrate(+)
                        and nvl(a.settlefee,0)-nvl(b.settlefee,0)>0 group by a.taxrate
             union all
             select '省公司主办集团客户业务' as busName,'调账应收' as settleType,a1.taxrate as taxRate,sum(nvl(b1.settlefee,0)) as settleOutFee,sum(nvl(a1.settlefee,0)) as settleInFee,
                    '调账结算额（结入 - 结出）= '||round(sum(nvl(a1.settlefee,0)-nvl(b1.settlefee,0)),2) ||'（元）' as settleFee
             from a1,b1 where a1.inprov=b1.outprov(+) and a1.taxrate=b1.taxrate(+)
             /*and nvl(a1.settlefee,0)-nvl(b1.settlefee,0)>0*/ group by a1.taxrate
             union all
             select '省公司主办集团客户业务' as busName,'实收' as settleType,c.taxrate as taxRate,sum(nvl(d.settlefee,0))as settleOutFee,sum(nvl(c.settlefee,0))  as settleInFee,
                    '结算额（结入 - 结出）= '||round(sum(nvl(c.settlefee,0)-nvl(d.settlefee,0)),2)||'（元）' as settleFee
             from c,d where c.inprov=d.outprov(+) and c.taxrate=d.taxrate(+)
                        and nvl(c.settlefee,0)-nvl(d.settlefee,0)>0 group by c.taxrate
             union all
             select '省公司主办集团客户业务' as busName,'调账实收' as settleType,c1.taxrate as taxRate,sum(nvl(d1.settlefee,0))as settleOutFee,sum(nvl(c1.settlefee,0)) as settleInFee,
                    '调账结算额（结入 - 结出）= '||round(sum(nvl(c1.settlefee,0)-nvl(d1.settlefee,0)),2) ||'（元）' as settleFee
             from c1,d1 where c1.inprov=d1.outprov(+) and c1.taxrate=d1.taxrate(+)
             /*and nvl(c1.settlefee,0)-nvl(d1.settlefee,0)>0*/ group by c1.taxrate);
    </select>

</mapper>