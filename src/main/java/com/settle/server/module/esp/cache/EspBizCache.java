package com.settle.server.module.esp.cache;

import com.settle.server.module.esp.enums.BizCode;

import javax.xml.transform.Transformer;
import javax.xml.validation.Validator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/16
 * @since 1.0.0
 */
public class EspBizCache {

    private static final Map<BizCode, ReentrantLock> LOCK_MAP = new ConcurrentHashMap<>();

    private static final Map<BizCode, Validator> VALIDATOR_MAP = new HashMap<>();
    private static final Map<BizCode, ThreadLocal<Validator>> VALIDATOR_THREDLOCAL_MAP = new ConcurrentHashMap<>();
    private static final Map<BizCode, Transformer> TRANSFORMER_MAP = new HashMap<>();
    private static final Map<BizCode, ThreadLocal<Transformer>> TRANSFORMER_THREADLOCAL_MAP = new ConcurrentHashMap<>();

    public static ReentrantLock getLock(BizCode bizCode) {
        return LOCK_MAP.computeIfAbsent(bizCode, k -> new ReentrantLock());
    }

    public static Validator getValidator(BizCode bizCode) {
        return VALIDATOR_MAP.get(bizCode);
    }


    public static Validator getValidator2(BizCode bizCode) {
        ThreadLocal<Validator> threadLocal = VALIDATOR_THREDLOCAL_MAP.computeIfAbsent(bizCode, k -> new ThreadLocal<>());

        return threadLocal.get();
    }

    public static void putValidator2(BizCode bizCode, Validator validator) {
        ThreadLocal<Validator> threadLocal = VALIDATOR_THREDLOCAL_MAP.get(bizCode);
        threadLocal.set(validator);
    }



    public static void putValidator(BizCode bizCode, Validator validator) {
        VALIDATOR_MAP.put(bizCode, validator);
    }

    public static Transformer getTransformer(BizCode bizCode) {
        return TRANSFORMER_MAP.get(bizCode);
    }

    public static Transformer getTransformer2(BizCode bizCode) {
        ThreadLocal<Transformer> threadLocal = TRANSFORMER_THREADLOCAL_MAP.computeIfAbsent(bizCode, k -> new ThreadLocal<>());
        return threadLocal.get();
    }
    public static void putTransformer(BizCode bizCode, Transformer transformer) {
        TRANSFORMER_MAP.put(bizCode, transformer);
    }
    public static void putTransformer2(BizCode bizCode, Transformer transformer) {
        TRANSFORMER_THREADLOCAL_MAP.get(bizCode).set(transformer);
    }

}