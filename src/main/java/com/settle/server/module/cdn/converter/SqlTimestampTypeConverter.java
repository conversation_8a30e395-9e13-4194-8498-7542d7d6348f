package com.settle.server.module.cdn.converter;

import java.sql.Timestamp;

/**
 * java.sql.Timestamp 类型转换器
 * 将“yyyy-mm-dd hh:mm:ss[.f...]”格式的字符串时间戳转换为 {@link Timestamp} 类型
 *
 * <AUTHOR>
 */
public class SqlTimestampTypeConverter implements TypeConverter<String, Timestamp> {

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp convert(final String value) {
        return Timestamp.valueOf(value);
    }

}
