<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.settle.server.dao.stludr.SyncInterfaceMcNMGDao">
    <insert id="insertBatch">
        insert into SYNC_INTERFACE_MC_P2C_NMG_${acctMonth}
        (
        ID,PARENT_ID,SETTLEMENT_PARTY_IN, SETTLEMENT_PARTY_OUT_TYPE, SETTLEMENT_PARTY_OUT, SETTLEMENT_RATE,
        SETTLEMENT_TYPE, SETTLEMENT_AMOUNT, FILE_NAME, STATUS,
        PRD_SETTLE_DISVALUE)
        values
        <foreach collection="syncInterfaceMcNMGLst" item="item" index="index" separator=",">
            (#{item.id},#{item.parentId},#{item.settlementPartyIn}, #{item.settlementPartyOutType}, #{item.settlementPartyOut}, #{item.settlementRate},
            #{item.settlementType}, #{item.settlementAmount}, #{item.fileName}, #{item.status}, #{item.proSettleDisvalue})
        </foreach>
    </insert>
    <delete id="deleteByFileNameMonth">
        delete from stludr.SYNC_INTERFACE_MC_P2C_NMG_${acctMonth}
        <where>
            <if test="fileName != null and fileName != ''">
                AND file_name = #{fileName, jdbcType = VARCHAR}
            </if>
        </where>
    </delete>
</mapper>