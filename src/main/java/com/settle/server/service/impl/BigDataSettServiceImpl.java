package com.settle.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.Lists;
import com.settle.server.config.BigDataConfig;
import com.settle.server.config.BigSftpConfig;
import com.settle.server.dao.stludr.StlBigDataSettDao;
import com.settle.server.entity.StlBigDataSett;
import com.settle.server.entity.bigdata.BigDataFileLog;
import com.settle.server.enums.BigDataConstants;
import com.settle.server.enums.ProvinceCode;
import com.settle.server.module.bigdata.dto.BigDataDTO;
import com.settle.server.module.bigdata.enums.FileInterfaceEnum;
import com.settle.server.module.bigdata.service.BigDataFileLogService;
import com.settle.server.module.bigdata.service.BigDataFlowService;
import com.settle.server.module.esp.CustomerFileFilter;
import com.settle.server.service.BigDataSettService;
import com.settle.server.utils.EnumUtils;
import com.settle.server.utils.ListUtils;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPInputStream;

@Slf4j
@Service
public class BigDataSettServiceImpl implements BigDataSettService {

    @Autowired
    private StlBigDataSettDao stlBigDataSettDao;

    @Autowired
    private BigSftpConfig bigSftpConfig;

    @Autowired
    private BigDataConfig bigDataConfig;

    @Autowired
    private BigDataFileLogService logService;

    @Autowired
    private BigDataFlowService bigDataFlowService;

    public static final String amountRegx = "^[0-9]+(.[0-9]{1,2})?$";
    //下行流量数据是否为4位以内小数
    public static final String flowRegx = "^[-+]?\\d{1,4}(\\.\\d{1,4})?$";
    public static final String VERTICAL_BAR = "€€";

    @Override
    public void loadUpload(String acctMonth) {
        long startTime = System.currentTimeMillis();
        try {
            //	a_10000_HDO_75967_yyyymm_00_001.dat.gz
            String fileNameRegex = "a_10000_HDO_75983_" + acctMonth + "_00_001.dat-0.gz";
            Collection<File> listFiles = this.scanFileByReg(acctMonth, fileNameRegex);
            if (listFiles == null) return;
            // 获取本地目录中的所有文件
            String localDirectoryStr = bigSftpConfig.getTempPath() + File.separator + acctMonth;
            // 确保备份目录存在,移动原始压缩包到bak目录
            File localDirectoryStrInfo = new File(localDirectoryStr);
            if (!localDirectoryStrInfo.exists()) {
                localDirectoryStrInfo.mkdirs();
            }
            for (File file : listFiles) {
                //把压缩文件放入压缩备份目录中
                FileUtil.move(file, localDirectoryStrInfo, true);
            }
            // 获取目录下所有以.dat.gz结尾的文件
            File bigDataGzDataPath = new File(localDirectoryStr);
            File[] gzFiles = bigDataGzDataPath.listFiles();
            if (gzFiles == null || gzFiles.length == 0) {
                log.info("目录:{}, 无文件", bigDataGzDataPath);
                return;
            }
            File localDirectory = new File(localDirectoryStr);
            String bakDirFile = bigSftpConfig.getGzBakDir() + File.separator + acctMonth;
            for (File gzFile : gzFiles) {
                if (!gzFile.isFile()) {
                    continue;
                }
                String fileGzName = gzFile.getName();
                if (StringUtils.endsWith(fileGzName, ".gz")) {
                    // 解压文件 打开.gz文件进行读取
                    GZIPInputStream gzipInputStream = new GZIPInputStream(new FileInputStream(String.valueOf(gzFile.toPath())));
                    // 获取压缩文件名
                    String outputFileName = gzFile.getName().substring(0, gzFile.getName().lastIndexOf('.'));
                    File outputFile = new File(localDirectoryStr, outputFileName);
                    // 创建输出流并解压
                    FileOutputStream fileOutputStream = new FileOutputStream(outputFile);
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = gzipInputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, len);
                    }
                    fileOutputStream.close();
                    gzipInputStream.close();
                    // 确保备份目录存在,移动原始压缩包到bak目录
                    File bakDirFileInfo = new File(bakDirFile);
                    if (!bakDirFileInfo.exists()) {
                        bakDirFileInfo.mkdirs();
                    }
                    //把压缩文件放入压缩备份目录中
                    FileUtil.move(gzFile, bakDirFileInfo, true);

                }
            }
            //开始处理文件
            File[] localFiles = localDirectory.listFiles();
            List<StlBigDataSett> StlBigDataSettList = null;
            if (localFiles != null && localFiles.length > 0) {
                stlBigDataSettDao.deleteBySettleMonth(acctMonth, "", "");
                //读取文件数据并生成反馈文件
                StlBigDataSettList = this.readFileDataAndGenFeedBack(localFiles, acctMonth);
            }
            if (!CollectionUtils.isEmpty(StlBigDataSettList)) {
                log.info(" ******文件处理完成,条数:{}！", StlBigDataSettList.size());
                //入库
                if (StlBigDataSettList.size() > 500) {
                    List<List<StlBigDataSett>> lists = ListUtils.splitList(StlBigDataSettList, 500);
                    XxlJobHelper.log("共拆分成{}批处理", lists.size());
                    lists.forEach(list -> {
                        stlBigDataSettDao.batchInsert(list);
                    });
                } else {
                    stlBigDataSettDao.batchInsert(StlBigDataSettList);
                }
            }
            //上传成功，移动到备份文件夹
            this.moveFileToBak(localFiles, acctMonth);
            long endSendTime = System.currentTimeMillis();
            long runSendTime = (endSendTime - startTime) / 1000;
            log.info(" ******文件处理完成,总用时:{}S，结束！", runSendTime);
        } catch (Exception e) {
            log.error("处理文件失败", e);
            stlBigDataSettDao.deleteBySettleMonth(acctMonth, "", "");
            throw new RuntimeException(" deal fail!" + e.getMessage());
        }
    }

    @Override
    public void broadbandPreProcess(String acctMonth) {
        //1.扫描目录文件
        Collection<File> listFiles = this.scanFileByReg(acctMonth, FileInterfaceEnum.INTERFACE_75824);
        if (CollectionUtils.isEmpty(listFiles)) {
            return;
        }
        //2.预处理文件
        List<BigDataDTO> bigDataDTOS = this.preProcessFiles(acctMonth, listFiles, FileInterfaceEnum.INTERFACE_75824);

        bigDataFlowService.process(acctMonth,bigDataDTOS);
    }

    private List<BigDataDTO> preProcessFiles(String acctMonth, Collection<File> listFiles, FileInterfaceEnum interfaceEnum) {
        List<BigDataDTO> bigDataDTOS = Lists.newArrayList();
        for (File file : listFiles) {
            BigDataDTO dto = this.processOnce(acctMonth, interfaceEnum, file);
            if (dto == null) continue;
            bigDataDTOS.add(dto);
        }
        return bigDataDTOS;
    }

    private BigDataDTO processOnce(String acctMonth, FileInterfaceEnum interfaceEnum, File file) {
        BigDataDTO dto = new BigDataDTO();
        BigDataFileLog bigDataFileLog = initLog(acctMonth, file, interfaceEnum);

        dto.setAcctMonth(acctMonth);
        dto.setInterfaceCode(interfaceEnum.getInterfaceCode());
        dto.setSrcFile(file);
        dto.setFileLog(bigDataFileLog);

        BigDataConstants.ErrorMsg fileStatusEnum = BigDataConstants.ErrorMsg.F00;
        try {
            if (!file.isFile()||!file.exists()) {
                log.warn("文件[{}]不存在", file.getName());
                fileStatusEnum = BigDataConstants.ErrorMsg.F02;
                return dto;
            }
            try (InputStream inputStream = Files.newInputStream(file.toPath())) {
                //解压文件
                byte[] unedGzip = ZipUtil.unGzip(inputStream);
                File ungzipFile = new File(file.getParent(), file.getName().substring(0, file.getName().lastIndexOf('.')));
                FileUtil.writeBytes(unedGzip, ungzipFile);

                dto.setUngzipFile(ungzipFile);
            }
        } catch (Exception e) {
            fileStatusEnum = BigDataConstants.ErrorMsg.F03;
            log.error("文件[{}]处理失败", file.getName(), e);
        }finally {
            bigDataFileLog.setFileStatus(fileStatusEnum.getErrorCode());
            bigDataFileLog.setMessage(fileStatusEnum.getErrorDesc());
            this.saveLog(bigDataFileLog);
        }
        return dto;
    }

    private void saveLog(BigDataFileLog log) {
        logService.saveOne(log);
    }

    private BigDataFileLog initLog(String acctMonth, File file, FileInterfaceEnum interfaceEnum) {
        BigDataFileLog log = new BigDataFileLog();
        log.setId(IdUtil.getSnowflakeNextId());
        log.setAcctMonth(acctMonth);
        log.setFileName(file.getName());
        log.setInterfaceCode(interfaceEnum.getInterfaceCode());
        log.setCreatedTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());
        return log;
    }

    private Collection<File> scanFileByReg(String acctMonth, FileInterfaceEnum fileInterfaceEnum) {
        String fileNameRegex = "";
        String dataPath = "";
        switch (fileInterfaceEnum) {
            case INTERFACE_75824:
                fileNameRegex = bigDataConfig.getFileReg();
                dataPath = bigDataConfig.getDataPath();
                break;
            case INTERFACE_75983:
                dataPath = bigSftpConfig.getTempPath();
                fileNameRegex = "a_10000_HDO_75983_" + acctMonth + "_00_001.dat-0.gz";
                break;
            default:
                return Collections.emptyList();
        }
        FileUtil.mkdir(dataPath);
        File dataFile = FileUtil.file(dataPath);
        log.info("扫描本地目录：{}, 文件：{}", dataPath, fileNameRegex);
        Collection<File> listFiles = FileUtils.listFiles(dataFile, new CustomerFileFilter(fileNameRegex), null);
        log.info("文件接口：{}, 账期：{},下载到本地文件数量有：{}个。", fileInterfaceEnum.getInterfaceCode(),acctMonth, listFiles.size());
        if (CollectionUtils.isEmpty(listFiles)) {
            log.info("未获取到匹配的文件记录。目录：{}，文件格式：{}", dataFile.getAbsolutePath(), fileNameRegex);
            return Collections.emptyList();
        }
        return listFiles;
    }

    private Collection<File> scanFileByReg(String acctMonth, String fileNameRegex) {
        File dataFile = FileUtil.file(bigSftpConfig.getTempPath());
        log.info("bigData本地目录为：{}", bigSftpConfig.getTempPath());
        Collection<File> listFiles = FileUtils.listFiles(dataFile, new CustomerFileFilter(fileNameRegex), null);
        log.info("账期：{},下载到本地文件数量有：{}个。", acctMonth, listFiles.size());
        if (CollectionUtils.isEmpty(listFiles)) {
            log.info("未获取到匹配的文件记录。目录：{}，文件格式：{}", dataFile.getAbsolutePath(), fileNameRegex);
            return null;
        }
        return listFiles;
    }


    private List<StlBigDataSett> readFileDataAndGenFeedBack(File[] localFiles, String acctMonth) throws Exception {
        List<StlBigDataSett> StlBigDataSettList = new ArrayList<>();
        //每个账期内，所有文件里的流水号
        Set<String> compositeKey = new HashSet<>();
        for (File localFile : localFiles) {
            InputStream inputStream = new FileInputStream(localFile);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String line;
            StringBuilder filteredData = new StringBuilder();
            String filename = localFile.getName();
            while ((line = reader.readLine()) != null) {
                List<String> result = StrUtil.split(line, VERTICAL_BAR);
                if (CollectionUtils.isEmpty(result) || result.size() != 5) {
                    throw new RuntimeException(String.format("Verification Error fileName: %s, 该行不是5个参数, is %s, Line is %s", filename, result.size(), line));
                }
                this.checkFileData(result, filteredData, acctMonth, compositeKey, filename, StlBigDataSettList);
            }
            inputStream.close();
            reader.close();
            Path dir1 = Paths.get(bigSftpConfig.getFeedBackPath(), acctMonth);
            Files.createDirectories(dir1);
            String localFilePath = dir1 + File.separator + "RESP_" + filename; // 本地新文件路径
            // 写入筛选后的数据到新文件
            BufferedWriter writer = new BufferedWriter(new FileWriter(localFilePath));
            writer.write(String.valueOf(filteredData));
            writer.flush();
            writer.close();
        }
        return StlBigDataSettList;
    }


    private void checkFileData(List<String> result, StringBuilder filteredData, String acctMonth, Set<String> compositeKey, String filename, List<StlBigDataSett> StlBigDataSetts) {
        //流水号
//        String serialNumber = result.get(0);
        String provinceNameIn = result.get(0);
        String provinceName = result.get(1);
        String busType = result.get(2);
        String downDataTraffic = result.get(3);
        String settleMonth = result.get(4);
        String compositeStr = provinceNameIn + provinceName + busType + acctMonth;
        if (compositeKey.contains(compositeStr)) {
            filteredData.append("|").append(BigDataConstants.ErrorMsg.F01.getErrorCode()).append("\n");
            return;
        }
        compositeKey.add(compositeStr);
        Collection<String> provinceCodeAll = ProvinceCode.provinceCodeMap.values();
        //流入省编码
        if (!provinceCodeAll.contains(provinceNameIn)) {
            filteredData.append("|").append(BigDataConstants.ErrorMsg.F96.getErrorCode()).append("\n");
            return;
        }
        //流出省
        if (!provinceCodeAll.contains(provinceName)) {
            filteredData.append("|").append(BigDataConstants.ErrorMsg.F96.getErrorCode()).append("\n");
            return;
        }
        //业务类型
        if (!EnumUtils.isInclude(BigDataConstants.BusType.class, busType)) {
            filteredData.append("|").append(BigDataConstants.ErrorMsg.F08.getErrorCode()).append("\n");
            return;
        }
        //下行流量
        Pattern pattern = Pattern.compile(flowRegx);
        Matcher matcher = pattern.matcher(downDataTraffic);
        if (!matcher.matches()) {
            filteredData.append("|").append(BigDataConstants.ErrorMsg.F07.getErrorCode()).append("\n");
            return;
        }
        //统计日期（年月）
        if (settleMonth.length() != 6 || !settleMonth.equals(acctMonth)) {
            filteredData.append("|").append(BigDataConstants.ErrorMsg.F97.getErrorCode()).append("\n");
            return;
        }

        filteredData.append(BigDataConstants.ErrorMsg.F00.getErrorCode()).append("\n");
        //记录校验没问题的数据
        StlBigDataSett StlBigDataSett = new StlBigDataSett();
        StlBigDataSett.setProvinceNameIn(provinceNameIn);
        StlBigDataSett.setProvinceCodeIn(ProvinceCode.getCode(provinceNameIn));
        StlBigDataSett.setProvinceName(provinceName);
        StlBigDataSett.setProvinceCode(ProvinceCode.getCode(provinceName));
        StlBigDataSett.setBusType(busType);
        StlBigDataSett.setDownDataTraffic(downDataTraffic);
        StlBigDataSett.setSettleMonth(settleMonth);
        StlBigDataSett.setSourceFileName(filename);
        StlBigDataSetts.add(StlBigDataSett);

    }

    private void moveFileToBak(File[] localFiles, String acctMonth) throws IOException {
        Path backPath = Paths.get(bigSftpConfig.getBakPath(), acctMonth);
        Files.createDirectories(backPath);
        log.info("账期：{}，本地文件数量：{}，开始移动到备份目录:{}", acctMonth, localFiles.length, backPath);
        for (File localFile : localFiles) {
            FileUtil.move(localFile, backPath.toFile(), true);
        }
    }
}
