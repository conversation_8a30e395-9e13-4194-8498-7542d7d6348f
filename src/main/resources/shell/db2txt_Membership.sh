#!/bin/bash
export DB2TXT_HOME=/home/<USER>/workspace/Membership
export LOG_PATH=${DB2TXT_HOME}/log
export CONF_PATH=${DB2TXT_HOME}/conf

log_file=${LOG_PATH}/db2txt_Membership_`date +%Y%m%d`.log

function f_Print
{
    cur_ts=`date +%Y%m%d%H%M%S`
    echo "${cur_ts} - $1" >> $log_file
}

function f_init_config
{
    f_Print "f_init_config starts"

    if [ ! -f "${CONF_PATH}/db2txt.conf" ]; then
        f_Print "f_init_config - config file not found - ${CONF_PATH}/db2txt.conf"
        exit
    fi 

    . ${CONF_PATH}/db2txt.conf

    f_Print "f_init_config finishes"
}

function f_db2f_task
{
    f_Print "f_db2f_task starts"

    QResult=`sqlplus  -s ${a_db_str} << END
    whenever sqlerror exit sql.sqlcode       
    
    set head off
    set feed off  
    set echo off
    set termout off
    set trimout off
    
    insert into stlusers.file_db2f_task
select stlusers.SEQ_DB2F_ID.nextval, 'MEMBER', 'Settlesend', 'tst3', 'bboss',
       'select row_no, order_source, prodord_sku_num, prodist_sku_num, customer_num, member_num, resource_code, prov_nm, collect_time, substr(file_name, 1, length(file_name) - 4), province from bboss.stl_mem_interests where status = ''00'' and acct_month = ''${acct_month}'' order by to_number(row_no)',
       '/home/<USER>/workspace/Membership/work', 36, '', '', 999999, '成员权益领取文件下发到省公司', 'N', '${acct_month}', sysdate
    from dual;
    commit;
END`

    f_Print "Oracle Messages :[ $QResult ]"
    
    if [[  ! -z $QResult && -z ${QResult##*ORA-*}  && ! -z ${QResult##*ORA-28002*} && ! -z ${QResult##*ORA-28011*}   ]]
    then
        f_Print "Error: f_delete_table failed - Oracle Error "
        f_Print "$QResult"
        exit
    fi

    f_Print "f_db2f_task finishes"
}


f_Print "*****************************************************"
f_Print "            db2txt_Membership.sh starts              "
f_Print "*****************************************************"

##Reading input parameter
acct_month=$1
f_Print "acct_month=$acct_month"

##initializing configuration file
f_init_config
if (( $? != 0)); then
   f_Print "f_init_config failure. Exit now." 
fi

##Inserting db2f task
f_db2f_task
if (( $? != 0)); then
   f_Print "f_db2f_task failure. Exit now."
fi

##Starting db2f
iProc=0
iProc=`ps -ef|grep db2txt|grep 'MEMBER$'|wc -l`
if [ ${iProc} -eq 0 ]
then
        nohup /home/<USER>/bin/db2txt -m=db2txt -t=MEMBER &
fi

##Waiting for file(s) to be generated
SIZE1=1
SIZE2=0
sleep 1

while [[ 1 -ne 2 ]]
do
  SIZE1=SIZE2
  SIZE2=`du -cb ${DB2TXT_HOME}/work/MembershipInterests_????????_???.csv|tail -n 1|awk '{print $1}'`
  
  if [[ ${SIZE2} -eq 0 ]]
  then
    echo "No MembershipInterests file generated."
    break
  else
  if [[ ${SIZE1} -eq ${SIZE2} ]]
  then
    echo "MembershipInterests files are finished."
    mv ${DB2TXT_HOME}/work/MembershipInterests_????????_???.csv ${DB2TXT_HOME}/outgoing
    break
  else
    sleep 3
  fi
  fi
done

##Stopping db2f
ps -ef | grep -v grep | grep MEMBER | awk '{print $2}' | xargs kill
