package com.settle.server.module.esp.dto;

import com.settle.server.module.esp.enums.RespCode;
import lombok.Data;

import java.io.File;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/16
 * @since 1.0.0
 */
@Data
public class ResultDate {

    private String code;
    private String message;
    private String fileStatus;
    private File txtFile;

    private ResultDate() {
    }

    public static ResultDate success() {
        ResultDate resultDate = new ResultDate();
        resultDate.setCode("0");
        resultDate.setMessage("success");
        return resultDate;
    }

    public static ResultDate success(File txtFile) {
        ResultDate resultDate = new ResultDate();
        resultDate.setCode("0");
        resultDate.setMessage("success");
        resultDate.setTxtFile(txtFile);
        return resultDate;
    }


    public static ResultDate fail(String message) {
        ResultDate resultDate = new ResultDate();
        resultDate.setCode("99");
        resultDate.setMessage(message);
        resultDate.setFileStatus("1");
        return resultDate;
    }

    public static ResultDate fail(RespCode respCode, String message) {
        ResultDate resultDate = new ResultDate();
        resultDate.setCode(respCode.getCode());
        resultDate.setMessage(message);
        if (respCode == RespCode.ERR_99_F || respCode == RespCode.ERR_F099_F ||
                respCode == RespCode.ERR_21 || respCode == RespCode.ERR_22 ||
                respCode == RespCode.ERR_F904 || respCode == RespCode.ERR_F904_2) {
            resultDate.setFileStatus("1");
        } else {
            resultDate.setFileStatus("2");
        }
        return resultDate;
    }

    public boolean isSuccess() {
        return "0".equals(code);
    }
}