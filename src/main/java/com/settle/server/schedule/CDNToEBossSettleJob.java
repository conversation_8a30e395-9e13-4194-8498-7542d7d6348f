package com.settle.server.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.module.cdn.CDNToEBossHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.settle.server.module.cdn.enums.ActionType.CSV2DB;
import static com.settle.server.module.cdn.enums.ActionType.DB2CSV;

@Component
@Slf4j
public class CDNToEBossSettleJob {

    @Autowired
    private CDNToEBossHandler handler;

    /**
     * CDN收入结算同步EBOSS
     * @return
     */
    @XxlJob("CDNToEBossSettleJobHandler")
    public ReturnT<String> CDNToEBossSettleJobHandler()  {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("CDNToEBoss开始，接受参数：{}", param);
        ReturnT<String> returnT;
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            }
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            handler.handle(acctMonth, DB2CSV);
            returnT =  ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("CDNToEBoss执行失败", e);
            String message = ExceptionUtils.getStackTrace(e);
            XxlJobHelper.log("CDNToEBoss执行失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    /**
     * EBOSS客户产品应收信息同步结算
     * @return
     */
    @XxlJob("EBossToSettleJobHandler")
    public ReturnT<String> EBossToSettleJobHandler()  {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("EBossToSettle开始，接受参数：{}", param);
        ReturnT<String> returnT;
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                LocalDateTime now = LocalDateTime.now();
                // 上一个月
                LocalDateTime localDateTime = now.minusMonths(1);
                // 格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth = localDateTime.format(formatter);
            }
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            handler.handle(acctMonth,CSV2DB);
            returnT =  ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("EBossToSettle执行失败", e);
            String message = ExceptionUtils.getStackTrace(e);
            XxlJobHelper.log("EBossToSettle执行失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

}
