package com.settle.server.controller;

import com.settle.server.service.IotCardSettService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 物联网卡平台
 * 《中国移动集客大厅与政企统一融合支撑网关（OneSupport）接口规范—业务平台V2.1-20231024.doc》新增6.13月结算数据文件接口。
 */
@Slf4j
@RestController
@RequestMapping("/iot")
public class IotCardSettController {

    @Autowired
    private IotCardSettService iotCardSettService;

    /**
     * @param acctMonth 出账账期，如202309
     * @return
     */
    @GetMapping(value = "/cardUpload")
    public String cardUpload(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            iotCardSettService.cardUpload(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("IotCardSettController --->save error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }


}
