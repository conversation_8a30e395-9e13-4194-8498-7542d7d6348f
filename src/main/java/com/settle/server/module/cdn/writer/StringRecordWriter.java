package com.settle.server.module.cdn.writer;


import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;
import com.settle.server.utils.Utils;

import java.io.StringWriter;

/**
 * 通过调用其 <code>toString</code> 方法将记录的有效负载写入 {@link StringWriter}
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class StringRecordWriter<P> implements RecordWriter<P> {

    private StringWriter stringWriter;

    /**
     * 创建一个新的 {@link StringWriter}
     *
     * @param stringWriter 将字符串写入
     */
    public StringRecordWriter(final StringWriter stringWriter) {
        Utils.checkNotNull(stringWriter, "string writer");
        this.stringWriter = stringWriter;
    }

    @Override
    public void writeRecords(Batch<P> batch) {
        for (Record<P> record : batch) {
            stringWriter.write(record.getPayload().toString());
            stringWriter.write(Utils.LINE_SEPARATOR);
        }
        stringWriter.flush();
    }

    @Override
    public void close() throws Exception {
        stringWriter.close();
    }
}
