package com.settle.server.Plugin;

import com.deepoove.poi.policy.AbstractRenderPolicy;
import com.deepoove.poi.render.RenderContext;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.settle.server.entity.SettleDataStatistics;
import org.apache.poi.util.Units;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xwpf.usermodel.XWPFChart;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class CustomChartRenderPolicy extends AbstractRenderPolicy<Object> {


    @Override
    protected void afterRender(RenderContext<Object> context) {
        // 清空标签
        clearPlaceholder(context, false);
    }

    @Override
    public void doRender(RenderContext<Object> context) throws Exception {
        Object data = context.getData();
        List<SettleDataStatistics> provinceDedicatedLineData = new ArrayList<>();
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            if (!list.isEmpty() && list.get(0) instanceof SettleDataStatistics) {
                provinceDedicatedLineData = (List<SettleDataStatistics>) data;
            }
        }
        //List<SettleDataStatistics> settleDataStatisticsList = (List<SettleDataStatistics>) data;

        XWPFRun run = context.getRun();
        NiceXWPFDocument xwpfDocument = context.getXWPFDocument();
        //在标签位置创建chart图表对象
        XWPFChart chart = xwpfDocument.createChart(run, 15 * Units.EMU_PER_CENTIMETER, 10 * Units.EMU_PER_CENTIMETER);
        //图表相关设置
        //图表标题
        chart.setTitleText("省公司主办专线业务增长图");
        //图例是否覆盖标题
        chart.setTitleOverlay(false);

        //图例设置
        XDDFChartLegend legend = chart.getOrAddLegend();
        //图例位置:上下左右
        legend.setPosition(LegendPosition.TOP);

        /*双Y轴*/

        // X轴
        XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        bottomAxis.setTitle("结算账期");
        // bottomAxis.setVisible(false);// 隐藏X轴

        // 左Y轴
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
//        // 左Y轴和X轴交叉点在X轴0点位置
//        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
//        leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        leftAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(leftAxis);
        // 设置左Y轴最大值
//        DecimalFormat decimalFormat = new DecimalFormat("#.##");
//        Optional<SettleDataStatistics> min = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        Optional<SettleDataStatistics> max = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleAmount));
//        leftAxis.setMinimum(0);
//        leftAxis.setMaximum(max.get().getSettleAmount());
//                leftAxis.setMajorUnit(1);
        // leftAxis.setVisible(false);// 隐藏Y轴
        leftAxis.setTitle("元");

        // 右Y轴
        XDDFValueAxis rightAxis = chart.createValueAxis(AxisPosition.RIGHT);
        // 右Y轴和X轴交叉点在X轴最大值位置
        rightAxis.setCrosses(AxisCrosses.MAX);
        rightAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
        // 构建坐标轴
        rightAxis.crossAxis(bottomAxis);
        bottomAxis.crossAxis(rightAxis);
//        Optional<SettleDataStatistics> rightmin = provinceDedicatedLineData.stream().min(Comparator.comparingDouble(SettleDataStatistics::getSettleCount));
//        Optional<SettleDataStatistics> rightmax = provinceDedicatedLineData.stream().max(Comparator.comparingDouble(SettleDataStatistics::getSettleCount));
//        rightAxis.setMinimum(0);
//        rightAxis.setMaximum(rightmax.get().getSettleCount());
        // rightAxis.setVisible(false);// 隐藏Y轴
//        rightAxis.setTitle("右Y轴标题");
//        rightAxis.setMajorUnit(1000);


        // CellRangeAddress(起始行号，终止行号， 起始列号，终止列号）
        // X轴数据，单元格范围位置[0, 0]到[0, 6]
//        XDDFDataSource<String> xdatas = XDDFDataSourcesFactory.fromStringCellRange(sheet, new CellRangeAddress(size +3, size+size1+2, 0, 0));
        XDDFCategoryDataSource xdatas = XDDFDataSourcesFactory.fromArray(provinceDedicatedLineData.stream().map(SettleDataStatistics::getSettleMonth).toArray(String[]::new));
//         XDDFCategoryDataSource xdatas = XDDFDataSourcesFactory.fromArray(new String[]{"202305","202306","202307"});
        // 左Y轴数据，单元格范围位置[1, 0]到[1, 6]
//        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(size +3, size+size1+2, 2, 2));
        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromArray(provinceDedicatedLineData.stream().map(SettleDataStatistics::getSettleAmount).toArray(Double[]::new));
//        XDDFNumericalDataSource<Double> leftYdatas = XDDFDataSourcesFactory.fromArray(new Double[]{172454361.02, 172532880.18, 172712834.01});
        // 右Y轴数据，单元格范围位置[2, 0]到[2, 6]
//        XDDFNumericalDataSource<Double> rightYdatas = XDDFDataSourcesFactory.fromNumericCellRange(sheet, new CellRangeAddress(size +3, size+size1+2, 1, 1));
         XDDFNumericalDataSource<Long> rightYdatas = XDDFDataSourcesFactory.fromArray(provinceDedicatedLineData.stream().map(SettleDataStatistics::getSettleCount).toArray(Long[]::new));
//        XDDFNumericalDataSource<Integer> rightYdatas = XDDFDataSourcesFactory.fromArray(new Integer[]{17158, 17245, 17280});

        // LINE：折线图，
        XDDFLineChartData line1 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series1 = (XDDFLineChartData.Series) line1.addSeries(xdatas, rightYdatas);
        // 折线图例标题
        series1.setTitle("条数", null);
        // 线条样式:true平滑曲线,false折线
        series1.setSmooth(false);
        // 设置标记大小
        series1.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series1.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line1);

        // LINE：折线图，
        XDDFLineChartData line2 = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
        // 图表加载数据，折线1
        XDDFLineChartData.Series series2 = (XDDFLineChartData.Series) line2.addSeries(xdatas, leftYdatas);
        // 折线图例标题
        series2.setTitle("结算金额", null);
        // 直线
        series2.setSmooth(false);
        // 设置标记大小
        series2.setMarkerSize((short) 6);
        // 设置标记样式，星星
        series2.setMarkerStyle(MarkerStyle.SQUARE);
        // 绘制
        chart.plot(line2);

        // 填充与线条-标记-填充-依数据点着色（取消勾选）
        chart.getCTChart().getPlotArea().getLineChartArray(0).addNewVaryColors().setVal(false);


    }
}
