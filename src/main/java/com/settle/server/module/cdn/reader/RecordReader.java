package com.settle.server.module.cdn.reader;


import com.settle.server.module.cdn.record.Record;

/**
 * 记录读取器接口
 * 用于从数据源<strong>顺序</strong>读取记录
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public interface RecordReader<P> {

    /**
     * 打开
     *
     * @throws Exception 打开过程中发生错误
     */
    default void open() throws Exception {
        // no-op
    }

    /**
     * 从数据源读取下一条记录
     *
     * @return 来自数据源的下一条记录或 null 如果到达数据源的末尾
     * @throws Exception 如果在读取下一条记录时发生错误
     */
    Record<P> readRecord() throws Exception;

    /**
     * 关闭
     *
     * @throws Exception 关闭过程中发生错误
     */
    default void close() throws Exception {
        // no-op
    }

}
