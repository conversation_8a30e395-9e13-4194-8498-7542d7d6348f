package com.settle.server.module.cdn.converter;

import com.settle.server.utils.Utils;

import java.util.concurrent.atomic.AtomicLong;

/**
 * AtomicLong 类型转换器
 * 不接受 {@code null} 或空字符串
 *
 * <AUTHOR>
 */
public class AtomicLongTypeConverter implements TypeConverter<String, AtomicLong> {

    /**
     * {@inheritDoc}
     */
    @Override
    public AtomicLong convert(final String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        return new AtomicLong(Long.parseLong(value));
    }

}
