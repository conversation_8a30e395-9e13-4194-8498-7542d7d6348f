package com.settle.server.controller;

import com.settle.server.service.CmiotSettService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Classname CmiotSettController
 * @Description TODO
 * @Date 2023/10/17 14:32
 * <AUTHOR>
 * @Version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/cmiot")
public class CmiotSettController {

    @Autowired
    private CmiotSettService cmiotSettService;

    @GetMapping(value = "/save")
    public String save(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            cmiotSettService.save(acctMonth);
            return "success";
        } catch (Exception e) {
            log.error("CmiotSettController --->save error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }

}
