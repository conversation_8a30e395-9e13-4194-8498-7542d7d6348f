package com.settle.server.xmlTransform.text2xml;

import com.settle.server.utils.CommandUtil;
import com.settle.server.utils.FileUtil;
import com.settle.server.xmlTransform.XMLTransformDBUtil;
import com.settle.server.xmlTransform.resp.BillList;
import com.settle.server.xmlTransform.resp.ErrorRecord;
import com.settle.server.xmlTransform.resp.ErrorRecords;
import com.settle.server.xmlTransform.text2xml.config.Entry;
import lombok.extern.slf4j.Slf4j;
import org.exolab.castor.xml.ValidationException;
import org.exolab.castor.xml.MarshalException;

//import java.io.FileOutputStream;
import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.File;
import java.io.FileWriter;
import java.io.OutputStreamWriter;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.Calendar;

@Slf4j
public class T2XEngine implements Runnable {

	private Entry _entry;

	public T2XEngine(Entry entry) {
		_entry = entry;
	}

	private String getCurrentDate() {
		SimpleDateFormat myFmt = new SimpleDateFormat("yyyyMMddhhmmss");
		Date date = new Date(Calendar.getInstance().getTimeInMillis());
		return myFmt.format(date);
	}

	private void dowithEFile(File srcFile, File tmpFile) {
		BufferedReader reader = null;
		FileWriter xmlfile = null;
		try {
			try {
				reader = new BufferedReader(new FileReader(srcFile));
				String tempString = null;
				BillList rspXml = new BillList();
				rspXml.setOrg_FileName(srcFile.getName().substring(1));
				rspXml.setResp_Date(getCurrentDate());
				rspXml.setFileStatus("2");
				ErrorRecords records = new ErrorRecords();
				while ((tempString = reader.readLine()) != null) {
					ErrorRecord erRecord = new ErrorRecord();
					// erRecord.setErrorCode(tempString.substring(0,
					// tempString.indexOf(",")));
					erRecord.setErrorCode(tempString.substring(0, 4));

					if (tempString.length() > 256) {
						erRecord.setErrorNode(tempString.substring(0, 256));
					} else {
						erRecord.setErrorNode(tempString);
					}

					records.addErrorRecord(erRecord);
				}

				rspXml.setErrorRecords(records);
				try {
					xmlfile = new FileWriter(tmpFile);
					rspXml.marshal(xmlfile);
				} finally {
					xmlfile.close();
				}
			} finally {
				reader.close();
			}
		} catch (ValidationException ex) {
			log.error("dowithEFile: " + ex.getMessage());
		} catch (MarshalException ex) {
			log.error("dowithEFile: " + ex.getMessage());
		} catch (FileNotFoundException ex) {
			log.error("dowithEFile: " + ex.getMessage());
		} catch (IOException ex) {
			log.error("dowithEFile: " + ex.getMessage());
		}
	}

	private void dowithFFile(File srcFile, File tmpFile) {
		BufferedReader reader = null;
		FileWriter xmlfile = null;
		try {
			try {
				reader = new BufferedReader(new FileReader(srcFile));
				String tempString = reader.readLine();
				
				/*Added by Nova Kitt for translating F000 and F001 ErrorCodes. 20180206*/
				/*String code = tempString.substring(0, 4);
				if (code.equals("F000"))
					code = "F904";
				else if (code.equals("F001"))
					code = "F099";*/
				
				BillList rspXml = new BillList();
				rspXml.setOrg_FileName(srcFile.getName().substring(1));
				rspXml.setResp_Date(getCurrentDate());
				rspXml.setFileStatus("1");
				rspXml.setErrorCode(tempString.substring(0, 4));

				if (tempString.length() > 256) {
					rspXml.setErrorNode(tempString.substring(0, 256));
				} else {
					rspXml.setErrorNode(tempString);
				}
				try {
					xmlfile = new FileWriter(tmpFile);
					rspXml.marshal(xmlfile);
				} finally {
					xmlfile.close();
				}
			} finally {
				reader.close();
			}
		} catch (ValidationException ex) {
			log.error("dowithFFile: " + ex.getMessage());
		} catch (MarshalException ex) {
			log.error("dowithFFile: " + ex.getMessage());
		} catch (FileNotFoundException ex) {
			log.error("dowithFFile: " + ex.getMessage());
		} catch (IOException ex) {
			log.error("dowithFFile: " + ex.getMessage());
		}
	}

	private void dowithCFile(File srcFile, File tmpFile) {
		try {
			FileWriter xmlfile = null;
			try {
				BillList rspXml = new BillList();
				rspXml.setOrg_FileName(srcFile.getName().substring(1));
				rspXml.setResp_Date(getCurrentDate());
				rspXml.setFileStatus("0");
				xmlfile = new FileWriter(tmpFile);
				rspXml.marshal(xmlfile);
			} finally {
				xmlfile.close();
			}
		} catch (ValidationException ex) {
			log.error("dowithCFile: " + ex.getMessage());
		} catch (MarshalException ex) {
			log.error("dowithCFile: " + ex.getMessage());
		} catch (FileNotFoundException ex) {
			log.error("dowithCFile: " + ex.getMessage());
		} catch (IOException ex) {
			log.error("dowithCFile: " + ex.getMessage());
		}
	}

	public void run() {
		while (true) {
			try {
				File[] files = FileUtil.getFilesInFolder(_entry.getIn(), _entry
						.getRegexp());
				if (files != null && files.length > 0) {
					for (int i = 0; i < files.length; i++) {
						File srcFile = files[i];
						File destFile = new File(_entry.getOut()
								+ FileUtil.getRespFileName(srcFile.getName()));
						File tmpFile = FileUtil.createTempFile(destFile);
						if (tmpFile == null)
							continue;

						if (!srcFile.isFile()) {
							log.error(
									FileUtil.comb(_entry.getName(), _entry
											.getType())
											+ " : "
											+ srcFile.getName()
											+ " is missed.");
							continue;
						}

						boolean ret = true;
						if ((srcFile.getName().startsWith("E"))) {
							if (srcFile.length() == 0)
								dowithCFile(srcFile, tmpFile);
							else
								dowithEFile(srcFile, tmpFile);

						} else if (srcFile.getName().startsWith("F")) {
							dowithFFile(srcFile, tmpFile);
						} else if (srcFile.getName().startsWith("C")) {
							dowithCFile(srcFile, tmpFile);
						} else {
							ret = false;
						}

						if (ret) {
							//logRecord(srcFile, destFile.getName(), destFile.length());
//							FileUtil.moveFile(tmpFile, destFile);
							
							int len = (int)tmpFile.length();
							FileInputStream fis = new FileInputStream(tmpFile);
							byte[] buf = new byte[len];
							fis.read(buf);
							fis.close();
							System.out.println("buffer: " + new String(buf));
							String sContent = new String(buf, "gb2312");
							System.out.println("sContent: " + sContent);
							
							File tmpFile2 =FileUtil.createTempFile2(destFile);
							FileOutputStream fos = new FileOutputStream(tmpFile2);
							
							OutputStreamWriter out = new OutputStreamWriter(fos, "utf-8");
				 			BufferedWriter bw = new BufferedWriter(out);
				 			bw.write(sContent);
						    bw.flush();
						    bw.close();
						    
						    FileUtil.moveFile(tmpFile2, destFile);
						    tmpFile.delete();
						    
						    logRecord(srcFile, destFile.getName(), destFile.length());
						    
							FileUtil.moveFile(srcFile, _entry.getBack());
							log.info(
									FileUtil.comb(_entry.getName(), _entry
											.getType())
											+ " : "
											+ srcFile.getName()
											+ " is processed.");
						}
					}
				}

				Thread.sleep(10 * 1000); // ����10��
			} catch (Exception ex) {
				log.error("Sleep Error: " + ex.getMessage());
				try {
					Thread.sleep(10 * 1000); // ����10��
				} catch (InterruptedException ex1) {
					log.error(
							"Sleep Error again : " + ex1.getMessage());
				}
			}
		}
	}

	private void logRecord(File src, String filename, long fileByte) {

		String errorCode = null;
		String errorMessage = null;
		String tempString;

		Long rawCount = Long.valueOf(-1);

		String srcName = src.getName();
		String biztype = srcName.charAt(11) + srcName.substring(13, 17);
		if (srcName.startsWith("E")) {
			if (src.length() == 0) {
				rawCount = new Long(0);
			} else {
				rawCount = CommandUtil.runCommand(src.getPath());
			}
		} else if (srcName.startsWith("F")) {
			BufferedReader reader = null;
			try {
				try {
					reader = new BufferedReader(new FileReader(src));
					tempString = reader.readLine();
					errorCode = tempString.substring(0, 4);

					if (tempString.length() > 256) {
						errorMessage = tempString.substring(0, 256);
					} else {
						errorMessage = tempString;
					}

				} finally {
					reader.close();
				}
			} catch (FileNotFoundException e) {
				// TODO Auto-generated catch block
				log.error(e.getMessage());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				log.error(e.getMessage());
			}
		}

		XMLTransformDBUtil.getInstance().saveX2TResult(biztype.toUpperCase(), filename,
				rawCount, errorCode, errorMessage, 0, fileByte);
	}
}
