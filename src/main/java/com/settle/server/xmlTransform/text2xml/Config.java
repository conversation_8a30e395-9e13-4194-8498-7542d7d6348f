package com.settle.server.xmlTransform.text2xml;

import java.io.*;
import java.util.Properties;

import com.settle.server.utils.FileUtil;
import com.settle.server.xmlTransform.XMLTransformDBUtil;
import com.settle.server.xmlTransform.text2xml.config.*;
import lombok.extern.slf4j.Slf4j;
import org.exolab.castor.xml.*;


@Slf4j
public class Config
{
    private static Basepath[]	_basepathArray;
	private static Entry[]		_entryArray;
	
	public static int getEntryCount()
	{
		return _entryArray.length;
	}
	
	public static Entry getEntry(int index)
	{
		return _entryArray[index];
	}
	
	private static Basepath getBasepath(String name)
	{
		for (int i = 0; i < _basepathArray.length; i++) 
		{
			if (_basepathArray[i].getName().equals(name))
				return _basepathArray[i];
		}
		
		return null;
	}
	
	private static void replaceEnv(Basepath node)
	{
		node.setIn(FileUtil.getEnvPath(node.getIn()));
		node.setOut(FileUtil.getEnvPath(node.getOut()));
		node.setBack(FileUtil.getEnvPath(node.getBack()));
	}

	private static void appendEndSep(Basepath node)
	{
		node.setIn(FileUtil.addEndSep(node.getIn()));
		node.setOut(FileUtil.addEndSep(node.getOut()));
		node.setBack(FileUtil.addEndSep(node.getBack()));
	}

	private static boolean checkBasepath(Basepath node)
	{
		if (!FileUtil.checkPath(node.getIn())) 
			return false;

		if (!FileUtil.checkPath(node.getOut())) 
			return false;

		if (!FileUtil.checkPath(node.getBack())) 
			return false;

		return true;
	}

	private static void addBasepath(Entry entry, Basepath parent)
	{
		entry.setIn(FileUtil.addEndSep(parent.getIn() + entry.getIn()));
		entry.setOut(FileUtil.addEndSep(parent.getOut() + entry.getOut()));
		entry.setBack(FileUtil.addEndSep(parent.getBack() + entry.getBack()));
	}
	
	private static boolean checkEntry(Entry node)
	{
		if (!FileUtil.checkPath(node.getIn())) 
			return false;

		if (!FileUtil.checkPath(node.getOut())) 
			return false;

		if (!FileUtil.checkPath(node.getBack())) 
			return false;

		return true;
	}

	public static boolean init(String cfgFile)
	{
		try 
		{
			FileReader xmlfile = null;
			FileInputStream istream = null;
			try
			{
			xmlfile = new FileReader(cfgFile);
			Configure cfgNode = (Configure) Configure.unmarshal(xmlfile);

			if (!cfgNode.isValid())
			{
				System.out.println("Invalid config file: " + cfgFile);
				return false;
			}
			
			int i;

			Paths pathsNode = cfgNode.getPaths();

			//get log4j path
			String logFile = pathsNode.getLog4j();
			String dbConf = pathsNode.getDBConf();
			logFile = FileUtil.getEnvPath(logFile);
			dbConf = FileUtil.getEnvPath(dbConf);			

			if (!FileUtil.isFile(logFile))
			{
				System.out.println("Log4j file dose not exist : " + logFile);
				System.out.println("Invalid config file: " + cfgFile);
				return false;
			}
			
			Properties props = new Properties();
			try{			
			istream = new FileInputStream(logFile);  
			props.load(istream);

			} finally {
				if(istream != null){
					istream.close();
				}
			}
			
			if (!FileUtil.isFile(dbConf))
			{
				log.error("DB config file dose not exist : " + dbConf);
				log.error("Invalid config file: " + dbConf);
				return false;
			}
			XMLTransformDBUtil.setConfPath(dbConf);

			String lFile = props.getProperty("log4j.appender.R.File");//����·��
			lFile = FileUtil.getEnvPath(lFile);
			props.setProperty("log4j.appender.R.File",lFile);  
			
//			PropertyConfigurator.configure(props);//װ��log4j������Ϣ
			//DOMConfigurator.configure(logFile);
			
			_basepathArray = pathsNode.getBasepath();
			for (i = 0; i < _basepathArray.length; i++) 
			{
				replaceEnv(_basepathArray[i]);
				appendEndSep(_basepathArray[i]);
				if (!checkBasepath(_basepathArray[i]))
				{
					log.error("Invalid config file: " + cfgFile);
					//System.out.println("Invalid config file: " + cfgFile);
					return false;
				}
			}
			
			Category cgNode = cfgNode.getCategory();
			_entryArray = cgNode.getEntry();
			for (i = 0; i < _entryArray.length; i++) 
			{
				Basepath basepath = getBasepath(_entryArray[i].getBasepath());
				if (basepath == null)
				{
					log.error("Can not find Basepath of Entry: " + _entryArray[i].getBasepath());
					log.error("Invalid config file: " + cfgFile);
					//System.out.println("Can not find Basepath of Entry: " + _entryArray[i].getBasepath());
					//System.out.println("Invalid config file: " + cfgFile);
					return false;
				}

				addBasepath(_entryArray[i], basepath);
				if (!checkEntry(_entryArray[i]))
				{
					log.error("Check Entry failed! Invalid config file: " + cfgFile);
					//System.out.println("Invalid config file: " + cfgFile);
					return false;
				}
			}
			}finally{
				xmlfile.close();
			}
		}
		catch (ValidationException ex) 
		{
			System.out.println(">>>>>> PROCESSING ValidationException......");
			log.error(">>>>>>  ValidationException......" + ex.getMessage());
			return false;
		}
		catch (MarshalException ex) 
		{
			System.out.println(">>>>>> PROCESSING MarshalException......");
			log.error(">>>>>>  MarshalException......" + ex.getMessage());
			return false;
		}
		catch (FileNotFoundException ex) 
		{
			System.out.println(">>>>>> PROCESSING FileNotFoundException......");
			log.error(">>>>>>  FileNotFoundException......" + ex.getMessage());
			return false;
		}		
		catch (IOException ex) 
		{
			System.out.println(">>>>>> PROCESSING IOException......");
			log.error(">>>>>>  IOException......" + ex.getMessage());
			return false;
		}
		
		return true;
	}
}