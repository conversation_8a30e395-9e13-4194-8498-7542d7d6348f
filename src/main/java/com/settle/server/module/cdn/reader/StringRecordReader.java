package com.settle.server.module.cdn.reader;


import com.settle.server.module.cdn.record.Header;
import com.settle.server.module.cdn.record.StringRecord;

import java.time.LocalDateTime;
import java.util.Scanner;

/**
 * 从字符串读取数据的 {@link RecordReader}
 * 此读取器生成 {@link StringRecord} 实例
 *
 * <AUTHOR>
 */
public class StringRecordReader implements RecordReader<String> {

    private long currentRecordNumber;
    private Scanner scanner;
    private String dataSource;

    /**
     * 创建一个新的 {@link StringRecordReader}
     *
     * @param dataSource 字符串数据源
     */
    public StringRecordReader(final String dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void open() {
        currentRecordNumber = 1;
        scanner = new Scanner(dataSource);
    }

    @Override
    public StringRecord readRecord() {
        Header header = new Header(currentRecordNumber++, getDataSourceName(), LocalDateTime.now());
        String payload = scanner.hasNextLine() ? scanner.nextLine() : null;
        return payload == null ? null : new StringRecord(header, payload);
    }

    private String getDataSourceName() {
        return "In-Memory String";
    }

    @Override
    public void close() {
        scanner.close();
    }
}
