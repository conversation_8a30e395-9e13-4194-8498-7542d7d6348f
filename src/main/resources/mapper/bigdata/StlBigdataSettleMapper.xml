<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.bigdata.StlBigdataSettleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.bigdata.StlBigdataSettle">
        <id column="id" property="id"/>
        <result column="resp_file_name" property="respFileName"/>
        <result column="in_province_name" property="inProvinceName"/>
        <result column="out_porvince_name" property="outPorvinceName"/>
        <result column="business_type" property="businessType"/>
        <result column="avg_flow" property="avgFlow"/>
        <result column="op_time" property="opTime"/>
        <result column="settle_amount" property="settleAmount"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into stl_bigdata_settle values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id},
            #{item.respFileName},
            #{item.inProvinceName},
            #{item.outPorvinceName},
            #{item.businessType},
            #{item.avgFlow},
            #{item.opTime},
            #{item.settleAmount},
            #{item.taxRate},
            #{item.createdTime}
            )
        </foreach>
    </insert>

</mapper>
