<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
		  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

	<localRepository>/github/workspace/repository/</localRepository>

	<pluginGroups></pluginGroups>

	<proxies></proxies>

	<servers>
		<server>
			<username>l9hedph0oxlk</username>
			<password>Yb0rLKnJRS4J</password>
			<id>cmcc-devops</id>
		</server>
	</servers>

	<profiles>
		<profile>
			<repositories>
				<repository>
					<id>cmcc-devops</id>
					<name>maven</name>
					<url>https://artifactory.dep.devops.cmit.cloud:20101/artifactory/abs_devops_maven_local</url>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>cmcc-devops</id>
					<name>maven</name>
					<url>https://artifactory.dep.devops.cmit.cloud:20101/artifactory/maven</url>
				</pluginRepository>
			</pluginRepositories>
			<id>artifactory</id>
		</profile>
	</profiles>

	<activeProfiles>
		<activeProfile>artifactory</activeProfile>
	</activeProfiles>
</settings>
