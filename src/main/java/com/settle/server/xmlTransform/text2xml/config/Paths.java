/*
 * This class was automatically generated with 
 * <a href="http://www.castor.org">Castor 1.2</a>, using an XML
 * Schema.
 * $Id$
 */

package com.settle.server.xmlTransform.text2xml.config;

  //---------------------------------/
 //- Imported classes and packages -/
//---------------------------------/

import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;

/**
 * Class Paths.
 * 
 * @version $Revision$ $Date$
 */
public class Paths implements java.io.Serializable {


      //--------------------------/
     //- Class/Member Variables -/
    //--------------------------/

    /**
     * Field _log4j.
     */
    private String _log4j;

    /**
     * Field _DBConf.
     */
    private String _DBConf;

    /**
     * Field _basepathList.
     */
    private java.util.List _basepathList;


      //----------------/
     //- Constructors -/
    //----------------/

    public Paths() {
        super();
        this._basepathList = new java.util.ArrayList();
    }


      //-----------/
     //- Methods -/
    //-----------/

    /**
     * 
     * 
     * @param vBasepath
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     */
    public void addBasepath(
            final Basepath vBasepath)
    throws IndexOutOfBoundsException {
        this._basepathList.add(vBasepath);
    }

    /**
     * 
     * 
     * @param index
     * @param vBasepath
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     */
    public void addBasepath(
            final int index,
            final Basepath vBasepath)
    throws IndexOutOfBoundsException {
        this._basepathList.add(index, vBasepath);
    }

    /**
     * Method enumerateBasepath.
     * 
     * @return an Enumeration over all possible elements of this
     * collection
     */
    public java.util.Enumeration enumerateBasepath(
    ) {
        return java.util.Collections.enumeration(this._basepathList);
    }

    /**
     * Method getBasepath.
     * 
     * @param index
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     * @return the value of the
     * com.hp.xmlusage.text2xml.config.Basepath at the given index
     */
    public Basepath getBasepath(
            final int index)
    throws IndexOutOfBoundsException {
        // check bounds for index
        if (index < 0 || index >= this._basepathList.size()) {
            throw new IndexOutOfBoundsException("getBasepath: Index value '" + index + "' not in range [0.." + (this._basepathList.size() - 1) + "]");
        }
        
        return (Basepath) _basepathList.get(index);
    }

    /**
     * Method getBasepath.Returns the contents of the collection in
     * an Array.  <p>Note:  Just in case the collection contents
     * are changing in another thread, we pass a 0-length Array of
     * the correct type into the API call.  This way we <i>know</i>
     * that the Array returned is of exactly the correct length.
     * 
     * @return this collection as an Array
     */
    public Basepath[] getBasepath(
    ) {
        Basepath[] array = new Basepath[0];
        return (Basepath[]) this._basepathList.toArray(array);
    }

    /**
     * Method getBasepathCount.
     * 
     * @return the size of this collection
     */
    public int getBasepathCount(
    ) {
        return this._basepathList.size();
    }

    /**
     * Returns the value of field 'DBConf'.
     * 
     * @return the value of field 'DBConf'.
     */
    public String getDBConf(
    ) {
        return this._DBConf;
    }

    /**
     * Returns the value of field 'log4j'.
     * 
     * @return the value of field 'Log4j'.
     */
    public String getLog4j(
    ) {
        return this._log4j;
    }

    /**
     * Method isValid.
     * 
     * @return true if this object is valid according to the schema
     */
    public boolean isValid(
    ) {
        try {
            validate();
        } catch (org.exolab.castor.xml.ValidationException vex) {
            return false;
        }
        return true;
    }

    /**
     * Method iterateBasepath.
     * 
     * @return an Iterator over all possible elements in this
     * collection
     */
    public java.util.Iterator iterateBasepath(
    ) {
        return this._basepathList.iterator();
    }

    /**
     * 
     * 
     * @param out
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void marshal(
            final java.io.Writer out)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, out);
    }

    /**
     * 
     * 
     * @param handler
     * @throws java.io.IOException if an IOException occurs during
     * marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     */
    public void marshal(
            final org.xml.sax.ContentHandler handler)
    throws java.io.IOException, org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        Marshaller.marshal(this, handler);
    }

    /**
     */
    public void removeAllBasepath(
    ) {
        this._basepathList.clear();
    }

    /**
     * Method removeBasepath.
     * 
     * @param vBasepath
     * @return true if the object was removed from the collection.
     */
    public boolean removeBasepath(
            final Basepath vBasepath) {
        boolean removed = _basepathList.remove(vBasepath);
        return removed;
    }

    /**
     * Method removeBasepathAt.
     * 
     * @param index
     * @return the element removed from the collection
     */
    public Basepath removeBasepathAt(
            final int index) {
        Object obj = this._basepathList.remove(index);
        return (Basepath) obj;
    }

    /**
     * 
     * 
     * @param index
     * @param vBasepath
     * @throws IndexOutOfBoundsException if the index
     * given is outside the bounds of the collection
     */
    public void setBasepath(
            final int index,
            final Basepath vBasepath)
    throws IndexOutOfBoundsException {
        // check bounds for index
        if (index < 0 || index >= this._basepathList.size()) {
            throw new IndexOutOfBoundsException("setBasepath: Index value '" + index + "' not in range [0.." + (this._basepathList.size() - 1) + "]");
        }
        
        this._basepathList.set(index, vBasepath);
    }

    /**
     * 
     * 
     * @param vBasepathArray
     */
    public void setBasepath(
            final Basepath[] vBasepathArray) {
        //-- copy array
        _basepathList.clear();
        
        for (int i = 0; i < vBasepathArray.length; i++) {
                this._basepathList.add(vBasepathArray[i]);
        }
    }

    /**
     * Sets the value of field 'DBConf'.
     * 
     * @param DBConf the value of field 'DBConf'.
     */
    public void setDBConf(
            final String DBConf) {
        this._DBConf = DBConf;
    }

    /**
     * Sets the value of field 'log4j'.
     * 
     * @param log4j the value of field 'log4j'.
     */
    public void setLog4j(
            final String log4j) {
        this._log4j = log4j;
    }

    /**
     * Method unmarshal.
     * 
     * @param reader
     * @throws org.exolab.castor.xml.MarshalException if object is
     * null or if any SAXException is thrown during marshaling
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     * @return the unmarshaled com.hp.xmlusage.text2xml.config.Paths
     */
    public static Paths unmarshal(
            final java.io.Reader reader)
    throws org.exolab.castor.xml.MarshalException, org.exolab.castor.xml.ValidationException {
        return (Paths) Unmarshaller.unmarshal(Paths.class, reader);
    }

    /**
     * 
     * 
     * @throws org.exolab.castor.xml.ValidationException if this
     * object is an invalid instance according to the schema
     */
    public void validate(
    )
    throws org.exolab.castor.xml.ValidationException {
        org.exolab.castor.xml.Validator validator = new org.exolab.castor.xml.Validator();
        validator.validate(this);
    }

}
