package com.settle.server.module.mnp.validators;


import com.settle.server.module.mnp.dto.MessageResult;
import com.settle.server.module.mnp.dto.FileType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Service
public class FileContentValidationHandler {

    @Autowired
    private Map<String, FileValidator> fileValidatorMap;

    public MessageResult handleFileContentValidation(String fileType, List<String> fieldList, String[] fieldValues) throws IOException, InterruptedException {
        FileType value = FileType.getByType(fileType);
        if (value == null) {
            return MessageResult.success();
        }

        FileValidator fileValidator = fileValidatorMap.get(value.getBeanName());

        return fileValidator.validateFileContent(fieldList, fieldValues);
    }
}

