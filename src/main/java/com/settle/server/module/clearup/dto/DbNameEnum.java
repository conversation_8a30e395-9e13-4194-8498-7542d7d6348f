package com.settle.server.module.clearup.dto;

public enum DbNameEnum {
    STLUDR("STLUDR","stludrDataSource"),
    STLUSERS("STLUSERS","stlusersDataSource"),
    STTLBIZ("STTLBIZ","sttlbizDataSource");


    private String dbName;
    private String dataSourceBeanName;

    DbNameEnum(String dbName, String dataSourceBeanName) {
        this.dbName = dbName;
        this.dataSourceBeanName = dataSourceBeanName;
    }

    public String getDbName() {
        return dbName;
    }

    public String getDataSourceBeanName() {
        return dataSourceBeanName;
    }

    public static DbNameEnum getDbName(String dbName) {
        for (DbNameEnum dbNameEnum : DbNameEnum.values()) {
            if (dbNameEnum.getDbName().equals(dbName)) {
                return dbNameEnum;
            }
        }
        return null;
    }
}
