<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.bboss.StlMemInterestsMapper">

    <resultMap type="com.settle.server.entity.membership.StlMemInterests" id="BaseResultMap">
        <result column="order_source" property="orderSource" />
        <result column="prodord_sku_num" property="prodordSkuNum" />
        <result column="prodist_sku_num" property="prodistSkuNum" />
        <result column="customer_num" property="customerNum" />
        <result column="member_num" property="memberNum" />
        <result column="resource_code" property="resourceCode" />
        <result column="province" property="province" />
        <result column="collect_time" property="collectTime" />
        <result column="file_name" property="fileName" />
        <result column="row_no" property="rowNo" />
        <result column="acct_month" property="acctMonth" />
        <result column="status" property="status" />
        <result column="err_msg" property="errMsg" />
        <result column="member_type" property="memberType" />
        <result column="prov_nm" property="provNm" />
    </resultMap>


    <!--查询指定行数据-->
    <select id="queryAll" resultMap="BaseResultMap"  parameterType="com.settle.server.entity.membership.StlMemInterests">>
        select
        order_source, prodord_sku_num, prodist_sku_num, customer_num, member_num, resource_code, province, collect_time, file_name, row_no, acct_month, status, err_msg, member_type, prov_nm
        from boss_billing.stl_mem_interests
        <where>
            <if test="orderSource != null and orderSource != ''">
                and order_source = #{orderSource}
            </if>
            <if test="prodordSkuNum != null and prodordSkuNum != ''">
                and prodord_sku_num = #{prodordSkuNum}
            </if>
            <if test="prodistSkuNum != null and prodistSkuNum != ''">
                and prodist_sku_num = #{prodistSkuNum}
            </if>
            <if test="customerNum != null and customerNum != ''">
                and customer_num = #{customerNum}
            </if>
            <if test="memberNum != null and memberNum != ''">
                and member_num = #{memberNum}
            </if>
            <if test="resourceCode != null and resourceCode != ''">
                and resource_code = #{resourceCode}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="collectTime != null and collectTime != ''">
                and collect_time = #{collectTime}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="rowNo != null and rowNo != ''">
                and row_no = #{rowNo}
            </if>
            <if test="acctMonth != null and acctMonth != ''">
                and acct_month = #{acctMonth}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="errMsg != null and errMsg != ''">
                and err_msg = #{errMsg}
            </if>
            <if test="memberType != null and memberType != ''">
                and member_type = #{memberType}
            </if>
            <if test="provNm != null and provNm != ''">
                and prov_nm = #{provNm}
            </if>
        </where>
    </select>

    <select id="selectCountByCondition"  resultType="java.lang.Integer">
        select count(*)
          from boss_billing.stl_mem_interests
        <where>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="acctMonth != null and acctMonth != ''">
                and acct_month = #{acctMonth}
            </if>
        </where>
    </select>

    <select id="selectInfoByCondition" resultMap="BaseResultMap">
        select
        order_source, prodord_sku_num, prodist_sku_num, customer_num, member_num, resource_code, province, collect_time, file_name, row_no, acct_month, status, err_msg, member_type, prov_nm
        from boss_billing.stl_mem_interests
        <where>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="acctMonth != null and acctMonth != ''">
                and acct_month = #{acctMonth}
            </if>
        </where>
        order by file_name,CAST(row_no AS UNSIGNED)
        LIMIT #{offset}, #{limit}
    </select>




    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from boss_billing.stl_mem_interests
        <where>
            <if test="orderSource != null and orderSource != ''">
                and order_source = #{orderSource}
            </if>
            <if test="prodordSkuNum != null and prodordSkuNum != ''">
                and prodord_sku_num = #{prodordSkuNum}
            </if>
            <if test="prodistSkuNum != null and prodistSkuNum != ''">
                and prodist_sku_num = #{prodistSkuNum}
            </if>
            <if test="customerNum != null and customerNum != ''">
                and customer_num = #{customerNum}
            </if>
            <if test="memberNum != null and memberNum != ''">
                and member_num = #{memberNum}
            </if>
            <if test="resourceCode != null and resourceCode != ''">
                and resource_code = #{resourceCode}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="collectTime != null and collectTime != ''">
                and collect_time = #{collectTime}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="rowNo != null and rowNo != ''">
                and row_no = #{rowNo}
            </if>
            <if test="acctMonth != null and acctMonth != ''">
                and acct_month = #{acctMonth}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="errMsg != null and errMsg != ''">
                and err_msg = #{errMsg}
            </if>
            <if test="memberType != null and memberType != ''">
                and member_type = #{memberType}
            </if>
            <if test="provNm != null and provNm != ''">
                and prov_nm = #{provNm}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into boss_billing.stl_mem_interests(order_source, prodord_sku_num, prodist_sku_num, customer_num, member_num, resource_code, province, collect_time, file_name, row_no, acct_month, status, err_msg, member_type, prov_nm)
        values (#{orderSource}, #{prodordSkuNum}, #{prodistSkuNum}, #{customerNum}, #{memberNum}, #{resourceCode}, #{province}, #{collectTime}, #{fileName}, #{rowNo}, #{acctMonth}, #{status}, #{errMsg}, #{memberType}, #{provNm})
    </insert>

    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into boss_billing.stl_mem_interests(order_source, prodord_sku_num, prodist_sku_num, customer_num, member_num, resource_code, province, collect_time, file_name, row_no, acct_month, status, err_msg, member_type, prov_nm)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderSource}, #{entity.prodordSkuNum}, #{entity.prodistSkuNum}, #{entity.customerNum}, #{entity.memberNum}, #{entity.resourceCode}, #{entity.province}, #{entity.collectTime}, #{entity.fileName}, #{entity.rowNo}, #{entity.acctMonth}, #{entity.status}, #{entity.errMsg}, #{entity.memberType}, #{entity.provNm})
        </foreach>
    </insert>

    <insert id="batchInsert" useGeneratedKeys="false" parameterType="java.util.List">
        insert into boss_billing.stl_mem_interests (order_source, prodord_sku_num, prodist_sku_num, customer_num, member_num, resource_code, province, collect_time,
                                         file_name, row_no, acct_month, status, err_msg, member_type, prov_nm)
        values
        <foreach collection="list" item="item" separator=",">
            (
             #{item.orderSource},
             #{item.prodordSkuNum},
             #{item.prodistSkuNum},
             #{item.customerNum},
             #{item.memberNum},
             #{item.resourceCode},
             #{item.province},
             #{item.collectTime},
             #{item.fileName},
             #{item.rowNo},
             #{item.acctMonth},
             #{item.status},
             #{item.errMsg},
             #{item.memberType},
             #{item.provNm})
        </foreach>
    </insert>

    <update id="updateProvNumByAcctMonth">
        update stl_mem_interests a
        set prov_nm = (select prov_name
                       from province_t b
                       where a.province = b.prov_code and a.province = b.prov_code)
        where acct_month = #{acctMonth, jdbcType = VARCHAR} and status is null
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName, jdbcType = VARCHAR}
        </if>
    </update>

    <update id="updateStatusAndErrMsgByAcctMonth">
        update stl_mem_interests a
        set status  = '99',
            err_msg = '省代码不存在'
        where acct_month = #{acctMonth, jdbcType = VARCHAR}
          and status is null
          and a.prov_nm is null
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName, jdbcType = VARCHAR}
        </if>
    </update>

    <update id="updateStatusByAcctMonthAndCustNum">
        update stl_mem_interests a
        set status = '05'
        where acct_month = #{acctMonth, jdbcType = VARCHAR}
          and status is null
          and not exists(select *
                         from serv_biz_code b
                         where #{acctMonth, jdbcType = VARCHAR} between to_char(b.effective_date, 'YYYYMM') and to_char(b.expiry_date, 'YYYYMM')
                           and b.ec_code = a.customer_num
                           and b.order_id = a.prodist_sku_num)
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName, jdbcType = VARCHAR}
        </if>
    </update>

    <update id="updateStatusByAcctMonthAndMemberNum">
        update stl_mem_interests a
        set status = '02'
        where acct_month =  #{acctMonth, jdbcType = VARCHAR}
          and status is null
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName, jdbcType = VARCHAR}
        </if>
          and not exists(select *
                         from sur_addition_member_subscriber b
                         where  #{acctMonth, jdbcType = VARCHAR} between to_char(b.effective_date, 'YYYYMM') and to_char(b.expiry_date, 'YYYYMM')
                           and b.subscriber_id = a.prodist_sku_num
                           and b.member_number = a.member_num)
    </update>


    <update id="updateStatusByProdisNum">
        update stl_mem_interests a
        set status = '00',
            member_type = (select b.field_value
                           from sur_addition_subscriber b
                           where #{acctMonth, jdbcType = VARCHAR} between to_char(b.effective_date, 'YYYYMM') and to_char(b.expiry_date, 'YYYYMM')
                             and b.subscriber_id = a.prodist_sku_num)
        where acct_month = #{acctMonth, jdbcType = VARCHAR}
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName, jdbcType = VARCHAR}
        </if>
          and status is null
    </update>

    <update id="updateStatusByAcctMonth">
        update stl_mem_interests a
        set status  = '99',
            err_msg = '无法查询到个付/统付信息！'
        where acct_month = #{acctMonth, jdbcType = VARCHAR}
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName, jdbcType = VARCHAR}
        </if>
          and status is null;
    </update>



    <delete id="deleteBySettleMonth" parameterType="java.lang.String">
        delete from boss_billing.stl_mem_interests
        <where>
            <if test="acctMonth != null and acctMonth != ''">
                AND acct_month = #{acctMonth, jdbcType = VARCHAR}
            </if>
            <if test="memberType != null and memberType != ''">
                AND member_type = #{memberType, jdbcType = VARCHAR}
            </if>
            <if test="fileName != null and fileName != ''">
                AND file_name = #{fileName, jdbcType = VARCHAR}
            </if>
        </where>
    </delete>

</mapper>