package com.settle.server.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.settle.server.module.mnp.MNPHandler;
import com.settle.server.utils.DateTimeUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;

@Component
@Slf4j
public class MNPServerJob {

    @Autowired
    private MNPHandler handler;

    @XxlJob("mnpServerJobHandler")
    public ReturnT<String> mnpServerJobHandler()  {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("MNPServer开始，接受参数：{}", param);
        log.info("MNPServer开始，接受参数:{}",param);
        ReturnT<String> returnT;
        try {
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = new JSONObject();
            if (StringUtils.isNotBlank(param)) {
                entries = JSONUtil.parseObj(param);
            }
            String acctMonth = entries.getStr("acctMonth");
            if (StringUtils.isBlank(acctMonth)) {
                acctMonth = DateTimeUtil.lastMonth();
            } else {
                try {
                    //校验日志格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    sdf.setLenient(false);
                    sdf.parse(acctMonth);
                } catch (ParseException e) {
                    XxlJobHelper.log("账期格式错误,请传入正确的账期格式：yyyyMM");
                    return ReturnT.FAIL;
                }
            }
            XxlJobHelper.log("当前执行账期：{}", acctMonth );

            ReturnT<String> prepare = handler.prepare(acctMonth);
            if (prepare.getCode() == ReturnT.FAIL_CODE) {
                XxlJobHelper.log(prepare.getMsg());
                return prepare;
            }
            returnT = handler.start(acctMonth);
        } catch (Exception e) {
            log.error("MNPServer执行失败", e);
            String message = ExceptionUtils.getStackTrace(e);
            XxlJobHelper.log("MNPServer执行失败,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }
}
