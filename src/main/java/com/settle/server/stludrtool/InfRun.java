package com.settle.server.stludrtool;

import java.io.File;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.settle.server.pojo.InfSettleModel;
import com.settle.server.utils.ConfUtil;
import com.settle.server.utils.TimeTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

@Slf4j
public class InfRun {
	private static final String SQL = "INSERT INTO SYNC_CB_INTERFACE(REC_TYPE, BAL_MONTH, PRODUCT_CODE, ECID, SERVICE_CODE, SEND_PROV, RECV_PROV, SHEET_CNT, BAL_FEE)VALUES(?,?,?,?,?,?,?,?,?)";
	private static final int _COMMIT = 10000;

	public void insertDBData(String month) {
		//String path = ConfUtil.INF_TXTX_PATH + File.separator;
		String name = ConfUtil.INF_TXTX_NAME.replace("YYYYMM", month);
		File file = new File(ConfUtil.INF_TXTX_PATH + name);
		List<String> errorList = new ArrayList<String>();
		try {
			if (file.isFile()) {
				deleteData(month);
				long start = System.currentTimeMillis();
				int count = 0;
				StludrToolDBUtil ju = new StludrToolDBUtil();
				ju.getConn().setAutoCommit(false);
				ju.setPre(ju.getConn().prepareStatement(SQL, ResultSet.TYPE_SCROLL_SENSITIVE,
						ResultSet.CONCUR_READ_ONLY));
				List<String> list = FileUtils.readLines(file, "UTF-8");
				for (int i = 0; i < list.size(); i++) {
					InfSettleModel ism = new InfSettleModel(list.get(i));
					if (ism.isStatus()) {
						ju.getPre().setString(1, ism.getRecType());
						ju.getPre().setString(2, ism.getBalMonth());
						ju.getPre().setString(3, ism.getProductCode());
						ju.getPre().setString(4, ism.getEcId());
						ju.getPre().setString(5, ism.getServiceCode());
						ju.getPre().setString(6, ism.getSendProv());
						ju.getPre().setString(7, ism.getRecvProv());
						ju.getPre().setLong(8, ism.getSheetCnt());
						ju.getPre().setLong(9, ism.getBalFee());
						ju.getPre().addBatch();
						count++;
					} else {
						errorList.add(list.get(i));
					}
					if (count % _COMMIT == 0) {
						ju.getPre().executeBatch();
						ju.getConn().commit();
						log.debug("*** *** 导入数据条数: " + count + " *** ***");
						LogRun.logDebug(month, file.getAbsolutePath(), "*** *** 导入数据条数: " + count + " *** ***");
					}
				}
				ju.getPre().executeBatch();
				ju.getConn().commit();
				log.debug("*** *** 总共导入数据条数: " + count + " *** ***");
				LogRun.logDebug(month, file.getAbsolutePath(), "*** *** 总共导入数据条数: " + count + " *** ***");
				ju.close();
				long stop = System.currentTimeMillis();
				log.info("*** *** 数据入库耗时: " + TimeTool.toTime(stop - start) + " *** ***");
				log.info("*** *** 错误数据共: " + errorList.size() + "条 *** ***");
				LogRun.logInfo(month, file.getAbsolutePath(), "*** *** 数据入库耗时: " + TimeTool.toTime(stop - start) + " *** ***");
				LogRun.logError(month, file.getAbsolutePath(), "*** *** 错误数据共: " + errorList.size() + "条 *** ***");
				log.info("*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ");
				for (String str : errorList) {
					log.info(str);
					LogRun.logError(month, file.getAbsolutePath(), str);
				}
				log.info("*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ");
				FileUtils.moveFileToDirectory(file, new File(ConfUtil.INF_TXTX_PATH_BAK), true);
				File srcFile = new File(ConfUtil.INF_TXTX_PATH_BAK+File.separator+file.getName());
				SimpleDateFormat sdf_ = new SimpleDateFormat("yyMMddHHmmss");
				File destFile = new File(ConfUtil.INF_TXTX_PATH_BAK+File.separator+file.getName()+"."+sdf_.format(new Date()));
				srcFile.renameTo(destFile);
				LogRun.logInfo(month, file.getAbsolutePath(), "*** *** 内容计费数据入库完成! *** ***");
			} else {
				log.error("*** *** " + file.getAbsolutePath() + "文件不存在! *** ***");
				LogRun.logError(month, file.getAbsolutePath(), "*** *** " + file.getAbsolutePath() + "文件不存在! *** ***");
				return;
			}
		} catch (IOException e) {
			log.error(e.getMessage());
			LogRun.logError(month, file.getAbsolutePath(), e.getMessage());
			return;
		} catch (SQLException e) {
			log.error(e.getMessage());
			LogRun.logError(month, file.getAbsolutePath(), e.getMessage());
			return;
		}
	}
	
	private boolean deleteData(String month) {
		StludrToolDBUtil ju = new StludrToolDBUtil();
		try {
			ju.setPre(ju.getConn().prepareStatement("DELETE FROM SYNC_CB_INTERFACE WHERE BAL_MONTH=?"));
			ju.getPre().setString(1, month);
			ju.getPre().execute();
			LogRun.logInfo(month, "", "*** *** 清除" + month + "账期的内容计费数据! *** ***");
			return true;
		} catch (SQLException e) {
			log.error(e.getMessage());
			LogRun.logError(month, "", e.getMessage());
			return false;
		}
	}
}
