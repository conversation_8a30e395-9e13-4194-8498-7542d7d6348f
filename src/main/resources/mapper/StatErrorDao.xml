<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.settle.server.dao.bossAcct.StatErrorDao">


    <select id="queryStatErrorCodeInfo" resultType="com.settle.server.entity.StatErrorInfo">
        select t.biz_type,t.err_code,a.description,sum(t.count) as count from boss_acct.${tableName} t,boss_acct.def_error_code a
        where t.biz_type=a.biz_type and t.err_code=a.err_code(+)
          and t.biz_type in ('SMS','MMS') group by t.biz_type,t.err_code,a.description order by t.biz_type,t.err_code;
    </select>

</mapper>