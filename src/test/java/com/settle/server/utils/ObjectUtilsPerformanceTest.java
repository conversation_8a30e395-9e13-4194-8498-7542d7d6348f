package com.settle.server.utils;

import com.settle.server.entity.mnp.StlBaseinfoSrv6;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * ObjectUtils性能测试
 */
public class ObjectUtilsPerformanceTest {

    @Test
    public void testPerformanceComparison() {
        int testSize = 10000;
        List<StlBaseinfoSrv6> testObjects = createTestObjects(testSize);
        
        // 测试优化后的方法
        long startTime = System.currentTimeMillis();
        for (StlBaseinfoSrv6 obj : testObjects) {
            ObjectUtils.replaceEmptyStringWithNull(obj);
        }
        long optimizedTime = System.currentTimeMillis() - startTime;
        
        // 重新创建测试对象
        testObjects = createTestObjects(testSize);
        
        // 测试原始方法
        startTime = System.currentTimeMillis();
        for (StlBaseinfoSrv6 obj : testObjects) {
            replaceEmptyStringWithNullOriginal(obj);
        }
        long originalTime = System.currentTimeMillis() - startTime;
        
        System.out.println("测试对象数量: " + testSize);
        System.out.println("优化后方法耗时: " + optimizedTime + "ms");
        System.out.println("原始方法耗时: " + originalTime + "ms");
        System.out.println("性能提升: " + ((double)(originalTime - optimizedTime) / originalTime * 100) + "%");
    }
    
    private List<StlBaseinfoSrv6> createTestObjects(int size) {
        List<StlBaseinfoSrv6> objects = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            StlBaseinfoSrv6 obj = new StlBaseinfoSrv6();
            obj.setPospecnumber(i % 2 == 0 ? "PO" + i : ""); // 一半为空字符串
            obj.setPospecname("PO Name " + i);
            obj.setSospecnumber(i % 3 == 0 ? "" : "SO" + i); // 三分之一为空字符串
            obj.setSospecname("SO Name " + i);
            obj.setSoid("SOID" + i);
            obj.setCustomernumber(i % 4 == 0 ? "   " : "CUST" + i); // 四分之一为空白字符串
            obj.setCustomername("Customer " + i);
            obj.setStartProvNm("Start Prov " + i);
            obj.setStartProv("SP" + i);
            obj.setStartCity("Start City " + i);
            obj.setManagerNm(i % 5 == 0 ? "" : "Manager " + i);
            obj.setManagerCon("Manager Contact " + i);
            obj.setEndProvNm("End Prov " + i);
            obj.setEndProv("EP" + i);
            obj.setEndCity("End City " + i);
            obj.setEndDistr("End District " + i);
            obj.setCpNm("CP Name " + i);
            obj.setCpCon("CP Contact " + i);
            obj.setBandwidth("100M");
            obj.setAddress("Address " + i);
            obj.setStartRentRate("100");
            obj.setEndRentRate("200");
            obj.setStartSetupRate("50");
            obj.setEndSetupRate("60");
            obj.setStartServRentRate("70");
            obj.setEndServRentRate("80");
            obj.setStartServOnceRate("90");
            obj.setEndServOnceRate("100");
            obj.setSettlemonth("202407");
            objects.add(obj);
        }
        return objects;
    }
    
    // 原始的方法实现（用于性能对比）
    private static void replaceEmptyStringWithNullOriginal(Object obj) {
        if (obj == null) {
            return;
        }
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true); // 每次都设置可访问性
            try {
                Object value = field.get(obj);
                if (value instanceof String && ((String) ((String) value).trim()).isEmpty()) {
                    field.set(obj, null);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }
}
