package com.settle.server.module.cdn.writer;


import com.settle.server.module.cdn.record.Batch;
import com.settle.server.module.cdn.record.Record;
import com.settle.server.utils.Utils;

import java.util.Collection;

/**
 * 将记录写入 {@link Collection} 的记录写入器
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public class CollectionRecordWriter<P> implements RecordWriter<P> {

    private Collection<P> collection;

    /**
     * Create a new {@link CollectionRecordWriter}
     *
     * @param collection 要写入记录的集合
     */
    public CollectionRecordWriter(final Collection<P> collection) {
        Utils.checkNotNull(collection, "collection");
        this.collection = collection;
    }

    @Override
    public void writeRecords(Batch<P> batch) {
        for (Record<P> record : batch) {
            collection.add(record.getPayload());
        }
    }

}
