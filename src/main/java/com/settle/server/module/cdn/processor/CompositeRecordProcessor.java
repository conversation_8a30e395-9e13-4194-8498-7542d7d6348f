package com.settle.server.module.cdn.processor;


import com.settle.server.module.cdn.record.Record;

import java.util.ArrayList;
import java.util.List;

/**
 * 复合记录处理器
 *
 * <AUTHOR>
 */
public class CompositeRecordProcessor<I, O> implements RecordProcessor<I, O> {

    private List<RecordProcessor<I, O>> processors;

    /**
     * 创建一个新的 {@link CompositeRecordProcessor}
     */
    public CompositeRecordProcessor() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositeRecordProcessor}
     *
     * @param processors 委托
     */
    public CompositeRecordProcessor(List<RecordProcessor<I, O>> processors) {
        this.processors = processors;
    }

    @Override
    @SuppressWarnings(value = "unchecked")
    public Record<O> processRecord(Record<I> record) throws Exception {
        Record processedRecord = record;
        for (RecordProcessor processor : processors) {
            processedRecord = processor.processRecord(processedRecord);
            if (processedRecord == null) {
                return null;
            }
        }
        return processedRecord;
    }

    /**
     * 添加委托记录处理器
     *
     * @param recordProcessor 添加
     */
    public void addRecordProcessor(RecordProcessor<I, O> recordProcessor) {
        processors.add(recordProcessor);
    }
}
