package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Record;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * 将处理委托给其他侦听器的复合监听器
 *
 * <AUTHOR>
 */
public class CompositePipelineListener implements PipelineListener {

    private List<PipelineListener> listeners;

    /**
     * 创建一个新的 {@link CompositePipelineListener}
     */
    public CompositePipelineListener() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositePipelineListener}
     *
     * @param listeners 委托
     */
    public CompositePipelineListener(List<PipelineListener> listeners) {
        this.listeners = listeners;
    }

    @Override
    public <P> Record<P> beforeRecordProcessing(Record<P> record) {
        Record<P> recordToProcess = record;
        for (PipelineListener listener : listeners) {
            recordToProcess = listener.beforeRecordProcessing(recordToProcess);
        }
        return recordToProcess;
    }

    @Override
    public <P> void afterRecordProcessing(Record<P> inputRecord, Record<P> outputRecord) {
        for (ListIterator<PipelineListener> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().afterRecordProcessing(inputRecord, outputRecord);
        }
    }

    @Override
    public <P> void onRecordProcessingException(Record<P> record, Throwable throwable) {
        for (ListIterator<PipelineListener> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().onRecordProcessingException(record, throwable);
        }
    }

    /**
     * 添加委托监听器
     *
     * @param pipelineListener 添加
     */
    public void addPipelineListener(PipelineListener pipelineListener) {
        listeners.add(pipelineListener);
    }
}
