<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">

  <!-- Root Element -->
  <xs:element name="BillList">
    <xs:complexType>
      <xs:sequence>
        <!-- ProvCode -->
        <xs:element name="ProvCode" minOccurs="1" maxOccurs="1">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:length value="3"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>

        <!-- ToCBizList -->
        <xs:element name="ToCBizList" minOccurs="1" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <!-- ToCBizInfo -->
              <xs:element name="ToCBizInfo" minOccurs="1" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>

                    <!-- ProductSubsList -->
                    <xs:element name="ProductSubsList" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>

                          <!-- SubProductInfo -->
                          <xs:element name="SubProductInfo" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>

                                <!-- ProductSubsID -->
                                <xs:element name="ProductSubsID" minOccurs="1" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:minLength value="1"/>
                                      <xs:maxLength value="22"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- CloudInstanceID -->
                                <xs:element name="CloudInstanceID" minOccurs="0" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:maxLength value="128"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- ProductID -->
                                <xs:element name="ProductID"  minOccurs="1" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:minLength value="1"/>
                                      <xs:maxLength value="22"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- ProductName -->
                                <xs:element name="ProductName"  minOccurs="1" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:minLength value="1"/>
                                      <xs:maxLength value="256"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- DbProductID -->
                                <xs:element name="DbProductID" minOccurs="1" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:minLength value="1"/>
                                      <xs:maxLength value="22"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- DbProductName -->
                                <xs:element name="DbProductName" minOccurs="0" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:maxLength value="256"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- GhCode -->
                                <xs:element name="GhCode" minOccurs="0" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:maxLength value="256"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- PVProductClass -->
                                <xs:element name="PVProductClass" minOccurs="0" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:maxLength value="20"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- IssueTime -->
                                <xs:element name="IssueTime" minOccurs="0" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:length value="14"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- ExpireTime -->
                                <xs:element name="ExpireTime" minOccurs="0" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:length value="14"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- BillingTerm -->
                                <xs:element name="BillingTerm" minOccurs="1" maxOccurs="1">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <xs:length value="6"/>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>

                                <!-- FeeList -->
                                <xs:element name="FeeList" minOccurs="1" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <!-- FeeInfo -->
                                      <xs:element name="FeeInfo" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                          <xs:sequence>

                                            <!-- FeeVal -->
                                            <xs:element name="FeeVal" minOccurs="1" maxOccurs="1">
                                              <xs:simpleType>
                                                <xs:restriction base="xs:decimal">
                                                  <xs:totalDigits value="16"/>
                                                </xs:restriction>
                                              </xs:simpleType>
                                            </xs:element>

                                            <!-- TaxRate -->
                                            <xs:element name="TaxRate" minOccurs="1" maxOccurs="1">
                                              <xs:simpleType>
                                                <xs:restriction base="xs:decimal">
                                                  <xs:totalDigits value="16"/>
                                                </xs:restriction>
                                              </xs:simpleType>
                                            </xs:element>

                                            <!-- Tax -->
                                            <xs:element name="Tax" minOccurs="1" maxOccurs="1">
                                              <xs:simpleType>
                                                <xs:restriction base="xs:decimal">
                                                  <xs:totalDigits value="16"/>
                                                </xs:restriction>
                                              </xs:simpleType>
                                            </xs:element>

                                            <!-- FeeNoTax -->
                                            <xs:element name="FeeNoTax" minOccurs="1" maxOccurs="1">
                                              <xs:simpleType>
                                                <xs:restriction base="xs:decimal">
                                                  <xs:totalDigits value="16"/>
                                                </xs:restriction>
                                              </xs:simpleType>
                                            </xs:element>

                                            <!-- Province2SpecialSettle -->
                                            <xs:element name="Province2SpecialSettle" minOccurs="0" maxOccurs="1">
                                              <xs:complexType>
                                                <xs:sequence>
                                                  <!-- Province2SpecialSettleinfo -->
                                                  <xs:element name="Province2SpecialSettleinfo" minOccurs="0" maxOccurs="unbounded">
                                                    <xs:complexType>
                                                      <xs:sequence>

                                                        <!-- SettlementPartyIn -->
                                                        <xs:element name="SettlementPartyIn"  minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementPartyOut -->
                                                        <xs:element name="SettlementPartyOut"  minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementRate -->
                                                        <xs:element name="SettlementRate" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="10"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementType -->
                                                        <xs:element name="SettlementType" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="10"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementAmount -->
                                                        <xs:element name="SettlementAmount" minOccurs="1" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:decimal">
                                                              <xs:totalDigits value="16"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementClass -->
                                                        <xs:element name="SettlementClass" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- ReportCode -->
                                                        <xs:element name="ReportCode" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                      </xs:sequence>
                                                    </xs:complexType>
                                                  </xs:element>

                                                </xs:sequence>
                                              </xs:complexType>
                                            </xs:element>

                                            <!-- Province2ProvinceSettle -->
                                            <xs:element name="Province2ProvinceSettle" minOccurs="0" maxOccurs="1">
                                              <xs:complexType>
                                                <xs:sequence>
                                                  <!-- Province2Provinceinfo -->
                                                  <xs:element name="Province2Provinceinfo" minOccurs="0" maxOccurs="unbounded">
                                                    <xs:complexType>
                                                      <xs:sequence>

                                                        <!-- SettlementPartyIn -->
                                                        <xs:element name="SettlementPartyIn" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementPartyOut -->
                                                        <xs:element name="SettlementPartyOut" minOccurs="1" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:minLength value="1"/>
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementAmount -->
                                                        <xs:element name="SettlementAmount" minOccurs="1" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:decimal">
                                                              <xs:totalDigits value="16"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementType -->
                                                        <xs:element name="SettlementType" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="20"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementRate -->
                                                        <xs:element name="SettlementRate" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="8"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                      </xs:sequence>
                                                    </xs:complexType>
                                                  </xs:element>

                                                </xs:sequence>
                                              </xs:complexType>
                                            </xs:element>

                                            <!-- ParSettle -->
                                            <xs:element name="ParSettle" minOccurs="0" maxOccurs="1">
                                              <xs:complexType>
                                                <xs:sequence>
                                                  <!-- ParSettleInfo -->
                                                  <xs:element name="ParSettleInfo" minOccurs="0" maxOccurs="1">
                                                    <xs:complexType>
                                                      <xs:sequence>

                                                        <!-- PartnerCode -->
                                                        <xs:element name="PartnerCode" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="50"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- PartnerName -->
                                                        <xs:element name="PartnerName" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="256"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- ParSettleRate -->
                                                        <xs:element name="ParSettleRate" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="10"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- SettlementType -->
                                                        <xs:element name="SettlementType" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="10"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- ParSettlAmount -->
                                                        <xs:element name="ParSettlAmount" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:decimal">
                                                              <xs:totalDigits value="16"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                        <!-- ParResSettlRate -->
                                                        <xs:element name="ParResSettlRate" minOccurs="0" maxOccurs="1">
                                                          <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                              <xs:maxLength value="10"/>
                                                            </xs:restriction>
                                                          </xs:simpleType>
                                                        </xs:element>

                                                      </xs:sequence>
                                                    </xs:complexType>
                                                  </xs:element>

                                                </xs:sequence>
                                              </xs:complexType>
                                            </xs:element>

                                          </xs:sequence>
                                        </xs:complexType>
                                      </xs:element>

                                    </xs:sequence>
                                  </xs:complexType>
                                </xs:element>

                              </xs:sequence>
                            </xs:complexType>
                          </xs:element>

                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>

                  </xs:sequence>
                </xs:complexType>
              </xs:element>

            </xs:sequence>
          </xs:complexType>
        </xs:element>

      </xs:sequence>
    </xs:complexType>
  </xs:element>

</xs:schema>
