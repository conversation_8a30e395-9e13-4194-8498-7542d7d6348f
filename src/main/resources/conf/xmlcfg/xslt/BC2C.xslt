<?xml version="1.0" encoding="UTF-8"?>
<!--
- author:   zhou yuan
- date:     2024/05/25
- verson:   v1.0
- descprition:
    this file is used to transform the incoming inventory XML file to txt file.
-->
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                xmlns:fn="http://www.w3.org/2005/xpath-functions" exclude-result-prefixes="fn xs xsi xsl">
    <xsl:output method="text" encoding="UTF-8" media-type="text/plain"/>
    <xsl:variable name="sep">
        <xsl:text>!!!!</xsl:text>
    </xsl:variable>
    <xsl:variable name="zero">
        <xsl:text>0</xsl:text>
    </xsl:variable>
    <xsl:variable name="hyphen">
        <xsl:text>-</xsl:text>
    </xsl:variable>
    <xsl:variable name="crlf">
        <xsl:text>&#xd;&#xa;</xsl:text>
    </xsl:variable>
    <xsl:variable name="carr">
        <xsl:text>&#xa;</xsl:text>
    </xsl:variable>
    <xsl:variable name="P2P">
        <xsl:text>P2P</xsl:text>
    </xsl:variable>
    <xsl:variable name="P2C">
        <xsl:text>P2C</xsl:text>
    </xsl:variable>
    <xsl:variable name="PARTNER">
        <xsl:text>PARTNER</xsl:text>
    </xsl:variable>
    <!--
1	BillList	ProvCode
2	BillList	ToCBizList
2.1	ToCBizList	ToCBizInfo
2.1.1	ToCBizInfo	ProductSubsList
2.1.1.1	ProductSubsList	SubProductInfo
2.1.1.1.1	SubProductInfo	ProductSubsID
2.1.1.1.2	SubProductInfo	CloudInstanceID
2.1.1.1.3	SubProductInfo	ProductID
2.1.1.1.4	SubProductInfo	ProductName
2.1.1.1.5	SubProductInfo	DbProductID
2.1.1.1.6	SubProductInfo	DbProductName
2.1.1.1.7	SubProductInfo	GhCode
2.1.1.1.8	SubProductInfo	PVProductClass
2.1.1.1.9	SubProductInfo	IssueTime
2.1.1.1.10	SubProductInfo	ExpireTime
2.1.1.1.11	SubProductInfo	BillingTerm
2.1.1.1.12	SubProductInfo	FeeList
1.1.1.1.12.1	FeeList	FeeInfo

-->
    <xsl:template match="/BillList">
        <xsl:variable name="v_ProvCode" select="ProvCode"/>
        <xsl:for-each select="ToCBizList">
            <xsl:for-each select="ToCBizInfo">
                <xsl:for-each select="ProductSubsList">
                    <xsl:for-each select="SubProductInfo">
                        <xsl:variable name="v_ProductSubsID" select="ProductSubsID"/>
                        <xsl:variable name="v_CloudInstanceID" select="CloudInstanceID"/>
                        <xsl:variable name="v_ProductID" select="ProductID"/>
                        <xsl:variable name="v_ProductName" select="ProductName"/>
                        <xsl:variable name="v_DbProductID" select="DbProductID"/>
                        <xsl:variable name="v_DbProductName" select="DbProductName"/>
                        <xsl:variable name="v_GhCode" select="GhCode"/>
                        <xsl:variable name="v_PVProductClass" select="PVProductClass"/>
                        <xsl:variable name="v_IssueTime" select="IssueTime"/>
                        <xsl:variable name="v_ExpireTime" select="ExpireTime"/>
                        <xsl:variable name="v_BillingTerm" select="BillingTerm"/>
                        <xsl:variable name="pos_SubProductInfo" select="position()"/>
                        <xsl:for-each select="FeeList">
                            <xsl:for-each select="FeeInfo">
                                <xsl:variable name="v_FeeVal" select="FeeVal"/>
                                <xsl:variable name="v_TaxRate" select="TaxRate"/>
                                <xsl:variable name="v_Tax" select="Tax"/>
                                <xsl:variable name="v_FeeNoTax" select="FeeNoTax"/>
                                <xsl:variable name="pos_feeInfo" select="position()"/>
                                <xsl:for-each select="Province2SpecialSettle">
                                    <xsl:for-each select="Province2SpecialSettleinfo">
                                        <xsl:variable name="s_SettlementPartyIn1" select="SettlementPartyIn"/>
                                        <xsl:variable name="s_SettlementPartyOut1" select="SettlementPartyOut"/>
                                        <xsl:variable name="s_SettlementRate1" select="SettlementRate"/>
                                        <xsl:variable name="s_SettlementType1" select="SettlementType"/>
                                        <xsl:variable name="s_SettlementAmount1" select="SettlementAmount"/>
                                        <xsl:variable name="s_SettleClass1" select="SettleClass"/>
                                        <xsl:variable name="s_ReportCode1" select="ReportCode"/>
                                        <xsl:variable name="pos_P2S" select="position()"/>

                                        <xsl:value-of select="$pos_SubProductInfo"/>
                                        <xsl:value-of select="$hyphen"/>
                                        <xsl:value-of select="$pos_feeInfo"/>
                                        <xsl:value-of select="$hyphen"/>
                                        <xsl:value-of select="$pos_P2S"/>
                                        <xsl:value-of select="$sep"/>


                                        <xsl:value-of select="$v_ProvCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductSubsID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_CloudInstanceID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_DbProductID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_DbProductName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_GhCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_PVProductClass"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_IssueTime"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ExpireTime"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_BillingTerm"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$pos_SubProductInfo"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_FeeVal"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_TaxRate"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_Tax"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_FeeNoTax"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementPartyIn1"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementPartyOut1"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementRate1"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementType1"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementAmount1"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettleClass1"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_ReportCode1"/>
                                        <xsl:value-of select="$crlf"/>
                                    </xsl:for-each>
                                </xsl:for-each>

                                <xsl:for-each select="Province2ProvinceSettle">
                                    <xsl:for-each select="Province2Provinceinfo">
                                        <xsl:variable name="s_SettlementPartyIn2" select="SettlementPartyIn"/>
                                        <xsl:variable name="s_SettlementPartyOut2" select="SettlementPartyOut"/>
                                        <xsl:variable name="s_SettlementAmount2" select="SettlementAmount"/>
                                        <xsl:variable name="s_SettlementType2" select="SettlementType"/>
                                        <xsl:variable name="s_SettlementRate2" select="SettlementRate"/>
                                        <xsl:variable name="pos_P2P" select="position()"/>

                                        <xsl:value-of select="$pos_SubProductInfo"/>
                                        <xsl:value-of select="$hyphen"/>
                                        <xsl:value-of select="$pos_feeInfo"/>
                                        <xsl:value-of select="$hyphen"/>
                                        <xsl:value-of select="$pos_P2P"/>
                                        <xsl:value-of select="$sep"/>

                                        <xsl:value-of select="$v_ProvCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductSubsID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_CloudInstanceID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_DbProductID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_DbProductName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_GhCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_PVProductClass"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_IssueTime"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ExpireTime"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_BillingTerm"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$pos_SubProductInfo"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_FeeVal"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_TaxRate"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_Tax"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_FeeNoTax"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementPartyIn2"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementPartyOut2"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementAmount2"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementType2"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementRate2"/>
                                        <xsl:value-of select="$crlf"/>
                                    </xsl:for-each>
                                </xsl:for-each>

                                <xsl:for-each select="ParSettle">
                                    <xsl:for-each select="ParSettleInfo">
                                        <xsl:variable name="s_PartnerCode" select="PartnerCode"/>
                                        <xsl:variable name="s_PartnerName" select="PartnerName"/>
                                        <xsl:variable name="s_ParSettleRate" select="ParSettleRate"/>
                                        <xsl:variable name="s_SettlementType3" select="SettlementType"/>
                                        <xsl:variable name="s_ParSettlAmount" select="ParSettlAmount"/>
                                        <xsl:variable name="s_ParResSettlRate" select="ParResSettlRate"/>
                                        <xsl:variable name="pos_PARTNER" select="position()"/>

                                        <xsl:value-of select="$pos_SubProductInfo"/>
                                        <xsl:value-of select="$hyphen"/>
                                        <xsl:value-of select="$pos_feeInfo"/>
                                        <xsl:value-of select="$hyphen"/>
                                        <xsl:value-of select="$pos_PARTNER"/>
                                        <xsl:value-of select="$sep"/>

                                        <xsl:value-of select="$v_ProvCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductSubsID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_CloudInstanceID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ProductName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_DbProductID"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_DbProductName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_GhCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_PVProductClass"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_IssueTime"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_ExpireTime"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_BillingTerm"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$pos_SubProductInfo"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_FeeVal"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_TaxRate"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_Tax"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$v_FeeNoTax"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_PartnerCode"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_PartnerName"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_ParSettleRate"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_SettlementType3"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_ParSettlAmount"/>
                                        <xsl:value-of select="$sep"/>
                                        <xsl:value-of select="$s_ParResSettlRate"/>
                                        <xsl:value-of select="$crlf"/>
                                    </xsl:for-each>
                                </xsl:for-each>
                            </xsl:for-each>
                        </xsl:for-each>
                    </xsl:for-each>
                </xsl:for-each>
            </xsl:for-each>
        </xsl:for-each>
    </xsl:template>
</xsl:stylesheet>
