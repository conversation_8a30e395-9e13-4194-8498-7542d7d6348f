package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Batch;

/**
 * 允许实现类在处理每个批次之前/之后得到通知
 * 异常处理由实现类完成
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public interface BatchListener<P> {

    /**
     * 在读取每个批次之前执行
     */
    default void beforeBatchReading() {
        // no-op
    }

    /**
     * 每批处理完后执行
     *
     * @param batch 已处理的一批记录
     */
    default void afterBatchProcessing(final Batch<P> batch) {
        // no-op
    }

    /**
     * 每批写入成功后执行
     *
     * @param batch 已写入的一批记录
     */
    default void afterBatchWriting(final Batch<P> batch) {
        // no-op
    }

    /**
     * 每批写入过程中发生错误时执行
     *
     * @param batch     试图写入的批次
     * @param throwable 发生错误
     */
    default void onBatchWritingException(final Batch<P> batch, Throwable throwable) {
        // no-op
    }

}
