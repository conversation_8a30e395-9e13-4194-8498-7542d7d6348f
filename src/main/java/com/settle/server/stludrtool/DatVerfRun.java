package com.settle.server.stludrtool;

import java.io.File;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import com.settle.server.pojo.JingFenSettleModel;
import com.settle.server.utils.ConfUtil;
import com.settle.server.utils.FileTool;
import com.settle.server.utils.TimeTool;
import com.settle.server.utils.VerfTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.scheduling.annotation.Async;

@Slf4j
public class DatVerfRun {
	private static final String SQL = "INSERT INTO SYNC_BA_INTERFACE(FILENAME,BA_NUM,BA_MONTH,PROV_CODE,CHARGE_CODE,BA_TAXFEE)VALUES(?,?,?,?,?,?)";
	private static final int _COMMIT = 10000;

	@SuppressWarnings("unchecked")
	@Async
	public void insertDBData(String month) {
		FileTool ft = new FileTool();
		Map<String, Object> map = ft.getMapFiles(new File(ConfUtil._49016_PATH), month);
		List<String> datList = (List<String>) map.get(FileTool.DAT);
		String verf = (String) map.get(FileTool.VERF);
		if (null == verf || datList.size() == 0) {
			log.error("*** *** 无需要入库的数据文件! *** ***");
			LogRun.logError(month, verf, "*** *** 无需要入库的数据文件! *** ***");
			return;
		}
		VerfTool vt = new VerfTool(month, datList, verf);
		boolean flag = vt.verification();

		try {
			if (flag) {
				deleteData(month, verf);
				for (int i = 0; i < ((List<String>) map.get(FileTool.DAT)).size(); i++) {
					File file = new File(((List<String>) map.get(FileTool.DAT)).get(i));
					String fileName = ((List<String>) map.get(FileTool.DAT)).get(i);
					log.info("开始导入数据文件: " + fileName);
					LogRun.logInfo(month, fileName, "开始导入数据文件: " + fileName);
					insertData(month, file);
					LogRun.logInfo(month, fileName, "数据文件: " + fileName + " 导入完成 ");
					log.info("数据文件: " + ((List<String>) map.get(FileTool.DAT)).get(i) + " 导入完成");
					FileUtils.moveFileToDirectory(file, new File(ConfUtil._49016_PATH_BAK), true);
				}
				FileUtils.moveFileToDirectory(new File(verf), new File(ConfUtil._49016_PATH_BAK), true);
				LogRun.logInfo(month, verf, "*** *** 经分传给BBOSS数据文件导入完成! *** ***");
			}else{
				for (int i = 0; i < ((List<String>) map.get(FileTool.DAT)).size(); i++) {
					File file = new File(((List<String>) map.get(FileTool.DAT)).get(i));
					FileUtils.moveFileToDirectory(file, new File(ConfUtil._49016_PATH_ERRBAK), true);
				}
				FileUtils.moveFileToDirectory(new File(verf), new File(ConfUtil._49016_PATH_ERRBAK), true);
			}
		} catch (IOException e) {
			log.error(e.getMessage());
			LogRun.logError(month, verf, e.getMessage());
			return;
		}
	}

	private boolean insertData(String month, File file) {
		long start = System.currentTimeMillis();
		int count = 0;
		StludrToolDBUtil ju = new StludrToolDBUtil();
		String fileName = file.getName();
		try {
			List<String> list = FileUtils.readLines(file, "UTF-8");
			ju.getConn().setAutoCommit(false);
			ju.setPre(ju.getConn().prepareStatement(SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_READ_ONLY));
			for (int i = 0; i < list.size(); i++) {
				JingFenSettleModel jfsm = new JingFenSettleModel(list.get(i));
				ju.getPre().setString(1, fileName);
				ju.getPre().setInt(2, jfsm.getNumber());
				ju.getPre().setString(3, jfsm.getMonth());
				ju.getPre().setString(4, jfsm.getProvince());
				ju.getPre().setString(5, jfsm.getChargeCode());
				ju.getPre().setDouble(6, jfsm.getTaxfee());
				ju.getPre().addBatch();
				count++;
				if (count % _COMMIT == 0) {
					ju.getPre().executeBatch();
					ju.getConn().commit();
					log.debug("*** *** 导入数据条数: " + count + " *** ***");
					LogRun.logDebug(month, file.getAbsolutePath(), "*** *** 导入数据条数: " + count + " *** ***");
				}
			}
			ju.getPre().executeBatch();
			ju.getConn().commit();
			log.debug("*** *** 总共导入数据条数: " + count + " *** ***");
			LogRun.logDebug(month, file.getAbsolutePath(), "*** *** 总共导入数据条数: " + count + " *** ***");
			ju.close();
		} catch (SQLException e) {
			log.error(e.getMessage());
			LogRun.logError(month, file.getAbsolutePath(), e.getMessage());
			return false;
		} catch (IOException e) {
			log.error(e.getMessage());
			LogRun.logError(month, file.getAbsolutePath(), e.getMessage());
			return false;
		}
		long stop = System.currentTimeMillis();
		log.info("*** *** 数据入库耗时: " + TimeTool.toTime(stop - start) + " *** ***");
		LogRun.logDebug(month, file.getAbsolutePath(), "*** *** 数据入库耗时: " + TimeTool.toTime(stop - start) + " *** ***");
		return true;
	}

	private boolean deleteData(String month, String verf) {
		StludrToolDBUtil ju = new StludrToolDBUtil();
		try {
			ju.setPre(ju.getConn().prepareStatement("DELETE FROM SYNC_BA_INTERFACE WHERE BA_MONTH=?"));
			ju.getPre().setString(1, month);
			ju.getPre().execute();
			LogRun.logInfo(month, verf, "*** *** 清除" + month + "账期的经分传给BBOSS数据! *** ***");
			return true;
		} catch (SQLException e) {
			log.error(e.getMessage());
			LogRun.logError(month, verf, e.getMessage());
			return false;
		} finally {
			ju.close();
		}
	}
}
