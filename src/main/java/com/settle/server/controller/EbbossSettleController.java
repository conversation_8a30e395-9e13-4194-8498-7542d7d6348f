package com.settle.server.controller;

import com.settle.server.module.esp.dto.ResultDate;
import com.settle.server.service.EbossFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/20
 * @since 1.0.0
 */
@RestController
@RequestMapping("/EbbossSettle")
@Slf4j
@Api(tags = "省/政企结算单")
public class EbbossSettleController {

    @Autowired
    private EbossFileService mobileCloudService;


    @PostMapping("/ebossMCSettleHandler")
    @ApiOperation(value = "省/政企4.16结算单", notes = "省/政企4.16结算单")
    public ResultDate ebossMCSettleHandler(String acctMonth) {
        log.info("start up 4.16...");
        mobileCloudService.processCloud_4_16(acctMonth);
        log.info("end 4.16...");
        return ResultDate.success();
    }

    @PostMapping("/ebossPVBILLSettleHandler")
    @ApiOperation(value = "省/政企4.29结算单", notes = "省/政企4.29结算单")
    public ResultDate ebossPVBILLSettleHandler(String acctMonth) {
        log.info("start up 4.29...");
        mobileCloudService.processCloud_4_29(acctMonth);
        log.info("end 4.29...");
        return ResultDate.success();
    }

    @PostMapping("/ebossPVSETTLESettleHandler")
    @ApiOperation(value = "省/政企4.30结算单", notes = "省/政企4.30结算单")
    public ResultDate ebossPVSETTLESettleHandler(String acctMonth) {
        log.info("start up 4.30...");
        mobileCloudService.processCloud_4_30(acctMonth);
        log.info("end 4.30...");
        return ResultDate.success();
    }

    @PostMapping("/ebossESPSettleHandler")
    @ApiOperation(value = "省/政企4.6结算单", notes = "省/政企4.6结算单")
    public ResultDate ebossESPSettleHandler(String acctMonth) {
        log.info("start up 4.6...");
        mobileCloudService.process4_6(acctMonth);
        log.info("end 4.6...");
        return ResultDate.success();
    }

    @GetMapping(value = "/saveBCTOC")
    @ApiOperation(value = "省/政企4.31结算单", notes = "省/政企4.31结算单")
    public String bcSave(@RequestParam(value = "acctMonth") String acctMonth) {
        try {
            log.info("start up 4.31...");
            mobileCloudService.saveBCTOC(acctMonth);
            log.info("end 4.31...");
            return "success";
        } catch (Exception e) {
            log.error("McController ---> save error:[{}]", e.getMessage(), e);
            return e.getMessage();
        }
    }
}