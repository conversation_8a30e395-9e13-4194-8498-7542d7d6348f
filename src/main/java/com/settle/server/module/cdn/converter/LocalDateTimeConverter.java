package com.settle.server.module.cdn.converter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * {@link LocalDateTime} 类型转换器
 * 将 {@link DateTimeFormatter#ISO_LOCAL_DATE_TIME} 格式的字符串日期时间转换为 {@link LocalDateTime} 类型
 *
 * <AUTHOR>
 */
public class LocalDateTimeConverter implements TypeConverter<String, LocalDateTime> {

    @Override
    public LocalDateTime convert(String value) {
        return LocalDateTime.parse(value);
    }
}
