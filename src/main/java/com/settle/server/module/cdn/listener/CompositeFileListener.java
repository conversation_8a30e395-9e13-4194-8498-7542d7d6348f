package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.job.JobMetrics;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * <AUTHOR>
 */
public class CompositeFileListener implements FileListener {

    private List<FileListener> listeners;

    /**
     * 创建一个新的 {@link CompositeFileListener}
     */
    public CompositeFileListener() {
        this(new ArrayList<>());
    }

    /**
     * 创建一个新的 {@link CompositeFileListener}
     *
     * @param listeners 委托
     */
    public CompositeFileListener(List<FileListener> listeners) {
        this.listeners = listeners;
    }

    @Override
    public void beforeFileProcessing() {
        for (FileListener listener : listeners) {
            listener.beforeFileProcessing();
        }
    }

    @Override
    public void afterFileProcessing(JobMetrics jobMetrics) {
        for (ListIterator<FileListener> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().afterFileProcessing(jobMetrics);
        }
    }

    @Override
    public void onFileProcessingException(Throwable throwable) {
        for (ListIterator<FileListener> iterator = listeners.listIterator(listeners.size()); iterator.hasPrevious(); ) {
            iterator.previous().onFileProcessingException(throwable);
        }
    }

    /**
     * 添加委托监听器
     *
     * @param fileListener 要添加文件监听器
     */
    public void addFileListener(FileListener fileListener) {
        listeners.add(fileListener);
    }
}
