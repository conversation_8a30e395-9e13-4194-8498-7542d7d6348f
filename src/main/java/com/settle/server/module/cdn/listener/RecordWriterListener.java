package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Batch;

/**
 * 使实现类在写入一批记录之前/之后得到通知
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public interface RecordWriterListener<P> {

    /**
     * 在写入每批记录之前调用
     *
     * @param batch 待写
     */
    default void beforeRecordWriting(Batch<P> batch) {
        // no-op
    }

    /**
     * 在写入每批记录后调用
     *
     * @param batch 已写入的记录
     */
    default void afterRecordWriting(Batch<P> batch) {
        // no-op
    }

    /**
     * 在批量写入过程中发生异常时调用
     *
     * @param batch     试图写
     * @param throwable 在记录写入期间抛出的 throwable
     */
    default void onRecordWritingException(Batch<P> batch, final Throwable throwable) {
        // no-op
    }

}
