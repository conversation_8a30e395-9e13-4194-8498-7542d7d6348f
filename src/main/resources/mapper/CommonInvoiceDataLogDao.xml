<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.settle.server.dao.bossAcct.CommonInvoiceDataLogDao">
    <insert id="save">
        INSERT INTO COMMON_INVOICE_DATA_FILE_LOG
            (ID, FILE_NAME, FILE_PATH, FILE_TYPE, ACCT_MONTH, CREATE_DATE)
        VALUES (#{id}, #{fileName}, #{filePath}, #{fileType}, #{acctMonth}, sysdate)
    </insert>
    <insert id="saveBatch">
        INSERT INTO COMMON_INVOICE_DATA_FILE_LOG (ID, FILE_NAME, FILE_PATH, FILE_TYPE, ACCT_MONTH, CREATE_DATE)VALUES
        <foreach collection="logs" item="item" index="index" separator=",">
            (#{item.id,},#{item.fileName},#{item.filePath},#{item.fileType},#{item.acctMonth},#{item.createDate})
        </foreach>
    </insert>
    <delete id="deleteLog">
        delete from COMMON_INVOICE_DATA_FILE_LOG where ACCT_MONTH=#{acctMonth} and FILE_TYPE in
        <foreach collection="rkeys" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>