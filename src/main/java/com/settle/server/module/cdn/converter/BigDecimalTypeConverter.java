package com.settle.server.module.cdn.converter;

import com.settle.server.utils.Utils;

import java.math.BigDecimal;

/**
 * BigDecimal 类型装换器
 * 不接受 {@code null} 或空字符串
 *
 * <AUTHOR>
 */
public class BigDecimalTypeConverter implements TypeConverter<String, BigDecimal> {

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal convert(final String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        return new BigDecimal(value);
    }

}
