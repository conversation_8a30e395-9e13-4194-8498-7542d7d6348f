package com.settle.server.module.cdn.converter;


import com.settle.server.utils.Utils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * AtomicInteger 类型转换器
 * 不接受 {@code null} 或空字符串
 *
 * <AUTHOR>
 */
public class AtomicIntegerTypeConverter implements TypeConverter<String, AtomicInteger> {

    /**
     * {@inheritDoc}
     */
    @Override
    public AtomicInteger convert(final String value) {
        Utils.checkArgument(value != null, "Value to convert must not be null");
        Utils.checkArgument(!value.isEmpty(), "Value to convert must not be empty");
        return new AtomicInteger(Integer.parseInt(value));
    }

}
