<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 被2013 sp1 () 使用XMLSpy v编辑的 (http://www.altova.com) by -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="BillList">
		<xs:annotation>
			<xs:documentation>Comment describing your root element</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:all>
				<xs:element name="ProvCode">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="3"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="PayTag">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:length value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="ECList">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ECInfo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:all>
										<xs:element name="GroupCustomerNumber" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="GroupCustomerName" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="EBOSSCustomerNumber">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="EBOSSCustomerName">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="AddressProvCode">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:length value="3"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="InnerECFlag">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:length value="1"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="EcDepartmentName"  minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="ECCreatorName" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="ECCreatorTel" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="32"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="FeeList">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="FeeInfo" maxOccurs="unbounded">
														<xs:complexType>
															<xs:all>
																<xs:element name="AccountID" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="32"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="AccountName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="SubsID">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="32"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductClassName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductDetailName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductID">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ProductName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="MainContract" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="RunDepartmentName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ICTFLAG" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="RateplanID" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="RateplanName" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="FeeVal" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="16"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="TaxRate" type="xs:float"/>
																<xs:element name="Tax" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="16"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="FeeNoTax" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="16"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="FeeFlag">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="OriginalBillMonth" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="DiscountAmount" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="16"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="StandardFee" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="16"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="SettleFee" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:integer">
																			<xs:totalDigits value="16"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="Province2SpecialSettle" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="Province2SpecialSettleinfo" minOccurs="0" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:all>
																						<xs:element name="SettlementPartyIn" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementPartyOut" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementRate" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementType" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementAmount">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettleClass" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																					</xs:all>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="Province2ProvinceSettle" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="Province2Provinceinfo" minOccurs="0" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:all>
																						<xs:element name="SettlementPartyIn">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementPartyOut">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementAmount">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementType" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="20"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementRate" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="8"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="Remark" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																					</xs:all>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="ParSettle" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="ParSettleInfo" minOccurs="0" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:all>
																						<xs:element name="PartnerCode" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="50"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="PartnerName" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="256"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ParSettleRate" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="SettlementType" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ParSettlAmount" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:integer">
																									<xs:totalDigits value="16"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ParResSettlRate" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="ParSettlePayType" minOccurs="0">
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																					</xs:all>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="BillingTerm">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="PayTerm" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="SettleItem" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="64"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="BusiMode" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="64"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="Chargecode">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="32"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="ChargeCodeName">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="64"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="CityCode" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="32"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="IniPrice" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="SettlePrice" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="DbProductCode" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="0"/>
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="DbProdChargeCode" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="0"/>
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:all>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:all>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:all>
		</xs:complexType>
	</xs:element>
</xs:schema>
