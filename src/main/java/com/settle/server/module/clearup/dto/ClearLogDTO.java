package com.settle.server.module.clearup.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/22
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
public class ClearLogDTO {

    private String dbName;
    private String tableName;
    private List<String> partition;
    // 分区类型，day:天分区，month:月分区
    private String partitionType;

    private String range;

    public ClearLogDTO(String dbName, String tableName, List<String> partition) {
        this.dbName = dbName;
        this.tableName = tableName;
        this.partition = partition;
    }
}