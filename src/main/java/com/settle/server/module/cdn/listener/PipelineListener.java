package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.record.Record;

/**
 * 使实现类在处理管道之前/之后得到通知
 *
 * <AUTHOR>
 */
public interface PipelineListener {

    /**
     * 在处理记录之前调用
     * 如果创建新记录，则<strong>必须</strong>保留修改后记录的原始标题
     *
     * @param record 将被处理的记录
     * @return 预处理记录
     */
    default <P> Record<P> beforeRecordProcessing(final Record<P> record) {
        return record;
    }

    /**
     * 在处理记录后调用
     *
     * @param inputRecord  要处理的记录
     * @param outputRecord 处理的记录，如果记录已被过滤，则可能为null
     */
    default <P> void afterRecordProcessing(final Record<P> inputRecord, final Record<P> outputRecord) {
        // no-op
    }

    /**
     * 在记录处理过程中发生异常时调用
     *
     * @param record    试图处理的记录
     * @param throwable 在记录处理期间抛出的 throwable
     */
    default <P> void onRecordProcessingException(final Record<P> record, final Throwable throwable) {
        // no-op
    }

}
