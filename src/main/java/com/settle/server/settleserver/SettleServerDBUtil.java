package com.settle.server.settleserver;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

@Slf4j
@Component
public class SettleServerDBUtil {
  public static String driverClassName;
  public static String url;
  public static String username;
  public static String password;

  @Value("${jdbc.sttleserver.driverClassName}")
  public void setDriverClassName(String driverClassNameValue){
    driverClassName = driverClassNameValue;
  }
  @Value("${jdbc.sttleserver.url}")
  public  void setUrl(String urlValue) {
    url = urlValue;
  }
  @Value("${jdbc.sttleserver.username}")
  public  void setUsername(String usernameValue) {
    username = usernameValue;
  }
  @Value("${jdbc.sttleserver.password}")
  public  void setPassword(String passwordValue) {
    password = passwordValue;
  }

  
  public static Connection Connection() {
    Connection connection = null;
    try {
      Class.forName(driverClassName).newInstance();
      connection = DriverManager.getConnection(url, username, /*DecipherUtil.decoder(password)*/password);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return connection;
  }

  public static void Close(Connection connection) {
    try {
      if (connection != null)
        connection.close();
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  public static void Close(Statement statement) {
    try {
      if (statement != null)
        statement.close();
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  public static void Close(ResultSet resultSet) {
    try {
      if (resultSet != null)
        resultSet.close();
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  public static void Rollback(Connection connection) {
    try {
      if (connection != null)
        connection.rollback();
    }
    catch (SQLException e) {
      log.error(e.getMessage(), e);
    }
  }
}