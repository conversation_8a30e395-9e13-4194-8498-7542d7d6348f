package com.settle.server.utils;

import cn.hutool.extra.mail.MailAccount;
import org.junit.jupiter.api.Test;

import java.io.File;

public class MailUtil {
    @Test
    public void sendEmail(){

//        ZipUtil.zip("\\home\\appop\\workspace\\word\\data\\结算说明201903", "\\home\\appop\\workspace\\word\\结算说明201903.zip");
//        System.out.println(1);
//        // 配置邮件信息
        MailAccount mailAccount = new MailAccount();
        mailAccount.setHost("smtp.139.com"); // 设置SMTP服务器地址
        mailAccount.setPort(25); // 设置SMTP服务器端口
        mailAccount.setAuth(true); // 需要验证
        mailAccount.setFrom("<EMAIL>"); // 发件人邮箱
        mailAccount.setUser("xsl_soul"); // 发件人账号
        mailAccount.setPass("9856dbdabadc10f26d00"); // 发件人授权码
        // 邮件信息
        String to = "<EMAIL>"; // 收件人邮箱
        String subject = "邮件主题"; // 邮件主题
        String content = "邮件正文"; // 邮件正文
        String filePath = "/path/to/your/attachment.pdf"; // 附件路径

        // 发送邮件
        File file = new File("\"D:\\home\\appop\\workspace\\SettleData\\zipFile\\结算说明202405.zip\"");
        File absoluteFile = file.getAbsoluteFile();
        String path = file.getPath();
        cn.hutool.extra.mail.MailUtil.send(mailAccount, to, subject, content, false,
                file);
    }
}
