package com.settle.server.xmlTransform.xml2text;

import java.io.*;
import java.util.Properties;

import com.settle.server.utils.FileUtil;
import com.settle.server.xmlTransform.XMLTransformDBUtil;
import com.settle.server.xmlTransform.xml2text.config.*;
import lombok.extern.slf4j.Slf4j;
import org.exolab.castor.xml.*;

@Slf4j
public class Config {
	private static Basepath[] _basepathArray;

	private static Entry[] _entryArray;

	public static int getEntryCount() {
		return _entryArray.length;
	}

	public static Entry getEntry(int index) {
		return _entryArray[index];
	}

	private static Basepath getBasepath(String name) {
		for (int i = 0; i < _basepathArray.length; i++) {
			if (_basepathArray[i].getName().equals(name))
				return _basepathArray[i];
		}

		return null;
	}

	private static void replaceEnv(Basepath node) {
		node.setIn(FileUtil.getEnvPath(node.getIn()));
		node.setOut(FileUtil.getEnvPath(node.getOut()));
		node.setBack(FileUtil.getEnvPath(node.getBack()));
		node.setResp(FileUtil.getEnvPath(node.getResp()));
		node.setXslt(FileUtil.getEnvPath(node.getXslt()));
		node.setXsd(FileUtil.getEnvPath(node.getXsd()));
	}

	private static void appendEndSep(Basepath node) {
		node.setIn(FileUtil.addEndSep(node.getIn()));
		node.setOut(FileUtil.addEndSep(node.getOut()));
		node.setBack(FileUtil.addEndSep(node.getBack()));
		node.setResp(FileUtil.addEndSep(node.getResp()));
		node.setXslt(FileUtil.addEndSep(node.getXslt()));
		node.setXsd(FileUtil.addEndSep(node.getXsd()));
	}

	private static boolean checkBasepath(Basepath node) {
		if (!FileUtil.checkPath(node.getIn()))
			return false;

		if (!FileUtil.checkPath(node.getOut()))
			return false;

		if (!FileUtil.checkPath(node.getBack()))
			return false;

		if (!FileUtil.checkPath(node.getResp()))
			return false;

		if (!FileUtil.checkPath(node.getXslt()))
			return false;

		if (!FileUtil.checkPath(node.getXsd()))
			return false;

		return true;
	}

	private static void addBasepath(Entry entry, Basepath parent) {
		entry.setIn(FileUtil.addEndSep(parent.getIn() + entry.getIn()));
		entry.setOut(FileUtil.addEndSep(parent.getOut() + entry.getOut()));
		entry.setBack(FileUtil.addEndSep(parent.getBack() + entry.getBack()));
		entry.setResp(FileUtil.addEndSep(parent.getResp() + entry.getResp()));

		entry.setXslt(parent.getXslt() + entry.getXslt());
		entry.setXsd(parent.getXsd() + entry.getXsd());
	}

	private static boolean checkEntry(Entry node) {
		if (!FileUtil.checkPath(node.getIn()))
			return false;

		if (!FileUtil.checkPath(node.getOut()))
			return false;

		if (!FileUtil.checkPath(node.getBack()))
			return false;

		if (!FileUtil.checkPath(node.getResp()))
			return false;

		if (!FileUtil.checkFile(node.getXslt()))
			return false;

		if (!FileUtil.checkFile(node.getXsd()))
			return false;

		return true;
	}

	public static boolean init(String cfgFile) {
		try {
			FileReader xmlfile = null;
			FileInputStream istream = null;
			try {
				xmlfile = new FileReader(cfgFile);
				Configure cfgNode = (Configure) Configure.unmarshal(xmlfile);

				if (!cfgNode.isValid()) {
					System.out.println("Invalid config file: " + cfgFile);
					return false;
				}

				int i;

				Paths pathsNode = cfgNode.getPaths();
				String logFile = pathsNode.getLog4j();
				String dbConf = pathsNode.getDBConf();
				logFile = FileUtil.getEnvPath(logFile);
				dbConf = FileUtil.getEnvPath(dbConf);
				if (!FileUtil.isFile(logFile)) {
					System.out
							.println("Log4j file dose not exist : " + logFile);
					System.out.println("Invalid config file: " + cfgFile);
					return false;
				}

				Properties props = new Properties();
				try {
					istream = new FileInputStream(logFile);
					props.load(istream);

				} finally {
					if (istream != null) {
						istream.close();
					}
				}

				if (!FileUtil.isFile(dbConf)) {
					log.error(
							"DB config file dose not exist : " + dbConf);
					log.error("Invalid config file: " + dbConf);
					return false;
				}
				XMLTransformDBUtil.setConfPath(dbConf);

				String lFile = props.getProperty("log4j.appender.R.File");// ����·��
				lFile = FileUtil.getEnvPath(lFile);
				props.setProperty("log4j.appender.R.File", lFile);

//				PropertyConfigurator.configure(props);// װ��log4j������Ϣ

				// DOMConfigurator.configure(logFile);

				_basepathArray = pathsNode.getBasepath();
				for (i = 0; i < _basepathArray.length; i++) {
					replaceEnv(_basepathArray[i]);
					appendEndSep(_basepathArray[i]);
					if (!checkBasepath(_basepathArray[i])) {
						log.error(
								"Invalid config file: " + cfgFile);
						return false;
					}
				}

				Category cgNode = cfgNode
						.getCategory();
				_entryArray = cgNode.getEntry();
				for (i = 0; i < _entryArray.length; i++) {
					Basepath basepath = getBasepath(_entryArray[i]
							.getBasepath());
					if (basepath == null) {
						log.error(
								"Can not find Basepath of Entry: "
										+ _entryArray[i].getBasepath());
						log.error(
								"Invalid config file: " + cfgFile);
						return false;
					}

					addBasepath(_entryArray[i], basepath);
					if (!checkEntry(_entryArray[i])) {
						log.error(
								"Invalid config file: " + cfgFile);
						return false;
					}
				}

			} finally {
				if (xmlfile != null)
					xmlfile.close();
			}
		} catch (ValidationException ex) {
			log.error(
					">>>>>> PROCESSING ValidationException......");
			log.error(ex.getMessage());
			// ex.printStackTrace();
			return false;
		} catch (MarshalException ex1) {
			log.error(">>>>>> PROCESSING MarshalException......");
			log.error(ex1.getMessage());
			// ex1.printStackTrace();
			return false;
		} catch (FileNotFoundException ex) {
			log.error(
					">>>>>> PROCESSING FileNotFoundException......");
			log.error(ex.getMessage());
			// ex.printStackTrace();
			return false;
		} catch (IOException ex) {
			log.error(">>>>>> PROCESSING IOException......");
			log.error(ex.getMessage());
			// ex.printStackTrace();
			return false;
		}

		return true;
	}
}