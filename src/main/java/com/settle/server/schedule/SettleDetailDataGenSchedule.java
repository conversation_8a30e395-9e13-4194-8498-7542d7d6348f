package com.settle.server.schedule;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Splitter;
import com.settle.server.dao.stludr.UdmDataFor45053Dao;
import com.settle.server.service.impl.SettleDataGenServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 *  BIL-ZQ-202501-31-双跨专线补充报表界面支持分省下载
 */
@Component
@Slf4j
public class SettleDetailDataGenSchedule {

    @Autowired
    private SettleDataGenServiceImpl service;

    @Autowired
    private UdmDataFor45053Dao udmDataFor45053Dao;

    @XxlJob("specialLineDataGenerate")
    public ReturnT<String> handleSpecialLineData() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("双跨文件生成开始，接受参数：{}", param);
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if(StringUtils.isBlank(acctMonth)){
                LocalDateTime now = LocalDateTime.now(); // 获取当前日期和时间
                LocalDateTime localDateTime = now.minusMonths(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth =  localDateTime.format(formatter);
            }
            String rkey = entries.getStr("rkey");
            if (StrUtil.isBlank(rkey)){
                XxlJobHelper.log("rkey不能为空");
                return ReturnT.FAIL;
            }
            List<String> rkeys = Splitter.on(",").splitToList(rkey);
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            boolean isSuccess=service.handleSpecialLineData(acctMonth,rkeys);
            if (!isSuccess){
                XxlJobHelper.log("双跨文件处理失败");
                returnT = ReturnT.FAIL;
            }
            XxlJobHelper.log("双跨文件处理结束");
        }catch (RuntimeException re){
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("双跨文件处理异常,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

    @XxlJob("fiveGDataGenerate")
    public ReturnT<String> handleFiveGDataGenerate() {
        ReturnT<String> returnT = ReturnT.SUCCESS;
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("5g消息文件生成开始，接受参数：{}", param);
            if (StringUtils.isBlank(param)) {
                param = "{}";
            }
            JSONObject entries = JSONUtil.parseObj(param);
            String acctMonth = entries.getStr("acctMonth");
            if(StringUtils.isBlank(acctMonth)){
                LocalDateTime now = LocalDateTime.now(); // 获取当前日期和时间
                LocalDateTime localDateTime = now.minusMonths(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                acctMonth =  localDateTime.format(formatter);
            }
            log.info("开始处理FiveG45053报表明细数据信息！");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("fiveGDataGenerate");
            udmDataFor45053Dao.deleteFor45053();
            udmDataFor45053Dao.deleteCustomerInfo();
            udmDataFor45053Dao.insertIntoCustomerInfo(acctMonth);
            udmDataFor45053Dao.updateCustomerInfoLocationName();
/*
            udmDataFor45053Dao.deleteTempMaapmmaInfo();
            udmDataFor45053Dao.insertTempMaapmmaInfo(acctMonth);*/
            udmDataFor45053Dao.insertDataAll(acctMonth);
            stopWatch.stop();
            log.info("将中间表合并到话单表处理完成，总耗时：{}ms", stopWatch.getTotalTimeMillis());
            String rkey = entries.getStr("rkey");
            if (StrUtil.isBlank(rkey)){
                XxlJobHelper.log("rkey不能为空");
                return ReturnT.FAIL;
            }
            List<String> rkeys = Splitter.on(",").splitToList(rkey);
            XxlJobHelper.log("当前账期：{}，开始执行定时任务，定时任务执行时间: {}" , acctMonth,LocalDateTime.now());
            boolean isSuccess=service.handleSpecialLineData(acctMonth,rkeys);
            if (!isSuccess){
                XxlJobHelper.log("5g消息文件处理失败");
                returnT = ReturnT.FAIL;
            }
            XxlJobHelper.log("5g消息文件处理结束");
        }catch (RuntimeException re){
            String message = ExceptionUtils.getStackTrace(re);
            XxlJobHelper.log("5g消息文件处理异常,原因:{}", message);
            returnT = ReturnT.FAIL;
        }
        return returnT;
    }

}
