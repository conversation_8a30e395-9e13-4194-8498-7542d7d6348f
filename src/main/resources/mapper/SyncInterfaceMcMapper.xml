<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.SyncInterfaceMcDao">

  <resultMap type="com.settle.server.entity.SyncInterfaceMc" id="SyncInterfaceMcMap">
    <result property="id" column="ID" jdbcType="VARCHAR"/>
    <result property="provCode" column="PROV_CODE" jdbcType="VARCHAR"/>
    <result property="payTag" column="PAY_TAG" jdbcType="VARCHAR"/>
    <result property="customerProvinceNumber" column="CUSTOMER_PROVINCE_NUMBER" jdbcType="VARCHAR"/>
    <result property="customerName" column="CUSTOMER_NAME" jdbcType="VARCHAR"/>
    <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
    <result property="creatorName" column="CREATOR_NAME" jdbcType="VARCHAR"/>
    <result property="orderMode" column="ORDER_MODE" jdbcType="VARCHAR"/>
    <result property="poSubsId" column="PO_SUBS_ID" jdbcType="VARCHAR"/>
    <result property="poId" column="PO_ID" jdbcType="VARCHAR"/>
    <result property="poName" column="PO_NAME" jdbcType="VARCHAR"/>
    <result property="productSubsId" column="PRODUCT_SUBS_ID" jdbcType="VARCHAR"/>
    <result property="productId" column="PRODUCT_ID" jdbcType="VARCHAR"/>
    <result property="productName" column="PRODUCT_NAME" jdbcType="VARCHAR"/>
    <result property="dbProductId" column="DB_PRODUCT_ID" jdbcType="VARCHAR"/>
    <result property="dbProductName" column="DB_PRODUCT_NAME" jdbcType="VARCHAR"/>
    <result property="coProductId" column="CO_PRODUCT_ID" jdbcType="VARCHAR"/>
    <result property="blProductId" column="BL_PRODUCT_ID" jdbcType="VARCHAR"/>
    <result property="oneProductId" column="ONE_PRODUCT_ID" jdbcType="VARCHAR"/>
    <result property="billingTerm" column="BILLING_TERM" jdbcType="VARCHAR"/>
    <result property="payTerm" column="PAY_TERM" jdbcType="VARCHAR"/>
    <result property="prodChargeCode" column="PROD_CHARGE_CODE" jdbcType="VARCHAR"/>
    <result property="poChargeCodeName" column="PO_CHARGE_CODE_NAME" jdbcType="VARCHAR"/>
    <result property="oneProChargeCode" column="ONE_PRO_CHARGE_CODE" jdbcType="VARCHAR"/>
    <result property="oneProChargeName" column="ONE_PRO_CHARGE_NAME" jdbcType="VARCHAR"/>
    <result property="dbProdChargeCode" column="DB_PROD_CHARGE_CODE" jdbcType="VARCHAR"/>
    <result property="feeVal" column="FEE_VAL" jdbcType="VARCHAR"/>
    <result property="taxRate" column="TAX_RATE" jdbcType="VARCHAR"/>
    <result property="tax" column="TAX" jdbcType="VARCHAR"/>
    <result property="feeNoTax" column="FEE_NO_TAX" jdbcType="VARCHAR"/>
    <result property="feeFlag" column="FEE_FLAG" jdbcType="VARCHAR"/>
    <result property="discountAmount" column="DISCOUNT_AMOUNT" jdbcType="VARCHAR"/>
    <result property="standardFee" column="STANDARD_FEE" jdbcType="VARCHAR"/>
    <result property="settleFee" column="SETTLE_FEE" jdbcType="VARCHAR"/>
    <result property="ghFeeType" column="GH_FEE_TYPE" jdbcType="VARCHAR"/>
    <result property="partnerCode" column="PARTNER_CODE" jdbcType="VARCHAR"/>
    <result property="partnerName" column="PARTNER_NAME" jdbcType="VARCHAR"/>
    <result property="parSettleRate" column="PAR_SETTLE_RATE" jdbcType="VARCHAR"/>
    <result property="settlementType" column="SETTLEMENT_TYPE" jdbcType="VARCHAR"/>
    <result property="parSettlAmount" column="PAR_SETTL_AMOUNT" jdbcType="VARCHAR"/>
    <result property="parResSettlRate" column="PAR_RES_SETTL_RATE" jdbcType="VARCHAR"/>
    <result property="settlementClass" column="SETTLEMENT_CLASS" jdbcType="VARCHAR"/>
    <result property="rateplanId" column="RATEPLAN_ID" jdbcType="VARCHAR"/>
    <result property="rateplanName" column="RATEPLAN_NAME" jdbcType="VARCHAR"/>
    <result property="standardSalePrice" column="STANDARD_SALE_PRICE" jdbcType="VARCHAR"/>
    <result property="settlePrice" column="SETTLE_PRICE" jdbcType="VARCHAR"/>
    <result property="originalBillMonth" column="ORIGINAL_BILL_MONTH" jdbcType="VARCHAR"/>
    <result property="feeSeq" column="FEE_SEQ" jdbcType="VARCHAR"/>
    <result property="memberNums" column="MEMBER_NUMS" jdbcType="VARCHAR"/>
    <result property="ifFreeResource" column="IF_FREE_RESOURCE" jdbcType="VARCHAR"/>
    <result property="onProductCode" column="ON_PRODUCT_CODE" jdbcType="VARCHAR"/>
    <result property="onProductName" column="ON_PRODUCT_NAME" jdbcType="VARCHAR"/>
    <result property="ejProductCode" column="EJ_PRODUCT_CODE" jdbcType="VARCHAR"/>
    <result property="ejProductName" column="EJ_PRODUCT_NAME" jdbcType="VARCHAR"/>
    <result property="sjProductCode" column="SJ_PRODUCT_CODE" jdbcType="VARCHAR"/>
    <result property="sjProductName" column="SJ_PRODUCT_NAME" jdbcType="VARCHAR"/>
    <result property="ghCode" column="GH_CODE" jdbcType="VARCHAR"/>
    <result property="productClass" column="PRODUCT_CLASS" jdbcType="VARCHAR"/>
    <result property="productReportName" column="PRODUCT_REPORT_NAME" jdbcType="VARCHAR"/>
    <result property="productReportItemName" column="PRODUCT_REPORT_ITEM_NAME" jdbcType="VARCHAR"/>
    <result property="issueTime" column="ISSUE_TIME" jdbcType="VARCHAR"/>
    <result property="expireTime" column="EXPIRE_TIME" jdbcType="VARCHAR"/>
    <result property="productType" column="PRODUCT_TYPE" jdbcType="VARCHAR"/>
    <result property="busiType" column="BUSI_TYPE" jdbcType="VARCHAR"/>
    <result property="contractMain" column="CONTRACT_MAIN" jdbcType="VARCHAR"/>
    <result property="reserved1" column="RESERVED_1" jdbcType="VARCHAR"/>
    <result property="reserved2" column="RESERVED_2" jdbcType="VARCHAR"/>
    <result property="status" column="STATUS" jdbcType="VARCHAR"/>
    <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
    <result property="origNode" column="ORIG_NODE" jdbcType="VARCHAR"/>
    <result property="settleDisvalue" column="SETTLE_DISVALUE" jdbcType="VARCHAR"/>
    <result property="discountType" column="DISCOUNT_TYPE" jdbcType="VARCHAR"/>
    <result property="ecId" column="EC_ID" jdbcType="VARCHAR"/>
  </resultMap>

  <delete id="deleteMcByMonth" parameterType="java.lang.String">
    truncate table stludr.SYNC_INTERFACE_MC_${acctMonth}
  </delete>

  <delete id="deleteMcP2PByMonth" parameterType="java.lang.String">
    truncate table stludr.SYNC_INTERFACE_MC_P2P_${acctMonth}
  </delete>

  <delete id="deleteMcP2CByMonth" parameterType="java.lang.String">
    truncate table stludr.SYNC_INTERFACE_MC_P2C_${acctMonth}
  </delete>

  <delete id="deleteByFileNameMonth" parameterType="java.lang.String">
    delete from stludr.SYNC_INTERFACE_MC_${acctMonth}
    <where>
      <if test="fileName != null and fileName != ''">
        AND file_name = #{fileName, jdbcType = VARCHAR}
      </if>
    </where>
  </delete>
  <delete id="deleteMcP2CNMGByMonth">
    truncate table stludr.SYNC_INTERFACE_MC_P2C_NMG_${acctMonth}
  </delete>

  <select id="queryErrorByFileName" resultMap="SyncInterfaceMcMap">
    select
    ID, PROV_CODE, PAY_TAG, CUSTOMER_PROVINCE_NUMBER, CUSTOMER_NAME, CITY_CODE, CREATOR_NAME, ORDER_MODE, PO_SUBS_ID,
    PO_ID, PO_NAME, PRODUCT_SUBS_ID, PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID, DB_PRODUCT_NAME, CO_PRODUCT_ID,
    BL_PRODUCT_ID, ONE_PRODUCT_ID, BILLING_TERM, PAY_TERM, PROD_CHARGE_CODE, PO_CHARGE_CODE_NAME, ONE_PRO_CHARGE_CODE,
    ONE_PRO_CHARGE_NAME, DB_PROD_CHARGE_CODE, FEE_VAL, TAX_RATE, TAX, FEE_NO_TAX, FEE_FLAG, DISCOUNT_AMOUNT,
    STANDARD_FEE, SETTLE_FEE, GH_FEE_TYPE, PARTNER_CODE, PARTNER_NAME, PAR_SETTLE_RATE, SETTLEMENT_TYPE,
    PAR_SETTL_AMOUNT, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS, RATEPLAN_ID, RATEPLAN_NAME, STANDARD_SALE_PRICE,
    SETTLE_PRICE, ORIGINAL_BILL_MONTH, FEE_SEQ, MEMBER_NUMS, IF_FREE_RESOURCE, ON_PRODUCT_CODE, ON_PRODUCT_NAME,
    EJ_PRODUCT_CODE, EJ_PRODUCT_NAME, SJ_PRODUCT_CODE, SJ_PRODUCT_NAME, GH_CODE, PRODUCT_CLASS, PRODUCT_REPORT_NAME,
    PRODUCT_REPORT_ITEM_NAME, ISSUE_TIME, EXPIRE_TIME, PRODUCT_TYPE, BUSI_TYPE, CONTRACT_MAIN, RESERVED_1, RESERVED_2,
    STATUS, FILE_NAME, ORIG_NODE, SETTLE_DISVALUE, DISCOUNT_TYPE, EC_ID
    from SYNC_INTERFACE_MC_${acctMonth}
    <where>
        status!='0'
        and FILE_NAME = #{fileName}
    </where>
  </select>


  <select id="selectFileNameByStatus" resultType="java.lang.String">
    select file_name
    from (select distinct file_name
          from stludr.sync_interface_mc_${acctMonth} minus select distinct file_name
          from stludr.sync_interface_mc_${acctMonth}
          where status != '0')
    order by file_name;
  </select>

  <insert id="insert"  useGeneratedKeys="false">
    insert into SYNC_INTERFACE_MC_${acctMonth}(ID, PROV_CODE, PAY_TAG, CUSTOMER_PROVINCE_NUMBER, CUSTOMER_NAME,
                                               CITY_CODE, CREATOR_NAME, ORDER_MODE, PO_SUBS_ID, PO_ID, PO_NAME,
                                               PRODUCT_SUBS_ID, PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID,
                                               DB_PRODUCT_NAME, CO_PRODUCT_ID, BL_PRODUCT_ID, ONE_PRODUCT_ID,
                                               BILLING_TERM, PAY_TERM, PROD_CHARGE_CODE, PO_CHARGE_CODE_NAME,
                                               ONE_PRO_CHARGE_CODE, ONE_PRO_CHARGE_NAME, DB_PROD_CHARGE_CODE, FEE_VAL,
                                               TAX_RATE, TAX, FEE_NO_TAX, FEE_FLAG, DISCOUNT_AMOUNT, STANDARD_FEE,
                                               SETTLE_FEE, GH_FEE_TYPE, PARTNER_CODE, PARTNER_NAME, PAR_SETTLE_RATE,
                                               SETTLEMENT_TYPE, PAR_SETTL_AMOUNT, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS,
                                               RATEPLAN_ID, RATEPLAN_NAME, STANDARD_SALE_PRICE, SETTLE_PRICE,
                                               ORIGINAL_BILL_MONTH, FEE_SEQ, MEMBER_NUMS, IF_FREE_RESOURCE,
                                               ON_PRODUCT_CODE, ON_PRODUCT_NAME, EJ_PRODUCT_CODE, EJ_PRODUCT_NAME,
                                               SJ_PRODUCT_CODE, SJ_PRODUCT_NAME, GH_CODE, PRODUCT_CLASS,
                                               PRODUCT_REPORT_NAME, PRODUCT_REPORT_ITEM_NAME, ISSUE_TIME, EXPIRE_TIME,
                                               PRODUCT_TYPE, BUSI_TYPE, CONTRACT_MAIN, RESERVED_1, RESERVED_2, STATUS,
                                               FILE_NAME, ORIG_NODE, SETTLE_DISVALUE, DISCOUNT_TYPE, EC_ID)
    values (#{id}, #{provCode}, #{payTag}, #{customerProvinceNumber}, #{customerName}, #{cityCode}, #{creatorName},
            #{orderMode}, #{poSubsId}, #{poId}, #{poName}, #{productSubsId}, #{productId}, #{productName},
            #{dbProductId}, #{dbProductName}, #{coProductId}, #{blProductId}, #{oneProductId}, #{billingTerm},
            #{payTerm}, #{prodChargeCode}, #{poChargeCodeName}, #{oneProChargeCode}, #{oneProChargeName},
            #{dbProdChargeCode}, #{feeVal}, #{taxRate}, #{tax}, #{feeNoTax}, #{feeFlag}, #{discountAmount},
            #{standardFee}, #{settleFee}, #{ghFeeType}, #{partnerCode}, #{partnerName}, #{parSettleRate},
            #{settlementType}, #{parSettlAmount}, #{parResSettlRate}, #{settlementClass}, #{rateplanId},
            #{rateplanName}, #{standardSalePrice}, #{settlePrice}, #{originalBillMonth}, #{feeSeq}, #{memberNums},
            #{ifFreeResource}, #{onProductCode}, #{onProductName}, #{ejProductCode}, #{ejProductName}, #{sjProductCode},
            #{sjProductName}, #{ghCode}, #{productClass}, #{productReportName}, #{productReportItemName}, #{issueTime},
            #{expireTime}, #{productType}, #{busiType}, #{contractMain}, #{reserved1}, #{reserved2}, #{status},
            #{fileName}, #{origNode}, #{settleDisvalue}, #{discountType}, #{ecId})
  </insert>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
    insert into SYNC_INTERFACE_MC_${acctMonth} (
      ID,
      PROV_CODE,
      PAY_TAG,
      CUSTOMER_PROVINCE_NUMBER,
      CUSTOMER_NAME,
      CITY_CODE,
      CREATOR_NAME,
      EC_ID,
      PO_SUBS_ID,
      PO_ID,
      PO_NAME,
      PRODUCT_SUBS_ID,
      PRODUCT_ID,
      PRODUCT_NAME,
      DB_PRODUCT_ID,
      DB_PRODUCT_NAME,
      CO_PRODUCT_ID,
      BL_PRODUCT_ID,
      ONE_PRODUCT_ID,
      BILLING_TERM,
      PAY_TERM,
      PROD_CHARGE_CODE,
      PO_CHARGE_CODE_NAME,
      ONE_PRO_CHARGE_CODE,
      ONE_PRO_CHARGE_NAME,
      DB_PROD_CHARGE_CODE,
      FEE_VAL,
      TAX_RATE,
      TAX,
      FEE_NO_TAX,
      FEE_FLAG,
      DISCOUNT_AMOUNT,
      STANDARD_FEE,
      SETTLE_FEE,
      GH_FEE_TYPE,
      PARTNER_CODE,
      PARTNER_NAME,
      PAR_SETTLE_RATE,
      SETTLEMENT_TYPE,
      PAR_SETTL_AMOUNT,
      PAR_RES_SETTL_RATE,
      SETTLEMENT_CLASS,
      RATEPLAN_ID,
      RATEPLAN_NAME,
      STANDARD_SALE_PRICE,
      SETTLE_PRICE,
      ORIGINAL_BILL_MONTH,
      FEE_SEQ,
      MEMBER_NUMS,
      IF_FREE_RESOURCE,
      DISCOUNT_TYPE,
      SETTLE_DISVALUE,
      BS_TYPE,
      ON_PRODUCT_CODE,
      ON_PRODUCT_NAME,
      EJ_PRODUCT_CODE,
      EJ_PRODUCT_NAME,
      SJ_PRODUCT_CODE,
      SJ_PRODUCT_NAME,
      GH_CODE,
      PRODUCT_CLASS,
      PRODUCT_REPORT_NAME,
      PRODUCT_REPORT_ITEM_NAME,
      ISSUE_TIME,
      EXPIRE_TIME,
      PRODUCT_TYPE,
      BUSI_TYPE,
      CONTRACT_MAIN,
      RESERVED_1,
      RESERVED_2,
      FILE_NAME,RATE_PLAN_GH_CODE,RATE_PLAN_GH_NAME) values
     <foreach collection="list" item="item" index="index" separator=",">
      (
        #{item.id},
        #{item.provCode},
        #{item.payTag},
        #{item.customerProvinceNumber},
        #{item.customerName},
        #{item.cityCode},
        #{item.creatorName},
        #{item.ecId},
        #{item.poSubsId},
        #{item.poId},
        #{item.poName},
        #{item.productSubsId},
        #{item.productId},
        #{item.productName},
        #{item.dbProductId},
        #{item.dbProductName},
        #{item.coProductId},
        #{item.blProductId},
        #{item.oneProductId},
        #{item.billingTerm},
        #{item.payTerm},
        #{item.prodChargeCode},
        #{item.poChargeCodeName},
        #{item.oneProChargeCode},
        #{item.oneProChargeName},
        #{item.dbProdChargeCode},
        #{item.feeVal},
        #{item.taxRate},
        #{item.tax},
        #{item.feeNoTax},
        #{item.feeFlag},
        #{item.discountAmount},
        #{item.standardFee},
        #{item.settleFee},
        #{item.ghFeeType},
        #{item.partnerCode},
        #{item.partnerName},
        #{item.parSettleRate},
        #{item.settlementType},
        #{item.parSettlAmount},
        #{item.parResSettlRate},
        #{item.settlementClass},
        #{item.rateplanId},
        #{item.rateplanName},
        #{item.standardSalePrice},
        #{item.settlePrice},
        #{item.originalBillMonth},
        #{item.feeSeq},
        #{item.memberNums},
        #{item.ifFreeResource},
        #{item.discountType},
        #{item.settleDisvalue},
        #{item.bsType},
        #{item.onProductCode},
        #{item.onProductName},
        #{item.ejProductCode},
        #{item.ejProductName},
        #{item.sjProductCode},
        #{item.sjProductName},
        #{item.ghCode},
        #{item.productClass},
        #{item.productReportName},
        #{item.productReportItemName},
        #{item.issueTime},
        #{item.expireTime},
        #{item.productType},
        #{item.busiType},
        #{item.contractMain},
        #{item.reserved1},
        #{item.reserved2},
        #{item.fileName},
        #{item.ratePlanGhCode},
        #{item.ratePlanGhName}
      )
    </foreach>
  </insert>

  <update id="updateStatusByAcctMonth">
    update stludr.SYNC_INTERFACE_MC_${acctMonth}
    set status='23'
    where prov_code not in (select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1'))
      and status is null
      and file_name = #{fileName}
  </update>

  <update id="updateStatusByAcctMonthAndProductId">
    update stludr.SYNC_INTERFACE_MC_${acctMonth} t
    set status = '24'
    where t.co_product_id is null
      and (select count(1) from stludr.sync_interface_mc_p2p_${acctMonth} a where a.id = t.id and a.file_name = t.file_name) > 0
      and status is null
      and file_name = #{fileName}
  </update>


  <update id="updateStatusByAcctMonthAndId"  >
    update stludr.SYNC_INTERFACE_MC_${acctMonth} t
    set status = '99'
    where  status is null and file_name = #{fileName}
      and pay_tag = 2 and (select count(1) from stludr.sync_interface_mc_p2c_${acctMonth} b where b.id = t.id and b.file_name = t.file_name) > 0
  </update>

  <update id="updateOrderModeByAcctMonth">
    UPDATE stludr.SYNC_INTERFACE_MC_${acctMonth}
    SET order_mode =
          CASE
            WHEN prov_code = '000' THEN '1'
            ELSE '3'
            END
    WHERE `status` IS NULL
  </update>

  <update id="updateStatusByOrderMode">
    update stludr.sync_interface_mc_${acctMonth}
    set status='25'
    where order_mode is null
      and co_product_id is not null
      and status is null
      and file_name = #{fileName}
  </update>
  <update id="updateStatusModeByAcctMonthAndStatus">
    update stludr.SYNC_INTERFACE_MC_${acctMonth} a set status = '0'
    where status is null and file_name = #{fileName}
  </update>
</mapper>