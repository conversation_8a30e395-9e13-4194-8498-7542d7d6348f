package com.settle.server.module.esp;

import org.apache.commons.io.filefilter.AbstractFileFilter;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/5/16
 * @since 1.0.0
 */
public class CustomerFileFilter extends AbstractFileFilter {

    private final String pattern;


    public CustomerFileFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public boolean accept(File file) {
        Pattern p = Pattern.compile(pattern);
        Matcher matcher = p.matcher(file.getName());
        return matcher.find();
    }


    @Override
    public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
        return super.visitFile(file, attributes);
    }
}