package com.settle.server.controller;


import com.settle.server.settleserver.BadyHandler;
import com.settle.server.settleserver.BaseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class SettleServerController {


    @PostMapping("/test111")
    public  void SettleServerMain(HttpServletRequest req, @RequestParam Map<String,Object> request){
        log.info("start up...");
        String arg= (String) request.get("request");
        List<String> args= Arrays.asList(arg.split(","));
        BaseHandler handler = new BadyHandler();
        if (args.size() == 0)
            handler.init();
        else {
            handler.init(args.get(0));
        }
        handler.start();
        log.info("end...");
    }

}
