package com.settle.server.xmlTransform.xml2text;

//import java.io.*;
import com.settle.server.utils.CommandUtil;
import com.settle.server.xmlTransform.xml2text.config.Entry;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Slf4j
public class Parser {
	public static void showUsage() {
		System.out.println("Usage:");
		System.out
				.println("\tjava -cp xmlusage.jar com.hp.xmlusage.xml2text.Parser -i Config.xml");
	}

	public static void main(String[] args) {
		if (args.length != 2 || !args[0].equals("-i")) {
			showUsage();
			return;
		}

		System.out.println("I AM STARTING ......");
		String cfgFile = args[1];
		if (!Config.init(cfgFile)) {
			System.out.println("Initializing from config file failed!");
			return;
		}
		if (!CommandUtil.isSingle("com.hp.xmlusage.xml2text.Parser", cfgFile)) {
			log.error("There is one same thread running already!");
			return;
		}
		log.info("Initialized from config file successfully.");
		
		ExecutorService fixedThreadPool = Executors.newFixedThreadPool(30);
		for (int i = 0; i < Config.getEntryCount(); i++) {
			Entry entry = Config.getEntry(i);
			try {
//				new Thread(new X2TEngine(entry)).start();
				fixedThreadPool.execute(new X2TEngine(entry));
				log.info(entry.getName() + " thread of xml2text is started.");
			} catch (Exception ex) {
				log.error("Start Entry: "+entry.getName()+"Error, Exception: " + ex.getMessage());
				// return;
				System.exit(1);
			}
		}

		log.info("All threads are started.");

		/*
		 * while (true) { try { Thread.sleep(10 * 1000); } catch
		 * (InterruptedException ex) { ex.printStackTrace(); break; } }
		 * 
		 * FileUtil.getLog().info("Program Over.");
		 */

	}
}