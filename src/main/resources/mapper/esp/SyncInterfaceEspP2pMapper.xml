<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.esp.SyncInterfaceEspP2pMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.esp.SyncInterfaceEspP2p">
        <result column="ID" property="id" />
        <result column="SETTLEMENT_PARTY_IN" property="settlementPartyIn" />
        <result column="SETTLEMENT_PARTY_OUT" property="settlementPartyOut" />
        <result column="SETTLEMENT_RATE" property="settlementRate" />
        <result column="SETTLEMENT_TYPE" property="settlementType" />
        <result column="SETTLEMENT_AMOUNT" property="settlementAmount" />
        <result column="REMARK" property="remark" />
        <result column="FILE_NAME" property="fileName" />
        <result column="STATUS" property="status" />
    </resultMap>
    <insert id="saveP2p">
        INSERT INTO SYNC_INTERFACE_ESP_P2P_${acctMonth}
            (
             ID,
             SETTLEMENT_PARTY_IN,
             SETTLEMENT_PARTY_OUT,
             SETTLEMENT_RATE,
             SETTLEMENT_TYPE,
             SETTLEMENT_AMOUNT,
             REMARK,
             FILE_NAME,
             STATUS
            )
        VALUES
            <foreach collection="list" item="item" index="index" separator=",">
                (
                 #{item.id},
                 #{item.settlementPartyIn},
                 #{item.settlementPartyOut},
                 #{item.settlementRate},
                 #{item.settlementType},
                 #{item.settlementAmount},
                 #{item.remark},
                 #{item.fileName},
                 #{item.status}
                )
            </foreach>
    </insert>
    <update id="truncate">
        TRUNCATE TABLE SYNC_INTERFACE_ESP_P2P_${acctMonth};
    </update>
    <update id="updateStatus23">
        update stludr.SYNC_INTERFACE_ESP_P2P_${acctMonth}
        set status='F023'
        where (file_name, id) in (select file_name, id from stludr.SYNC_INTERFACE_ESP_${acctMonth} where status='F023')
        and status is null
        and file_name=#{fileName}
    </update>

</mapper>
