package com.settle.server.controller;

import com.settle.server.module.report.dto.ReportDTO;
import com.settle.server.module.report.service.ReportFileBeanFactory;
import com.settle.server.module.report.service.ReportFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2024/4/9
 * @since 1.0.0
 */
@RestController
@RequestMapping("erp")
@Slf4j
public class ErpReportController {

    @Autowired
    private ReportFileBeanFactory reportFileBeanFactory;

    @GetMapping("/report")
    public String report(@RequestParam("settlemonth") String settlemonth,
                         @RequestParam("R_KEY") String rKey,
                         @RequestParam("sign") String sign) {
        try {
            ReportFileService reportFileService = reportFileBeanFactory.getReportFileService(ReportFileBeanFactory.ReportFileType.ERP);
            ReportDTO dto = new ReportDTO();
            dto.setRKey(rKey);
            dto.setSettleMonth(settlemonth);
            dto.setSign(sign);
            reportFileService.reportFile(Collections.singletonList(dto));
        } catch (Exception e) {
            log.warn("erp report error:{}", e.getMessage(), e);
            return "error";
        }
        return "success";
    }
}