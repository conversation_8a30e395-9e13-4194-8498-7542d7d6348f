<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.settle.server.dao.stludr.pvsettle.SyncInterfacePvsTocMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.settle.server.entity.pvsettle.SyncInterfacePvsToc">
        <result column="PROV_CODE" property="provCode" />
        <result column="IS_TO_C" property="isToC" />
        <result column="PRODUCT_SUBS_ID" property="productSubsId" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="PRODUCT_NAME" property="productName" />
        <result column="DB_PRODUCT_ID" property="dbProductId" />
        <result column="DB_PRODUCT_NAME" property="dbProductName" />
        <result column="PV_PRODUCT_CLASS" property="pvProductClass" />
        <result column="BILLING_TERM" property="billingTerm" />
        <result column="PAY_TERM" property="payTerm" />
        <result column="FEE_VAL" property="feeVal" />
        <result column="TAX_RATE" property="taxRate" />
        <result column="TAX" property="tax" />
        <result column="FEE_NO_TAX" property="feeNoTax" />
        <result column="PV_SETTLE_RATE" property="pvSettleRate" />
        <result column="PV_SETTLE_VALUE" property="pvSettleValue" />
        <result column="RATE_PLAN_GH_CODE" property="ratePlanGhCode" />
        <result column="RATE_PLAN_GH_NAME" property="ratePlanGhName" />
        <result column="ISSUE_TIME" property="issueTime" />
        <result column="EXPIRE_TIME" property="expireTime" />
        <result column="BZ_TYPE" property="bzType" />
        <result column="RESERVED_1" property="reserved1" />
        <result column="RESERVED_2" property="reserved2" />
        <result column="STATUS" property="status" />
        <result column="FILE_NAME" property="fileName" />
        <result column="TAG" property="tag" />
    </resultMap>
    <insert id="insertBatch">
        insert into SYNC_INTERFACE_PVS_TOC_${acctMonth}(PROV_CODE, IS_TO_C, PRODUCT_SUBS_ID, PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID, DB_PRODUCT_NAME, PV_PRODUCT_CLASS, BILLING_TERM, PAY_TERM, FEE_VAL, TAX_RATE, TAX, FEE_NO_TAX, PV_SETTLE_RATE, PV_SETTLE_VALUE, RATE_PLAN_GH_CODE, RATE_PLAN_GH_NAME, ISSUE_TIME, EXPIRE_TIME, BZ_TYPE, RESERVED_1, RESERVED_2, STATUS, FILE_NAME, TAG)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
             #{item.provCode},
             #{item.isToC},
             #{item.productSubsId},
             #{item.productId},
             #{item.productName},
             #{item.dbProductId},
             #{item.dbProductName},
             #{item.pvProductClass},
             #{item.billingTerm},
             #{item.payTerm},
             #{item.feeVal},
             #{item.taxRate},
             #{item.tax},
             #{item.feeNoTax},
             #{item.pvSettleRate},
             #{item.pvSettleValue},
             #{item.ratePlanGhCode},
             #{item.ratePlanGhName},
             #{item.issueTime},
             #{item.expireTime},
             #{item.bzType},
             #{item.reserved1},
             #{item.reserved2},
             #{item.status},
             #{item.fileName},
             #{item.tag})
        </foreach>
    </insert>

    <update id="truncateTable">
        TRUNCATE TABLE SYNC_INTERFACE_PVS_TOC_${acctMonth}
    </update>
    <update id="updateStatusByAcctMonth">
        update stludr.SYNC_INTERFACE_PVS_TOC_${acctMonth}
        set status='23'
        where prov_code not in (select prov_cd from stludr.stl_province_cd where prov_type in ('0', '1'))
          and status is null
          and file_name = #{fileName}
    </update>
    <update id="updateStatus0">
        update stludr.SYNC_INTERFACE_PVS_TOC_${acctMonth}
        set status='0'
        where status is null
        and file_name = #{fileName}
    </update>
    <select id="queryErrorByFileName" resultMap="BaseResultMap">
        select PROV_CODE, IS_TO_C, PRODUCT_SUBS_ID, PRODUCT_ID, PRODUCT_NAME, DB_PRODUCT_ID, DB_PRODUCT_NAME, PV_PRODUCT_CLASS, BILLING_TERM, PAY_TERM, FEE_VAL, TAX_RATE, TAX, FEE_NO_TAX, PV_SETTLE_RATE, PV_SETTLE_VALUE, RATE_PLAN_GH_CODE, RATE_PLAN_GH_NAME, ISSUE_TIME, EXPIRE_TIME, BZ_TYPE, RESERVED_1, RESERVED_2, STATUS, FILE_NAME, TAG
        from SYNC_INTERFACE_PVS_TOC_${acctMonth}
        where FILE_NAME = #{fileName}
        <![CDATA[AND STATUS <> '0'    ]]>
    </select>
</mapper>
