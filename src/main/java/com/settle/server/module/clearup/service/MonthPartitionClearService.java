package com.settle.server.module.clearup.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.settle.server.module.clearup.dto.ClearLogDTO;
import com.settle.server.module.clearup.dto.DbNameEnum;
import com.settle.server.module.clearup.dto.RunLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2025/5/23
 * @since 1.0.0
 */
@Service("monthPartitionClearService")
@Slf4j
public class MonthPartitionClearService extends AbstractClearTableService{


    private int partitionNum = 1;
    private String partitionFormat = "p%02d";

    @Override
    protected void beforeProcess(RunLog runLog, DbNameEnum nameEnum) {
        int mmdd = runLog.getLogMmdd();
        //获取dd
        int dd = mmdd % 100;
        if (dd != 16) {
            runLog.setExec(false);
            runLog.setLogLevel("DBG");
            runLog.setLogText("dd " + dd + " ne 16, skip monthly clean");
        }else {
            runLog.setExec(true);
            runLog.setLogLevel("INF");
            runLog.setLogText("dd " + dd + " eq 16, start monthly clean");
        }
        saveRunLog(runLog,nameEnum);
    }

    @Override
    protected List<String> partitionsBySelf(ClearLogDTO dto) {
        //删除前1一个分区数据
        DateTime now = DateTime.of(DateTime.now());
        ArrayList<String> partitionList = Lists.newArrayList();
        for (int i = 0; i < partitionNum; i++) {
            Integer mm = Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(now, i + 1), "MM"));
            String partition = String.format(partitionFormat, mm);
            partitionList.add(partition);
        }
        return partitionList;
    }
}