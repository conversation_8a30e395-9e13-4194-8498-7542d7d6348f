package com.settle.server.module.cdn.flatfile;

import cn.hutool.core.util.StrUtil;
import com.settle.server.module.cdn.constant.NormalConst;
import com.settle.server.module.cdn.mapper.AbstractRecordMapper;
import com.settle.server.module.cdn.mapper.ObjectMapper;
import com.settle.server.module.cdn.mapper.RecordMapper;
import com.settle.server.module.cdn.record.GenericRecord;
import com.settle.server.module.cdn.record.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 */
public class DelimitedRecordMapper<P> extends AbstractRecordMapper<P> implements RecordMapper<String, P> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DelimitedRecordMapper.class);

    public static final String DEFAULT_DELIMITER = ",";
    public static final String DEFAULT_QUALIFIER = "";
    public static final boolean DEFAULT_WHITESPACE_TRIMMING = false;

    private String delimiter = DEFAULT_DELIMITER;
    private boolean trimWhitespaces = DEFAULT_WHITESPACE_TRIMMING;
    private String qualifier = DEFAULT_QUALIFIER;
    private int recordExpectedLength;
    private List<Integer> fieldsPositions;
    private String[] fieldNames;
    private boolean fieldNamesRetrievedFromHeader;
    private String orgLogId;

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     * 列名和预期的记录大小将从标题记录中计算出来。并设置为与目标对象同名的字段。
     *
     * @param recordClass 目标域对象类
     */
    public DelimitedRecordMapper(final Class<P> recordClass) {
        super(recordClass);
        objectMapper = new ObjectMapper<>(recordClass);
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     * 预期的记录大小将从标题记录中计算出来。
     *
     * @param recordClass 目标域对象类
     * @param fieldNames  一个字符串数组，包含目标类型字段名称，其顺序与分隔平面文件中的顺序相同
     */
    public DelimitedRecordMapper(final Class<P> recordClass, String... fieldNames) {
        this(recordClass);
        this.fieldNames = fieldNames;
        this.recordExpectedLength = fieldNames.length;
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     *
     * @param recordClass          目标域对象类
     * @param fieldNames           一个字符串数组，包含目标类型字段名，其顺序与分隔平面文件中的顺序相同
     * @param recordExpectedLength 记录预期长度
     */
    public DelimitedRecordMapper(final Class<P> recordClass, final String[] fieldNames, final int recordExpectedLength) {
        this(recordClass, fieldNames);
        this.recordExpectedLength = recordExpectedLength;
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     *
     * @param recordClass          目标域对象类
     * @param fieldNames           一个字符串数组，包含目标类型字段名，其顺序与分隔平面文件中的顺序相同
     * @param recordExpectedLength 记录预期长度
     * @param orgLogId             文件对应日志ID
     */
    public DelimitedRecordMapper(final Class<P> recordClass, final String[] fieldNames, final int recordExpectedLength, final String orgLogId) {
        this(recordClass, fieldNames);
        this.recordExpectedLength = recordExpectedLength;
        this.orgLogId = orgLogId;
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     * 预期的记录大小将从标题记录中计算出来
     *
     * @param recordClass     目标域对象类
     * @param fieldsPositions 要保留的字段索引数组
     */
    public DelimitedRecordMapper(final Class<P> recordClass, final Integer... fieldsPositions) {
        this(recordClass);
        this.fieldsPositions = Arrays.asList(fieldsPositions);
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     * 预期的记录大小将从标题记录中计算出来
     *
     * @param recordClass     目标域对象类
     * @param fieldsPositions 要保留的字段索引数组
     * @param fieldNames      在平面文件中以相同顺序表示字段名称的字符串数组
     */
    public DelimitedRecordMapper(final Class<P> recordClass, final Integer[] fieldsPositions, final String[] fieldNames) {
        this(recordClass);
        this.fieldNames = fieldNames;
        this.fieldsPositions = Arrays.asList(fieldsPositions);
    }

    /**
     * 创建一个新的 {@link DelimitedRecordMapper}
     *
     * @param recordClass          目标域对象类
     * @param fieldsPositions      要保留的字段索引数组
     * @param fieldNames           在平面文件中以相同顺序表示字段名称的字符串数组
     * @param recordExpectedLength 记录预期长度
     */
    public DelimitedRecordMapper(final Class<P> recordClass, final Integer[] fieldsPositions, final String[] fieldNames, final int recordExpectedLength) {
        this(recordClass, fieldsPositions, fieldNames);
        this.recordExpectedLength = recordExpectedLength;
    }

    @Override
    public Record<P> processRecord(final Record<String> record) throws Exception {
        List<Field> fields = parseRecord(record);
        Map<String, String> fieldsContents = new HashMap<>();
        int index = 0;
        for (Field field : fields) {
            String fieldName;
            if (fieldNamesRetrievedFromHeader) {
                fieldName = fieldNames[field.getIndex()];
            } else {
                fieldName = fieldNames[index++];
            }
            String rawContent = field.getRawContent();

            if (StrUtil.equalsAny(fieldName, "adjustAcctMonth", "adjustAmount") && StrUtil.isBlank(rawContent)) {
                rawContent = null;
            }

            fieldsContents.put(fieldName, rawContent);
        }
        if (this.orgLogId != null) {
            fieldsContents.put(NormalConst.CAMEL_LOG_ID, this.orgLogId);
            fieldsContents.put(NormalConst.CAMEL_LINE_NUM, String.valueOf(record.getHeader().getNumber()));
        }

        return new GenericRecord<>(record.getHeader(), objectMapper.mapObject(fieldsContents));
    }

    protected List<Field> parseRecord(final Record<String> record) throws Exception {

        String payload = record.getPayload();
        String[] tokens = payload.split(delimiter, -1);

        setRecordExpectedLength(tokens);
        setFieldNames(tokens);
        checkRecordLength(tokens);
        checkQualifier(tokens);

        List<Field> fields = new ArrayList<>();
        int index = 0;
        for (String token : tokens) {
            token = trimWhitespaces(token);
            token = removeQualifier(token);
            fields.add(new Field(index++, token));
        }
        if (fieldsPositions != null) {
            filterFields(fields);
        }
        return fields;
    }

    private void checkQualifier(String[] tokens) throws Exception {
        if (qualifier.length() > 0) {
            for (String token : tokens) {
                if (!token.startsWith(qualifier) || !token.endsWith(qualifier)) {
                    throw new Exception("field [" + token + "] is not enclosed as expected with '" + qualifier + "'");
                }
            }
        }
    }

    private void checkRecordLength(String[] tokens) throws Exception {
        if (tokens.length != recordExpectedLength) {
            throw new Exception("record length (" + tokens.length + " fields) not equal to expected length of "
                    + recordExpectedLength + " fields");
        }
    }

    private void setFieldNames(String[] tokens) {
        // 配置约定：如果未指定字段名称，则从标题记录中检索它们（仅完成一次）
        if (fieldNames == null) {
            fieldNamesRetrievedFromHeader = true;
            fieldNames = new String[tokens.length];
            for (int i = 0; i < tokens.length; i++) {
                String token = tokens[i];
                token = trimWhitespaces(token);
                token = removeQualifier(token);
                fieldNames[i] = token;
            }
        }
    }

    private void setRecordExpectedLength(String[] tokens) {
        // 约定优于配置：如果未指定预期的记录大小，则从标头记录中计算
        if (this.recordExpectedLength == 0) {
            this.recordExpectedLength = tokens.length;
        }
    }

    private void filterFields(List<Field> fields) {
        Iterator<Field> iterator = fields.iterator();
        while (iterator.hasNext()) {
            int index = iterator.next().getIndex();
            if (!fieldsPositions.contains(index)) {
                iterator.remove();
            }
        }
    }

    private String trimWhitespaces(final String token) {
        if (trimWhitespaces) {
            return token.trim();
        }
        return token;
    }

    private String removeQualifier(final String token) {
        int qualifierLength = qualifier.length();
        if (qualifierLength > 0) {
            return token.substring(qualifierLength, token.length() - qualifierLength);
        }
        return token;
    }

    /**
     * 设置要使用的分隔符
     *
     * @param delimiter 要使用的分隔符
     */
    public void setDelimiter(final String delimiter) {
        String prefix = "";
        //转义 String.split 方法的正则表达式中使用的“管道”字符
        if ("|".equals(delimiter)) {
            prefix = "\\";
        }
        this.delimiter = prefix + delimiter;
    }

    /**
     * 解析记录时移除空格
     *
     * @param trimWhitespaces 如果要移除空格，则为 true
     */
    public void setTrimWhitespaces(final boolean trimWhitespaces) {
        this.trimWhitespaces = trimWhitespaces;
    }

    /**
     * 设置要使用的数据限定符
     *
     * @param qualifier 要使用的数据限定符
     */
    public void setQualifier(final String qualifier) {
        this.qualifier = qualifier;
    }

}
