package com.settle.server.module.cdn.reader;

import java.nio.charset.Charset;
import java.nio.file.Path;

/**
 * 所有文件读取器的抽象类
 *
 * @param <P> 记录负载的类型
 * <AUTHOR>
 */
public abstract class AbstractFileRecordReader<P> implements RecordReader<P> {

    protected Path path;
    protected Charset charset;

    /**
     * @param path 从中读取数据
     */
    protected AbstractFileRecordReader(Path path) {
        this(path, Charset.defaultCharset());
    }

    /**
     * @param path    从中读取数据
     * @param charset 输入文件的
     */
    public AbstractFileRecordReader(Path path, Charset charset) {
        this.path = path;
        this.charset = charset;
    }

    public AbstractFileRecordReader() {
        // no op
    }

    public Path getPath() {
        return path;
    }

    public Charset getCharset() {
        return charset;
    }
}
