package com.settle.server.module.bigdata.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.settle.server.dao.stludr.bigdata.SyncInterfaceBigdataFlowMapper;
import com.settle.server.entity.bigdata.SyncInterfaceBigdataFlow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Service
@Slf4j
public class SyncInterfaceBigdataFlowService extends ServiceImpl<SyncInterfaceBigdataFlowMapper, SyncInterfaceBigdataFlow> {


    public void insertBatch(List<SyncInterfaceBigdataFlow> syncInterfaceBigdataFlows) {

        List<List<SyncInterfaceBigdataFlow>> partition = Lists.partition(syncInterfaceBigdataFlows, 500);
        for (List<SyncInterfaceBigdataFlow> list : partition) {
            getBaseMapper().saveBatch(list);
            log.debug("插入数据条数:{}", list.size());
        }

    }

    public void deleteByAcctMonth(String acctMonth) {
        getBaseMapper().delete(new LambdaQueryWrapper<SyncInterfaceBigdataFlow>().eq(SyncInterfaceBigdataFlow::getSettleMonth, acctMonth));
    }
}
