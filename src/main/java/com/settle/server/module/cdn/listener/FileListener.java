package com.settle.server.module.cdn.listener;


import com.settle.server.module.cdn.job.JobMetrics;

/**
 * 文件监听器
 *
 * <AUTHOR>
 */
public interface FileListener {

    /**
     * 在处理文件之前调用
     *
     * @throws Exception 异常
     */
    default void beforeFileProcessing() {
        // no-op
    }

    /**
     * 处理文件后调用
     *
     * @throws Exception 异常
     */
    default void afterFileProcessing(final JobMetrics jobMetrics){
        // no-op
    }

    /**
     * 在文件处理过程中发生异常时调用
     *
     * @param throwable 在文件处理期间抛出的 throwable
     */
    default void onFileProcessingException(final Throwable throwable) {
        // no-op
    }
}
