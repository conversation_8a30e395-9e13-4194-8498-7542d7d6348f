#!/bin/bash
export TXT2DB_HOME=/home/<USER>/workspace/Membership
export LOG_PATH=${TXT2DB_HOME}/log
export CONF_PATH=${TXT2DB_HOME}/conf
export WORK_PATH=${TXT2DB_HOME}/work

log_file=${LOG_PATH}/txt2db_Membership_`date +%Y%m%d`.log
response_file=${WORK_PATH}/Membership_response.csv


function f_Print
{
    cur_ts=`date +%Y%m%d%H%M%S`
    echo "${cur_ts} - $1" >> $log_file
}

function f_init_config
{
    f_Print "f_init_config starts"

    if [ ! -f "${CONF_PATH}/txt2db.conf" ]; then
        f_Print "f_init_config - config file not found - ${CONF_PATH}/txt2db.conf"
        exit
    fi 

    . ${CONF_PATH}/txt2db.conf

    f_Print "f_init_config finishes"
}

function f_delete_table
{

    f_Print "f_delete_table starts"

    QResult=`sqlplus  -s ${a_db_str} << END
    whenever sqlerror exit sql.sqlcode       
    
    set head off
    set feed off  
    set echo off
    set termout off
    set trimout off
    
    delete from bboss.stl_mem_interests where acct_month = ${acct_month};
    commit;
END`

    f_Print "Oracle Messages :[ $QResult ]"
    
    if [[  ! -z $QResult && -z ${QResult##*ORA-*}  && ! -z ${QResult##*ORA-28002*} && ! -z ${QResult##*ORA-28011*}   ]]
    then
        f_Print "Error: f_delete_table failed - Oracle Error "
        f_Print "$QResult"
        exit
    fi

    f_Print "f_delete_table finishes"
    
}

function f_valid_file
{
    f_Print "f_valid_file starts"
    
    for filename in `find ${DATA_PATH} -type f -name "${naming_convention}"`
    do
        filename_only=`basename $filename ".csv"`
        f_Print "f_valid_file - ${filename_only} - Starting validating file"
        
        ##delete ^M and empty rows, append row number, acct_month and field count at the end of each row
        tr -d '\r' < ${DATA_PATH}/${filename_only}.csv | grep -v "^$" | sed "s/\t//g" | sed "s/ //g" | awk -F '|' '{print $0 "|" "'$acct_month'" }' > ${WORK_PATH}/${filename_only}.wrk
        
        ##count field number for each row. validate length for each field. change status to 99 in the wrong case
        cat ${WORK_PATH}/${filename_only}.wrk|awk -F '|' '{if (NF==10) print $0; else print $0 "|本行字段数不符合要求！|99" }'|awk -F '|' '{if (length($1)==1||$NF==99) print $0; else print $0 "|第一个字段orderSource为必填字段且长度应为1！|99"}'|awk -F '|' '{if (length($2)<=8&&length($2)>0||$NF=99) print $0; else print $0 "第二个字段lineNo为必填字段且长度不应大于8！|99"}'|awk -F '|' '{if (length($3)<=19&&length($3)>0||$NF==99) print $0; else print $0 "|第三个字段prodorderSkuNum为必填字段且长度不应大于19！|99"}'|awk -F '|' '{if (length($4)<=20&&length($4)>0||$NF==99) print $0; else print $0 "|第四个字段prodistSkuNum为必填字段且长度不应大于20！|99"}'|awk -F '|' '{if (length($5)<=32&&length($5)>0||$NF==99) print $0; else print $0 "|第五个字段customerNum为必填字段且长度不应大于32！|99"}'|awk -F '|' '{if (length($6)<=16&&length($6)>0||$NF==99) print $0; else print $0 "|第六个字段memberNum为必填字段且长度不应大于16！|99"}'|awk -F '|' '{if (length($7)<=16&&length($7)>0||$NF==99) print $0; else print $0 "|第七个字段resourceCode的长度不应大于16！|99"}'|awk -F '|' '{if (length($8)<=3||$NF==99) print $0; else print $0 "|第八个字段province的长度不应大于3！|99"}'|awk -F '|' '{if (length($9)<=16||$NF==99) print $0; else print $0 "|第九个字段collectTime的长度不应超过16！|99"}' > ${WORK_PATH}/${filename_only}.csv
    done

    f_Print "f_valid_file finishes"
}

function f_load_data
{

    f_Print "f_load_data starts"

    for filename in `find ${WORK_PATH} -type f -name "${naming_convention}"`
    do
        filename_only=`basename $filename ".csv"`
        f_Print "f_load_data - ${filename_only}.csv - Starting loading file"

        cat ${WORK_PATH}/${filename_only}.csv|awk -F '|' '{if ($NF==99) print "||||||||" "'$filename_only'" ".csv|"$2"|"$(NF-2)"|"$NF"|"$(NF-1); else print $1"|"$3"|"$4"|"$5"|"$6"|"$7"|"$8"|"$9"|" "'$filename_only'" ".csv|"$2"|"$10"||"}' > ${WORK_PATH}/STL_MEM_INTERESTS.dat

        cd ${WORK_PATH}
        sqlldr ${a_db_str} control=${CONF_PATH}/STL_MEM_INTERESTS.ctl log=${LOG_PATH}/sqlldr_log_${filename_only} bad=${WORK_PATH}/bad.${filename_only}

        ##original data file move to backup dir
        mv  ${DATA_PATH}/${filename_only}.csv ${BACKUP_PATH}/${acct_month}/

        ##delete file from work dir
        rm ${WORK_PATH}/${filename_only}.wrk
        rm ${WORK_PATH}/${filename_only}.csv
        rm ${WORK_PATH}/STL_MEM_INTERESTS.dat

        f_Print "f_load_data - ${filename_only}.csv - loading file - done"
    done

    f_Print "f_load_data finishes" 

}

function f_valid_data
{

    f_Print "f_valid_data starts"

    QResult=`sqlplus  -s ${a_db_str} << END
    whenever sqlerror exit sql.sqlcode       
    
    set head off
    set feed off  
    set echo off
    set termout off
    set trimout off
    
    --code 04
    update bboss.stl_mem_interests set status = '04' where acct_month = '${acct_month}' and status is null and resource_code not in ('3145', '3146', '3147', '3148', '3149', '3150', '3199', '3200', '3378', '3379', '3214', '3380', '3215', '3381');

    --code 99 province name
    update bboss.stl_mem_interests a set prov_nm = (select prov_name from bboss.province_t b where a.province = b.prov_code and a.province = b.prov_code) where acct_month = '${acct_month}' and status is null;
    update bboss.stl_mem_interests a set status = '99',err_msg = '省代码不存在' where acct_month = '${acct_month}' and status is null and a.prov_nm is null;

    --code 05
    update bboss.stl_mem_interests a set status = '05' where acct_month = '${acct_month}' and status is null and not exists (select * from bboss.serv_biz_code b where '${acct_month}' between to_char(b.effective_date, 'YYYYMM') and to_char(b.expiry_date, 'YYYYMM') and b.ec_code = a.customer_num and b.order_id = a.prodist_sku_num);

    --code 02
    update bboss.stl_mem_interests a set status = '02' where acct_month = '${acct_month}' and status is null and not exists (select * from ecgroup1.sur_addition_member_subscriber b where '${acct_month}' between to_char(b.effective_date, 'YYYYMM') and to_char(b.expiry_date, 'YYYYMM') and b.subscriber_id = a.prodist_sku_num and b.member_number = a.member_num);
 
    --code 00
   update bboss.stl_mem_interests a set status = '00', member_type = (select b.field_value from ecgroup1.sur_addition_subscriber b where '${acct_month}' between to_char(b.effective_date, 'YYYYMM') and to_char(b.expiry_date, 'YYYYMM') and b.subscriber_id = a.prodist_sku_num) where acct_month = '${acct_month}' and status is null;
 
    --code 99
    update bboss.stl_mem_interests a set status = '99', err_msg = '无法查询到个付/统付信息！' where acct_month = '${acct_month}' and status is null; 
    
    commit;

END`

    f_Print "Oracle Messages :[ $QResult ]"
    
    if [[  ! -z $QResult && -z ${QResult##*ORA-*}  && ! -z ${QResult##*ORA-28002*} && ! -z ${QResult##*ORA-28011*}   ]]
    then
        f_Print "Error: f_validation failed - Oracle Error "
        f_Print "$QResult"
        exit
    fi

    f_Print "f_validation finishes"
    
}

function f_get_response
{
    f_Print "f_get_response starts"
    
    QResult=`sqlplus  -s ${a_db_str} << END
    whenever sqlerror exit sql.sqlcode       
    
    set head off
    set feed off  
    set echo off
    set termout off
    set trimout off
    set linesize 500
    
    spool ${response_file};
    select file_name || ':DR:' || row_no || '|' || status || '|' || err_msg
      from bboss.stl_mem_interests
     order by file_name, to_number(row_no);

    spool off;

END`
   
    f_Print "Oracle Messages :[ $QResult ]"
    
    if [[  ! -z $QResult && -z ${QResult##*ORA-*}  && ! -z ${QResult##*ORA-28002*} && ! -z ${QResult##*ORA-28011*}   ]]
    then
        f_Print "Error: f_get_response failed - Oracle Error "
        f_Print "$QResult"
        exit
    fi

    f_Print "f_get_response finishes"
}

function f_get_response_file 
{
    f_Print "f_get_response_file starts"

    ##Remove empty lines and spaces
    sed '/^  *$/d' ${response_file} | sed '/^ *$/d' | sed 's/ //g' > ${response_file}.tmp
    mv ${response_file}.tmp ${response_file}

    ##Create file-level response files
    for filename in `awk -F ":DR:" '{print $1}' ${response_file} | sort | uniq`
    do
        f_Print "f_get_response_file - ${filename} - generating response file"
        echo "filename=$filename"
        grep "^$filename"  ${response_file} | awk -F ":DR:" '{print $2}'   >> ${WORK_PATH}/Resp_${filename}
        mv ${WORK_PATH}/Resp_${filename} ${RESP_PATH}/Resp_${filename}
        f_Print "f_get_response_file - ${filename} - response file created - ${RESP_PATH}/Resp_${filename}"
    done

    rm ${response_file}

    f_Print "f_get_response_file finishes"

}

f_Print "*****************************************************"
f_Print "            txt2db_Membership.sh starts              "
f_Print "*****************************************************"

##Reading input parameter
acct_month=$1
f_Print "acct_month=$acct_month"

##initializing configuration file
f_init_config 
if (( $? != 0)); then
   f_Print "f_init_config failure. Exit now."
fi    

##Creating backup data path
mkdir -p ${BACKUP_PATH}/${acct_month}
f_Print "backup data path ${BACKUP_PATH}/${acct_month} created"

##Check input dir, exit when no input
input_file_no=`find ${DATA_PATH} -type f -name "${naming_convention}" | wc -l`
echo input_file_no=$input_file_no
if (( input_file_no < 1 )); then
    f_Print "No input file found under dir ${DATA_PATH}. Exit now."
    exit
else
    f_Print "No of input file found under dir ${DATA_PATH}: ${input_file_no}"
fi

##Deleting data table before loading data files into DB
f_delete_table
if (( $? != 0)); then
   f_Print "f_delete_table failed. Exit now." 
fi

##Validating field number and field lengths
f_valid_file
if (( $? != 0)); then
   f_Print "f_valid_file failure. Exit now."
fi

##Loading data into DB
f_load_data
if (( $? != 0)); then
   f_Print "f_load_data failure. Exit now." 
fi

f_valid_data
if (( $? != 0)); then
   f_Print "f_valid_data failure. Exit now." 
fi

f_get_response
if (( $? != 0)); then
   f_Print "f_get_response failure. Exit now."
fi

f_get_response_file
if (( $? != 0)); then
   f_Print "f_get_response_file failure. Exit now." 
fi

f_Print "*****************************************************"
f_Print "                txt2db_MembershipInterests.sh finishes                   "
f_Print "*****************************************************"
f_Print ""
f_Print ""
